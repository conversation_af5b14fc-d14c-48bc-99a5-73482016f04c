/**
 * Category Database Model
 * Mongoose schema for category data persistence
 */

const mongoose = require('mongoose');
const { VALIDATION } = require('../../../shared/constants');

const categorySchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: VALIDATION.NAME_MAX_LENGTH
  },
  slug: {
    type: String,
    required: true,
    unique: true,
    lowercase: true,
    trim: true,
    match: /^[a-z0-9-]+$/
  },
  description: {
    type: String,
    maxlength: VALIDATION.DESCRIPTION_MAX_LENGTH
  },
  shortDescription: {
    type: String,
    maxlength: 200
  },
  parentId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    default: null
  },
  level: {
    type: Number,
    default: 0,
    min: 0,
    max: 5
  },
  order: {
    type: Number,
    default: 0
  },
  image: {
    url: String,
    alt: String
  },
  icon: {
    type: String,
    maxlength: 100
  },
  color: {
    type: String,
    match: /^#[0-9A-F]{6}$/i
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isFeatured: {
    type: Boolean,
    default: false
  },
  showInMenu: {
    type: Boolean,
    default: true
  },
  showInHomepage: {
    type: Boolean,
    default: false
  },
  seoTitle: {
    type: String,
    maxlength: 60
  },
  seoDescription: {
    type: String,
    maxlength: 160
  },
  seoKeywords: [{
    type: String,
    trim: true
  }],
  metadata: {
    productCount: {
      type: Number,
      default: 0,
      min: 0
    },
    childrenCount: {
      type: Number,
      default: 0,
      min: 0
    },
    totalProductCount: {
      type: Number,
      default: 0,
      min: 0
    },
    lastProductAddedAt: Date,
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
categorySchema.index({ slug: 1 });
categorySchema.index({ parentId: 1, order: 1 });
categorySchema.index({ isActive: 1, showInMenu: 1 });
categorySchema.index({ isFeatured: 1, isActive: 1 });
categorySchema.index({ level: 1 });
categorySchema.index({ name: 'text', description: 'text' });

// Virtual for children categories
categorySchema.virtual('children', {
  ref: 'Category',
  localField: '_id',
  foreignField: 'parentId'
});

// Virtual for parent category
categorySchema.virtual('parent', {
  ref: 'Category',
  localField: 'parentId',
  foreignField: '_id',
  justOne: true
});

// Virtual for products in this category
categorySchema.virtual('products', {
  ref: 'Product',
  localField: '_id',
  foreignField: 'categoryId'
});

// Virtual for full path
categorySchema.virtual('fullPath').get(function() {
  // This will be populated by the repository when needed
  return this._fullPath || [];
});

// Virtual for breadcrumb
categorySchema.virtual('breadcrumb').get(function() {
  return this.fullPath.map(cat => ({
    id: cat._id,
    name: cat.name,
    slug: cat.slug
  }));
});

// Virtual for is root category
categorySchema.virtual('isRoot').get(function() {
  return !this.parentId;
});

// Virtual for has children
categorySchema.virtual('hasChildren').get(function() {
  return this.metadata.childrenCount > 0;
});

// Virtual for has products
categorySchema.virtual('hasProducts').get(function() {
  return this.metadata.productCount > 0;
});

// Virtual for URL
categorySchema.virtual('url').get(function() {
  return `/category/${this.slug}`;
});

// Pre-save middleware
categorySchema.pre('save', async function(next) {
  // Generate slug if not provided
  if (!this.slug && this.name) {
    this.slug = this.generateSlug(this.name);
  }

  // Set level based on parent
  if (this.parentId) {
    try {
      const parent = await this.constructor.findById(this.parentId);
      if (parent) {
        this.level = parent.level + 1;
      }
    } catch (error) {
      console.error('Error setting category level:', error);
    }
  } else {
    this.level = 0;
  }

  // Validate level depth
  if (this.level > 5) {
    return next(new Error('Category hierarchy cannot exceed 5 levels'));
  }

  next();
});

// Post-save middleware
categorySchema.post('save', async function(doc) {
  // Update parent's children count
  if (doc.parentId) {
    try {
      await this.constructor.updateChildrenCount(doc.parentId);
    } catch (error) {
      console.error('Error updating parent children count:', error);
    }
  }
});

// Pre-remove middleware
categorySchema.pre('remove', async function(next) {
  // Check if category has children
  const childrenCount = await this.constructor.countDocuments({ parentId: this._id });
  if (childrenCount > 0) {
    return next(new Error('Cannot delete category with children'));
  }

  // Check if category has products
  const Product = mongoose.model('Product');
  const productCount = await Product.countDocuments({ categoryId: this._id });
  if (productCount > 0) {
    return next(new Error('Cannot delete category with products'));
  }

  next();
});

// Post-remove middleware
categorySchema.post('remove', async function(doc) {
  // Update parent's children count
  if (doc.parentId) {
    try {
      await this.constructor.updateChildrenCount(doc.parentId);
    } catch (error) {
      console.error('Error updating parent children count after deletion:', error);
    }
  }
});

// Instance methods
categorySchema.methods.generateSlug = function(name) {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim('-');
};

categorySchema.methods.getFullPath = async function() {
  const path = [];
  let current = this;

  while (current) {
    path.unshift({
      _id: current._id,
      name: current.name,
      slug: current.slug,
      level: current.level
    });

    if (current.parentId) {
      current = await this.constructor.findById(current.parentId);
    } else {
      current = null;
    }
  }

  return path;
};

categorySchema.methods.getChildren = async function(recursive = false) {
  if (!recursive) {
    return await this.constructor.find({ parentId: this._id }).sort({ order: 1 });
  }

  // Get all descendants recursively
  const descendants = [];
  const queue = [this._id];

  while (queue.length > 0) {
    const parentId = queue.shift();
    const children = await this.constructor.find({ parentId }).sort({ order: 1 });
    
    for (const child of children) {
      descendants.push(child);
      queue.push(child._id);
    }
  }

  return descendants;
};

categorySchema.methods.updateProductCount = async function() {
  const Product = mongoose.model('Product');
  const count = await Product.countDocuments({ 
    categoryId: this._id,
    status: 'active'
  });
  
  this.metadata.productCount = count;
  await this.save();
  
  return count;
};

categorySchema.methods.canBeDeleted = async function() {
  // Check children
  const childrenCount = await this.constructor.countDocuments({ parentId: this._id });
  if (childrenCount > 0) {
    return { canDelete: false, reason: 'Category has child categories' };
  }

  // Check products
  const Product = mongoose.model('Product');
  const productCount = await Product.countDocuments({ categoryId: this._id });
  if (productCount > 0) {
    return { canDelete: false, reason: 'Category has products' };
  }

  return { canDelete: true };
};

// Static methods
categorySchema.statics.findActive = function(options = {}) {
  return this.find({ isActive: true }, null, options);
};

categorySchema.statics.findFeatured = function(limit = 10) {
  return this.find({ 
    isFeatured: true, 
    isActive: true 
  }).limit(limit).sort({ order: 1 });
};

categorySchema.statics.findRootCategories = function(options = {}) {
  return this.find({ 
    parentId: null, 
    isActive: true 
  }, null, { sort: { order: 1 }, ...options });
};

categorySchema.statics.findByLevel = function(level, options = {}) {
  return this.find({ level, isActive: true }, null, options);
};

categorySchema.statics.buildTree = async function(parentId = null, maxLevel = 5) {
  const categories = await this.find({ 
    parentId, 
    isActive: true 
  }).sort({ order: 1 });

  const tree = [];
  
  for (const category of categories) {
    const categoryObj = category.toObject();
    
    if (category.level < maxLevel) {
      categoryObj.children = await this.buildTree(category._id, maxLevel);
    } else {
      categoryObj.children = [];
    }
    
    tree.push(categoryObj);
  }

  return tree;
};

categorySchema.statics.updateChildrenCount = async function(parentId) {
  const count = await this.countDocuments({ parentId });
  await this.findByIdAndUpdate(parentId, {
    'metadata.childrenCount': count
  });
  return count;
};

categorySchema.statics.updateAllProductCounts = async function() {
  const Product = mongoose.model('Product');
  const categories = await this.find({});
  
  for (const category of categories) {
    const productCount = await Product.countDocuments({
      categoryId: category._id,
      status: 'active'
    });
    
    await this.findByIdAndUpdate(category._id, {
      'metadata.productCount': productCount
    });
  }
};

categorySchema.statics.reorderCategories = async function(orderData) {
  const bulkOps = orderData.map(item => ({
    updateOne: {
      filter: { _id: item.id },
      update: { order: item.order }
    }
  }));

  return await this.bulkWrite(bulkOps);
};

categorySchema.statics.findWithProductCounts = async function() {
  return await this.aggregate([
    {
      $lookup: {
        from: 'products',
        localField: '_id',
        foreignField: 'categoryId',
        as: 'products'
      }
    },
    {
      $addFields: {
        productCount: { $size: '$products' }
      }
    },
    {
      $project: {
        products: 0
      }
    },
    {
      $sort: { order: 1 }
    }
  ]);
};

module.exports = mongoose.model('Category', categorySchema);
