/**
 * Banner Controller
 * Handles banner management for homepage
 */

const { HttpStatus } = require('../../shared/enums');

class BannerController {
  constructor(container) {
    this.container = container;
  }

  /**
   * Get active banners
   */
  async getActiveBanners(req, res) {
    try {
      // Mock active banners data
      const banners = {
        success: true,
        data: [
          {
            id: '1',
            title: '<PERSON><PERSON>',
            description: 'En yeni ürünlerimizi keşfedin',
            image: 'https://via.placeholder.com/800x300/4F46E5/FFFFFF?text=Ye<PERSON>+<PERSON>rünler',
            link: '/products?filter=new',
            type: 'product',
            isActive: true,
            order: 1,
            createdAt: new Date().toISOString()
          },
          {
            id: '2',
            title: 'Açık Artırmalar',
            description: 'Heyecan verici açık artırmalara katılın',
            image: 'https://via.placeholder.com/800x300/059669/FFFFFF?text=Açık+Artırmalar',
            link: '/auctions',
            type: 'auction',
            isActive: true,
            order: 2,
            createdAt: new Date().toISOString()
          },
          {
            id: '3',
            title: 'Çekilişler',
            description: 'Şanslı çekilişlerde yerinizi alın',
            image: 'https://via.placeholder.com/800x300/DC2626/FFFFFF?text=Çekilişler',
            link: '/lottery',
            type: 'lottery',
            isActive: true,
            order: 3,
            createdAt: new Date().toISOString()
          }
        ],
        pagination: {
          page: 1,
          limit: 10,
          total: 3,
          pages: 1
        },
        timestamp: new Date().toISOString()
      };

      res.status(HttpStatus.OK).json(banners);
    } catch (error) {
      console.error('Get active banners error:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: 'Failed to get active banners',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Get all banners (admin)
   */
  async getAllBanners(req, res) {
    try {
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;

      // Mock all banners data
      const banners = {
        success: true,
        data: [
          {
            id: '1',
            title: 'Yeni Ürünler',
            description: 'En yeni ürünlerimizi keşfedin',
            image: 'https://via.placeholder.com/800x300/4F46E5/FFFFFF?text=Yeni+Ürünler',
            link: '/products?filter=new',
            type: 'product',
            isActive: true,
            order: 1,
            createdAt: new Date().toISOString()
          },
          {
            id: '2',
            title: 'Açık Artırmalar',
            description: 'Heyecan verici açık artırmalara katılın',
            image: 'https://via.placeholder.com/800x300/059669/FFFFFF?text=Açık+Artırmalar',
            link: '/auctions',
            type: 'auction',
            isActive: true,
            order: 2,
            createdAt: new Date().toISOString()
          },
          {
            id: '3',
            title: 'Çekilişler',
            description: 'Şanslı çekilişlerde yerinizi alın',
            image: 'https://via.placeholder.com/800x300/DC2626/FFFFFF?text=Çekilişler',
            link: '/lottery',
            type: 'lottery',
            isActive: true,
            order: 3,
            createdAt: new Date().toISOString()
          },
          {
            id: '4',
            title: 'Takas Sistemi',
            description: 'Ürünlerinizi takas edin',
            image: 'https://via.placeholder.com/800x300/7C3AED/FFFFFF?text=Takas+Sistemi',
            link: '/exchange',
            type: 'exchange',
            isActive: false,
            order: 4,
            createdAt: new Date().toISOString()
          }
        ],
        pagination: {
          page,
          limit,
          total: 4,
          pages: 1
        },
        timestamp: new Date().toISOString()
      };

      res.status(HttpStatus.OK).json(banners);
    } catch (error) {
      console.error('Get all banners error:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: 'Failed to get banners',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Create new banner
   */
  async createBanner(req, res) {
    try {
      const bannerData = req.body;

      const newBanner = {
        id: Date.now().toString(),
        ...bannerData,
        isActive: bannerData.isActive !== undefined ? bannerData.isActive : true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      res.status(HttpStatus.CREATED).json({
        success: true,
        data: newBanner,
        message: 'Banner created successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Create banner error:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: 'Failed to create banner',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Update banner
   */
  async updateBanner(req, res) {
    try {
      const { id } = req.params;
      const updateData = req.body;

      const updatedBanner = {
        id,
        ...updateData,
        updatedAt: new Date().toISOString()
      };

      res.status(HttpStatus.OK).json({
        success: true,
        data: updatedBanner,
        message: 'Banner updated successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Update banner error:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: 'Failed to update banner',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Delete banner
   */
  async deleteBanner(req, res) {
    try {
      const { id } = req.params;

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'Banner deleted successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Delete banner error:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: 'Failed to delete banner',
        timestamp: new Date().toISOString()
      });
    }
  }
}

module.exports = BannerController;
