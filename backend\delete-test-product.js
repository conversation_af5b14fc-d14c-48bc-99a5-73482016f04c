const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:5000/api';
let adminToken = '';

async function deleteTestProduct() {
  console.log('🗑️ Deleting test product...\n');

  try {
    // 1. Login to get admin token
    console.log('1️⃣ Logging in as admin...');
    const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'eEf8kbpE9G8XhiL'
      })
    });

    const loginData = await loginResponse.json();
    adminToken = loginData.data.token;
    console.log('✅ Admin login successful\n');

    // 2. Get all products to find test product
    console.log('2️⃣ Finding test product...');
    const listResponse = await fetch(`${BASE_URL}/products`);
    const listData = await listResponse.json();
    const products = listData.data.products;
    
    console.log(`📦 Found ${products.length} products:`);
    products.forEach((product, index) => {
      console.log(`  ${index + 1}. ${product.name} - ID: ${product.id} - Price: ${product.price}₺`);
    });
    console.log('');

    // 3. Find and delete test product
    const testProduct = products.find(p => p.name.includes('Simple Test Product'));
    
    if (testProduct && testProduct.id) {
      console.log('3️⃣ Deleting test product...');
      console.log(`   Product: ${testProduct.name}`);
      console.log(`   ID: ${testProduct.id}`);
      console.log(`   Price: ${testProduct.price}₺`);
      
      const deleteResponse = await fetch(`${BASE_URL}/products/${testProduct.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${adminToken}`,
          'Content-Type': 'application/json'
        }
      });

      const deleteData = await deleteResponse.json();
      
      if (deleteResponse.ok) {
        console.log('✅ Test product deleted successfully!');
      } else {
        console.log('❌ Failed to delete test product:', deleteData.message);
      }
    } else {
      console.log('ℹ️ Test product not found (already deleted or doesn\'t exist)');
    }

    // 4. Verify deletion by listing products again
    console.log('\n4️⃣ Verifying deletion...');
    const finalListResponse = await fetch(`${BASE_URL}/products`);
    const finalListData = await finalListResponse.json();
    const finalProducts = finalListData.data.products;
    
    console.log(`📦 Final product count: ${finalProducts.length}`);
    finalProducts.forEach((product, index) => {
      console.log(`  ${index + 1}. ${product.name} - Price: ${product.price}₺`);
    });

    const stillExists = finalProducts.find(p => p.name.includes('Simple Test Product'));
    if (!stillExists) {
      console.log('\n✅ Test product successfully removed from database!');
    } else {
      console.log('\n❌ Test product still exists in database');
    }

    console.log('\n🎉 Cleanup completed!');

  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error('Stack:', error.stack);
  }
}

deleteTestProduct();
