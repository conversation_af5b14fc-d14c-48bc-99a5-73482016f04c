/**
 * Product Routes Configuration
 * Defines all product-related API endpoints
 */

const express = require('express');
const ErrorMiddleware = require('../middleware/ErrorMiddleware');

class ProductRoutes {
  constructor(container) {
    this.container = container;
    this.router = express.Router();
    this.setupRoutes();
  }

  setupRoutes() {
    const productController = this.container.resolve('productController');
    const authMiddleware = this.container.resolve('authMiddleware');

    // Public routes (no authentication required)

    // Get all products with filtering and pagination
    this.router.get('/',
      ErrorMiddleware.asyncHandler(productController.getAllProducts.bind(productController))
    );

    // Get featured products (must be before /:id route)
    this.router.get('/featured',
      ErrorMiddleware.asyncHandler(productController.getFeaturedProducts.bind(productController))
    );

    // Get products by category
    this.router.get('/category/:categoryId',
      ErrorMiddleware.asyncHandler(productController.getProductsByCategory.bind(productController))
    );

    // Get product by ID (must be after specific routes)
    this.router.get('/:id',
      ErrorMiddleware.asyncHandler(productController.getProductById.bind(productController))
    );

    // Get related products
    this.router.get('/:id/related',
      ErrorMiddleware.asyncHandler(productController.getRelatedProducts.bind(productController))
    );

    // Get product statistics (public for transparency)
    this.router.get('/:id/statistics',
      ErrorMiddleware.asyncHandler(productController.getProductStatistics.bind(productController))
    );

    // Increment view count (can be public)
    this.router.post('/:id/view',
      ErrorMiddleware.asyncHandler(productController.incrementViewCount.bind(productController))
    );

    // Check product availability (public)
    this.router.get('/:id/availability',
      ErrorMiddleware.asyncHandler(productController.checkAvailability.bind(productController))
    );

    // Search products
    this.router.get('/search/query',
      ErrorMiddleware.asyncHandler(productController.searchProducts.bind(productController))
    );

    // Protected routes (authentication required)

    // Create new product (sellers and admins only)
    this.router.post('/',
      authMiddleware.authenticate(),
      authMiddleware.requireSeller(),
      ErrorMiddleware.asyncHandler(productController.createProduct.bind(productController))
    );

    // Update product (owner or admin only)
    this.router.put('/:id',
      authMiddleware.authenticate(),
      ErrorMiddleware.asyncHandler(productController.updateProduct.bind(productController))
    );

    // Delete product (owner or admin only)
    this.router.delete('/:id',
      authMiddleware.authenticate(),
      ErrorMiddleware.asyncHandler(productController.deleteProduct.bind(productController))
    );

    // Update product inventory (owner or admin only)
    this.router.put('/:id/inventory',
      authMiddleware.authenticate(),
      ErrorMiddleware.asyncHandler(productController.updateInventory.bind(productController))
    );

    // Get user's own products (seller dashboard)
    this.router.get('/user/my-products',
      authMiddleware.authenticate(),
      authMiddleware.requireSeller(),
      ErrorMiddleware.asyncHandler(productController.getUserProducts.bind(productController))
    );

    // Admin routes (admin only)

    // Approve product
    this.router.post('/:id/approve',
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(productController.approveProduct.bind(productController))
    );

    // Reject product
    this.router.post('/:id/reject',
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(productController.rejectProduct.bind(productController))
    );

    // Update product status
    this.router.put('/:id/status',
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(productController.updateProductStatus.bind(productController))
    );

    // Get product counts by status
    this.router.get('/admin/counts',
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(productController.getProductCounts.bind(productController))
    );

    // Get low stock products
    this.router.get('/admin/low-stock',
      authMiddleware.authenticate(),
      authMiddleware.requireRoles(['admin', 'seller']),
      ErrorMiddleware.asyncHandler(productController.getLowStockProducts.bind(productController))
    );

    // Bulk update products
    this.router.post('/admin/bulk-update',
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(productController.bulkUpdateProducts.bind(productController))
    );
  }

  getRouter() {
    return this.router;
  }
}

module.exports = ProductRoutes;
