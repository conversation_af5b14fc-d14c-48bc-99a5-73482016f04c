/**
 * Product Service Unit Tests
 */

const ProductService = require('../../../src/business/services/ProductService');
const { BusinessException, ValidationException, AuthorizationException } = require('../../../src/core/exceptions');
const { ProductStatus, UserRole } = require('../../../src/shared/enums');

describe('ProductService', () => {
  let productService;
  let mockProductRepository;
  let mockUserRepository;
  let mockCategoryRepository;
  let mockEmailService;

  beforeEach(() => {
    // Mock dependencies
    mockProductRepository = {
      findById: jest.fn(),
      findAll: jest.fn(),
      findByCategory: jest.fn(),
      findBySeller: jest.fn(),
      findByStatus: jest.fn(),
      findFeatured: jest.fn(),
      findRelated: jest.fn(),
      search: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      updateStatus: jest.fn(),
      updateInventory: jest.fn(),
      incrementViewCount: jest.fn(),
      getStatistics: jest.fn(),
      getLowStockProducts: jest.fn(),
      bulkUpdate: jest.fn()
    };

    mockUserRepository = {
      findById: jest.fn()
    };

    mockCategoryRepository = {
      findById: jest.fn()
    };

    mockEmailService = {
      sendNotificationEmail: jest.fn(),
      sendProductApprovalEmail: jest.fn(),
      sendProductRejectionEmail: jest.fn()
    };

    productService = new ProductService(
      mockProductRepository,
      mockUserRepository,
      mockCategoryRepository,
      mockEmailService
    );
  });

  describe('createProduct', () => {
    it('should create product successfully for seller', async () => {
      // Arrange
      const productData = testUtils.createTestProduct();
      const sellerId = '123';
      const seller = testUtils.createTestSeller({ id: sellerId });
      const category = testUtils.createTestCategory({ id: productData.categoryId });
      const savedProduct = { ...productData, id: '456', sellerId };

      mockUserRepository.findById.mockResolvedValue(seller);
      mockCategoryRepository.findById.mockResolvedValue(category);
      mockProductRepository.create.mockResolvedValue(savedProduct);

      // Act
      const result = await productService.createProduct(productData, sellerId);

      // Assert
      expect(mockUserRepository.findById).toHaveBeenCalledWith(sellerId);
      expect(mockProductRepository.create).toHaveBeenCalled();
      expect(result).toEqual(savedProduct);
    });

    it('should throw AuthorizationException if user cannot sell', async () => {
      // Arrange
      const productData = testUtils.createTestProduct();
      const userId = '123';
      const user = testUtils.createTestUser({ id: userId, permissions: { canSell: false } });

      mockUserRepository.findById.mockResolvedValue(user);

      // Act & Assert
      await expect(productService.createProduct(productData, userId)).rejects.toThrow(AuthorizationException);
      expect(mockProductRepository.create).not.toHaveBeenCalled();
    });

    it('should throw ValidationException if category not found', async () => {
      // Arrange
      const productData = testUtils.createTestProduct({ categoryId: 'invalid' });
      const sellerId = '123';
      const seller = testUtils.createTestSeller({ id: sellerId });

      mockUserRepository.findById.mockResolvedValue(seller);
      mockCategoryRepository.findById.mockResolvedValue(null);

      // Act & Assert
      await expect(productService.createProduct(productData, sellerId)).rejects.toThrow(ValidationException);
      expect(mockProductRepository.create).not.toHaveBeenCalled();
    });
  });

  describe('updateProduct', () => {
    it('should update product successfully by owner', async () => {
      // Arrange
      const productId = '456';
      const updateData = { name: 'Updated Product' };
      const sellerId = '123';
      const product = testUtils.createTestProduct({ id: productId, sellerId });
      const seller = testUtils.createTestSeller({ id: sellerId });
      const updatedProduct = { ...product, ...updateData };

      mockProductRepository.findById.mockResolvedValue(product);
      mockUserRepository.findById.mockResolvedValue(seller);
      mockProductRepository.update.mockResolvedValue(updatedProduct);

      // Act
      const result = await productService.updateProduct(productId, updateData, sellerId);

      // Assert
      expect(mockProductRepository.findById).toHaveBeenCalledWith(productId);
      expect(mockProductRepository.update).toHaveBeenCalledWith(productId, expect.objectContaining(updateData));
      expect(result).toEqual(updatedProduct);
    });

    it('should allow admin to update any product', async () => {
      // Arrange
      const productId = '456';
      const updateData = { name: 'Updated Product' };
      const adminId = '789';
      const sellerId = '123';
      const product = testUtils.createTestProduct({ id: productId, sellerId });
      const admin = testUtils.createTestAdmin({ id: adminId });
      const updatedProduct = { ...product, ...updateData };

      mockProductRepository.findById.mockResolvedValue(product);
      mockUserRepository.findById.mockResolvedValue(admin);
      mockProductRepository.update.mockResolvedValue(updatedProduct);

      // Act
      const result = await productService.updateProduct(productId, updateData, adminId);

      // Assert
      expect(result).toEqual(updatedProduct);
    });

    it('should throw AuthorizationException if non-owner tries to update', async () => {
      // Arrange
      const productId = '456';
      const updateData = { name: 'Updated Product' };
      const otherUserId = '789';
      const sellerId = '123';
      const product = testUtils.createTestProduct({ id: productId, sellerId });
      const otherUser = testUtils.createTestUser({ id: otherUserId, role: UserRole.USER });

      mockProductRepository.findById.mockResolvedValue(product);
      mockUserRepository.findById.mockResolvedValue(otherUser);

      // Act & Assert
      await expect(productService.updateProduct(productId, updateData, otherUserId)).rejects.toThrow(AuthorizationException);
      expect(mockProductRepository.update).not.toHaveBeenCalled();
    });
  });

  describe('deleteProduct', () => {
    it('should delete product successfully by owner', async () => {
      // Arrange
      const productId = '456';
      const sellerId = '123';
      const product = testUtils.createTestProduct({ id: productId, sellerId });
      const seller = testUtils.createTestSeller({ id: sellerId });

      mockProductRepository.findById.mockResolvedValue(product);
      mockUserRepository.findById.mockResolvedValue(seller);
      mockProductRepository.delete.mockResolvedValue(true);

      // Act
      const result = await productService.deleteProduct(productId, sellerId);

      // Assert
      expect(mockProductRepository.findById).toHaveBeenCalledWith(productId);
      expect(mockProductRepository.delete).toHaveBeenCalledWith(productId);
      expect(result).toBe(true);
    });

    it('should allow admin to delete any product', async () => {
      // Arrange
      const productId = '456';
      const adminId = '789';
      const sellerId = '123';
      const product = testUtils.createTestProduct({ id: productId, sellerId });
      const admin = testUtils.createTestAdmin({ id: adminId });

      mockProductRepository.findById.mockResolvedValue(product);
      mockUserRepository.findById.mockResolvedValue(admin);
      mockProductRepository.delete.mockResolvedValue(true);

      // Act
      const result = await productService.deleteProduct(productId, adminId);

      // Assert
      expect(result).toBe(true);
    });
  });

  describe('approveProduct', () => {
    it('should approve product successfully by admin', async () => {
      // Arrange
      const productId = '456';
      const adminId = '789';
      const sellerId = '123';
      const product = testUtils.createTestProduct({ 
        id: productId, 
        sellerId, 
        status: ProductStatus.PENDING 
      });
      const admin = testUtils.createTestAdmin({ id: adminId });
      const seller = testUtils.createTestSeller({ id: sellerId });
      const approvedProduct = { ...product, status: ProductStatus.ACTIVE };

      mockProductRepository.findById.mockResolvedValue(product);
      mockUserRepository.findById
        .mockResolvedValueOnce(admin)
        .mockResolvedValueOnce(seller);
      mockProductRepository.updateStatus.mockResolvedValue(approvedProduct);

      // Act
      const result = await productService.approveProduct(productId, adminId);

      // Assert
      expect(mockProductRepository.updateStatus).toHaveBeenCalledWith(productId, ProductStatus.ACTIVE);
      expect(mockEmailService.sendProductApprovalEmail).toHaveBeenCalled();
      expect(result).toEqual(approvedProduct);
    });

    it('should throw AuthorizationException if non-admin tries to approve', async () => {
      // Arrange
      const productId = '456';
      const userId = '123';
      const product = testUtils.createTestProduct({ id: productId });
      const user = testUtils.createTestUser({ id: userId, role: UserRole.USER });

      mockProductRepository.findById.mockResolvedValue(product);
      mockUserRepository.findById.mockResolvedValue(user);

      // Act & Assert
      await expect(productService.approveProduct(productId, userId)).rejects.toThrow(AuthorizationException);
      expect(mockProductRepository.updateStatus).not.toHaveBeenCalled();
    });
  });

  describe('getProductById', () => {
    it('should return product by ID', async () => {
      // Arrange
      const productId = '456';
      const product = testUtils.createTestProduct({ id: productId });

      mockProductRepository.findById.mockResolvedValue(product);

      // Act
      const result = await productService.getProductById(productId);

      // Assert
      expect(mockProductRepository.findById).toHaveBeenCalledWith(productId);
      expect(result).toEqual(product);
    });

    it('should throw ValidationException if product not found', async () => {
      // Arrange
      const productId = 'nonexistent';

      mockProductRepository.findById.mockResolvedValue(null);

      // Act & Assert
      await expect(productService.getProductById(productId)).rejects.toThrow(ValidationException);
    });
  });

  describe('getAllProducts', () => {
    it('should return paginated products', async () => {
      // Arrange
      const options = { page: 1, limit: 10 };
      const products = [
        testUtils.createTestProduct({ id: '1' }),
        testUtils.createTestProduct({ id: '2' })
      ];
      const result = {
        products,
        pagination: { page: 1, limit: 10, total: 2, pages: 1 }
      };

      mockProductRepository.findAll.mockResolvedValue(result);

      // Act
      const response = await productService.getAllProducts(options);

      // Assert
      expect(mockProductRepository.findAll).toHaveBeenCalledWith(options);
      expect(response).toEqual(result);
    });
  });

  describe('searchProducts', () => {
    it('should search products successfully', async () => {
      // Arrange
      const query = 'test product';
      const options = { page: 1, limit: 10 };
      const products = [testUtils.createTestProduct({ name: 'Test Product' })];
      const result = {
        products,
        pagination: { page: 1, limit: 10, total: 1, pages: 1 }
      };

      mockProductRepository.search.mockResolvedValue(result);

      // Act
      const response = await productService.searchProducts(query, options);

      // Assert
      expect(mockProductRepository.search).toHaveBeenCalledWith(query, options);
      expect(response).toEqual(result);
    });
  });

  describe('incrementViewCount', () => {
    it('should increment view count successfully', async () => {
      // Arrange
      const productId = '456';
      const product = testUtils.createTestProduct({ id: productId, viewCount: 5 });
      const updatedProduct = { ...product, viewCount: 6 };

      mockProductRepository.findById.mockResolvedValue(product);
      mockProductRepository.incrementViewCount.mockResolvedValue(updatedProduct);

      // Act
      const result = await productService.incrementViewCount(productId);

      // Assert
      expect(mockProductRepository.incrementViewCount).toHaveBeenCalledWith(productId);
      expect(result).toEqual(updatedProduct);
    });
  });
});
