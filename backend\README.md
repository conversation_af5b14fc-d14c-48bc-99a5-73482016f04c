# Robinhoot E-Ticaret API

Bu proje, Robinhoot e-ticaret uygulaması için Express.js tabanlı bir backend API'sidir. MongoDB veritabanı kullanır ve MVC mimarisi üzerine kurulmuştur.

## Özellikler

- Express.js tabanlı RESTful API
- MongoDB veritabanı bağlantısı
- MVC (Model-View-Controller) mimarisi
- JWT tabanlı kimlik doğrulama
- MongoDB migration sistemi (migrate-mongo)
- <PERSON><PERSON><PERSON><PERSON>, ka<PERSON><PERSON><PERSON>, sepet ve sipariş yönetimi
- Açık artırma sistemi
- Hata yönetimi ve loglama

## Kurulum

1. Gereksinimleri yükleyin:

```bash
npm install
```

2. `.env` dosyasını oluşturun:

```
NODE_ENV=development
PORT=5000
MONGODB_URI=mongodb+srv://cluster0.s3afsyr.mongodb.net/
MONGO_USER=emrevaryemez22
MONGO_PASSWORD=your_password_here
MONGO_DB_NAME=robinhoot
JWT_SECRET=your_jwt_secret_key_change_in_production
JWT_EXPIRES_IN=7d
CORS_ORIGIN=http://localhost:3000
UPSTASH_REDIS_REST_URL=...
UPSTASH_REDIS_REST_TOKEN=...
```

3. Veritabanı migration'larını çalıştırın:

```bash
npx migrate-mongo up
```

4. Sunucuyu başlatın:

```bash
npm run dev
```

## API Endpoint'leri

### Kimlik Doğrulama

- `POST /api/auth/register` - Kullanıcı kaydı
- `POST /api/auth/login` - Kullanıcı girişi
- `GET /api/auth/me` - Kullanıcı bilgilerini getir
- `POST /api/auth/forgot-password` - Şifre sıfırlama isteği
- `PUT /api/auth/reset-password/:token` - Şifre sıfırlama
- `GET /api/auth/verify-email/:token` - E-posta doğrulama

### Ürünler

- `GET /api/products` - Tüm ürünleri getir
- `GET /api/products/:id` - Tek bir ürünü getir
- `POST /api/products` - Yeni ürün oluştur (Admin)
- `PUT /api/products/:id` - Ürünü güncelle (Admin)
- `DELETE /api/products/:id` - Ürünü sil (Admin)
- `GET /api/products/featured` - Öne çıkan ürünleri getir

### Kategoriler

- `GET /api/categories` - Tüm kategorileri getir
- `GET /api/categories/:id` - Tek bir kategoriyi getir
- `POST /api/categories` - Yeni kategori oluştur (Admin)
- `PUT /api/categories/:id` - Kategoriyi güncelle (Admin)
- `DELETE /api/categories/:id` - Kategoriyi sil (Admin)
- `GET /api/categories/tree` - Kategori ağacını getir
- `GET /api/categories/featured` - Öne çıkan kategorileri getir

### Açık Artırmalar

- `GET /api/auctions` - Tüm açık artırmaları getir
- `GET /api/auctions/:id` - Tek bir açık artırmayı getir
- `POST /api/auctions` - Yeni açık artırma oluştur (Admin)
- `PUT /api/auctions/:id` - Açık artırmayı güncelle (Admin)
- `DELETE /api/auctions/:id` - Açık artırmayı sil (Admin)
- `GET /api/auctions/active` - Aktif açık artırmaları getir
- `POST /api/auctions/:id/bid` - Açık artırmaya teklif ver

### Sepet

- `GET /api/cart` - Kullanıcının sepetini getir
- `POST /api/cart` - Sepete ürün ekle
- `DELETE /api/cart` - Sepeti temizle
- `PUT /api/cart/:itemId` - Sepetteki ürün miktarını güncelle
- `DELETE /api/cart/:itemId` - Sepetten ürün çıkar
- `POST /api/cart/apply-coupon` - Kupon kodu uygula

### Siparişler

- `GET /api/orders` - Tüm siparişleri getir (Admin)
- `POST /api/orders` - Yeni sipariş oluştur
- `GET /api/orders/myorders` - Kullanıcının siparişlerini getir
- `GET /api/orders/:id` - Tek bir siparişi getir
- `PUT /api/orders/:id/status` - Sipariş durumunu güncelle (Admin)
- `PUT /api/orders/:id/payment` - Ödeme durumunu güncelle (Admin)

## Migration Sistemi

Veritabanı şemasını ve örnek verileri yönetmek için migrate-mongo kullanılmaktadır.

### Migration Dosyası Oluşturma

```bash
npx migrate-mongo create <migration-name>
```

### Migration'ları Çalıştırma

```bash
npx migrate-mongo up
```

### Migration'ları Geri Alma

```bash
npx migrate-mongo down
```

### Migration Durumunu Kontrol Etme

```bash
npx migrate-mongo status
```

## Lisans

Bu proje MIT lisansı altında lisanslanmıştır. 