/**
 * Auction Controller
 * Application layer - HTTP request handling for auction operations
 */

const { HttpStatus } = require('../../shared/enums');
const { PAGINATION } = require('../../shared/constants');

class AuctionController {
  constructor(auctionService) {
    this.auctionService = auctionService;
  }

  /**
   * Get all auctions
   */
  async getAllAuctions(req, res, next) {
    try {
      const {
        status,
        sortBy = 'endTime',
        sortOrder = 'asc',
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        minPrice,
        maxPrice,
        search
      } = req.query;

      const options = {
        page: parseInt(page),
        limit: Math.min(parseInt(limit), PAGINATION.MAX_LIMIT),
        sortBy,
        sortOrder,
        status,
        minPrice: minPrice ? parseFloat(minPrice) : undefined,
        maxPrice: maxPrice ? parseFloat(maxPrice) : undefined
      };

      // Debug: Check if auctionService and its repository are properly injected
      console.log('AuctionController - auctionService:', !!this.auctionService);
      console.log('AuctionController - auctionRepository:', !!this.auctionService?.auctionRepository);
      console.log('AuctionController - findAll method:', typeof this.auctionService?.auctionRepository?.findAll);

      let result;
      if (search) {
        result = await this.auctionService.searchAuctions(search, options);
      } else {
        result = await this.auctionService.getAllAuctions(options);
      }

      res.status(HttpStatus.OK).json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get auction by ID
   */
  async getAuctionById(req, res, next) {
    try {
      const { id } = req.params;
      const auction = await this.auctionService.getAuctionById(id);

      res.status(HttpStatus.OK).json({
        success: true,
        data: { auction }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Create new auction
   */
  async createAuction(req, res, next) {
    try {
      const auctionData = {
        ...req.body,
        sellerId: req.user.id
      };

      const auction = await this.auctionService.createAuction(auctionData, req.user.id);

      res.status(HttpStatus.CREATED).json({
        success: true,
        message: 'Auction created successfully',
        data: { auction }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update auction
   */
  async updateAuction(req, res, next) {
    try {
      const { id } = req.params;
      const auction = await this.auctionService.updateAuction(id, req.body, req.user.id);

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'Auction updated successfully',
        data: { auction }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete auction
   */
  async deleteAuction(req, res, next) {
    try {
      const { id } = req.params;
      await this.auctionService.deleteAuction(id, req.user.id);

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'Auction deleted successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Place bid on auction
   */
  async placeBid(req, res, next) {
    try {
      const { id } = req.params;
      const { amount } = req.body;

      if (!amount || amount <= 0) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          message: 'Valid bid amount is required'
        });
      }

      const auction = await this.auctionService.placeBid(
        id,
        req.user.id,
        parseFloat(amount),
        req.user.username || req.user.name
      );

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'Bid placed successfully',
        data: { auction }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get active auctions
   */
  async getActiveAuctions(req, res, next) {
    try {
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        sortBy = 'endTime',
        sortOrder = 'asc'
      } = req.query;

      const options = {
        page: parseInt(page),
        limit: Math.min(parseInt(limit), PAGINATION.MAX_LIMIT),
        sortBy,
        sortOrder
      };

      const result = await this.auctionService.getActiveAuctions(options);

      res.status(HttpStatus.OK).json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get user's auctions (seller)
   */
  async getUserAuctions(req, res, next) {
    try {
      const {
        status,
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = req.query;

      const options = {
        page: parseInt(page),
        limit: Math.min(parseInt(limit), PAGINATION.MAX_LIMIT),
        sortBy,
        sortOrder,
        status
      };

      const result = await this.auctionService.getUserAuctions(req.user.id, options);

      res.status(HttpStatus.OK).json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get user's bids
   */
  async getUserBids(req, res, next) {
    try {
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT
      } = req.query;

      const options = {
        page: parseInt(page),
        limit: Math.min(parseInt(limit), PAGINATION.MAX_LIMIT)
      };

      const result = await this.auctionService.getUserBids(req.user.id, options);

      res.status(HttpStatus.OK).json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get auction bids
   */
  async getAuctionBids(req, res, next) {
    try {
      const { id } = req.params;
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        sortBy = 'timestamp',
        sortOrder = 'desc'
      } = req.query;

      const options = {
        page: parseInt(page),
        limit: Math.min(parseInt(limit), PAGINATION.MAX_LIMIT),
        sortBy,
        sortOrder
      };

      const result = await this.auctionService.getAuctionBids(id, options);

      res.status(HttpStatus.OK).json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get ending soon auctions
   */
  async getEndingSoonAuctions(req, res, next) {
    try {
      const { hours = 24 } = req.query;
      const auctions = await this.auctionService.getEndingSoonAuctions(parseInt(hours));

      res.status(HttpStatus.OK).json({
        success: true,
        data: { auctions }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update auction status (Admin)
   */
  async updateAuctionStatus(req, res, next) {
    try {
      const { id } = req.params;
      const { status } = req.body;

      if (!status) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          message: 'Status is required'
        });
      }

      const auction = await this.auctionService.updateAuctionStatus(id, status, req.user.id);

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'Auction status updated successfully',
        data: { auction }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get auction statistics
   */
  async getAuctionStatistics(req, res, next) {
    try {
      const { id } = req.params;
      const statistics = await this.auctionService.getAuctionStatistics(id);

      res.status(HttpStatus.OK).json({
        success: true,
        data: { statistics }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Increment auction view count
   */
  async incrementViewCount(req, res, next) {
    try {
      const { id } = req.params;
      await this.auctionService.incrementViewCount(id);

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'View count updated'
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update auction statuses (System)
   */
  async updateAuctionStatuses(req, res, next) {
    try {
      const result = await this.auctionService.updateAuctionStatuses();

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'Auction statuses updated successfully',
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get auctions count by status (Admin)
   */
  async getAuctionCounts(req, res, next) {
    try {
      const { sellerId } = req.query;
      const counts = await this.auctionService.getAuctionCounts(sellerId);

      res.status(HttpStatus.OK).json({
        success: true,
        data: { counts }
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = AuctionController;
