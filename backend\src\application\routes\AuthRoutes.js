/**
 * Auth Routes Configuration
 * Defines all authentication related API endpoints
 */

const express = require('express');
const ErrorMiddleware = require('../middleware/ErrorMiddleware');

class AuthRoutes {
  constructor(container) {
    this.container = container;
    this.router = express.Router();
    this.setupRoutes();
  }

  setupRoutes() {
    const authController = new (require('../controllers/AuthController'))(this.container);
    const authMiddleware = this.container.resolve('authMiddleware');

    // Test route
    this.router.get('/test', (req, res) => {
      console.log('🧪 AuthRoutes test endpoint called!');
      res.json({ message: 'Auth routes working!', timestamp: new Date().toISOString() });
    });

    // Public authentication routes
    this.router.post('/login', (req, res, next) => {
      console.log('🔥 AuthRoutes /login called!', { email: req.body.email, hasPassword: !!req.body.password });
      return ErrorMiddleware.asyncHandler(authController.login.bind(authController))(req, res, next);
    });

    this.router.post('/register',
      ErrorMiddleware.asyncHandler(authController.register.bind(authController))
    );

    // Protected routes (require authentication)
    this.router.get('/profile',
      authMiddleware.authenticate(),
      ErrorMiddleware.asyncHandler(authController.getProfile.bind(authController))
    );

    this.router.put('/profile',
      authMiddleware.authenticate(),
      ErrorMiddleware.asyncHandler(authController.updateProfile.bind(authController))
    );

    this.router.post('/change-password',
      authMiddleware.authenticate(),
      ErrorMiddleware.asyncHandler(authController.changePassword.bind(authController))
    );

    this.router.post('/logout',
      authMiddleware.authenticate(),
      ErrorMiddleware.asyncHandler(authController.logout.bind(authController))
    );

    this.router.get('/verify',
      authMiddleware.authenticate(),
      ErrorMiddleware.asyncHandler(authController.verifyToken.bind(authController))
    );

    // Alternative routes for backward compatibility
    this.router.get('/me',
      authMiddleware.authenticate(),
      ErrorMiddleware.asyncHandler(authController.getProfile.bind(authController))
    );

    this.router.put('/me',
      authMiddleware.authenticate(),
      ErrorMiddleware.asyncHandler(authController.updateProfile.bind(authController))
    );
  }

  getRouter() {
    return this.router;
  }
}

module.exports = AuthRoutes;
