/**
 * Update User DTO
 * Data Transfer Object for user updates
 */

const { ValidationException } = require('../../../core/exceptions');
const { VALIDATION } = require('../../../shared/constants');
const { UserRole, UserStatus } = require('../../../shared/enums');

class UpdateUserDto {
  constructor(data) {
    this.name = data.name?.trim();
    this.email = data.email?.toLowerCase().trim();
    this.username = data.username?.toLowerCase().trim();
    this.role = data.role;
    this.status = data.status;
    this.isEmailVerified = data.isEmailVerified;
    this.phoneNumber = data.phoneNumber?.trim();
    this.address = data.address;
    this.avatar = data.avatar?.trim();

    this.validate();
  }

  validate() {
    const errors = [];

    // Name validation (optional)
    if (this.name !== undefined) {
      if (!this.name) {
        errors.push('Name cannot be empty');
      } else if (this.name.length < VALIDATION.NAME_MIN_LENGTH) {
        errors.push(`Name must be at least ${VALIDATION.NAME_MIN_LENGTH} characters`);
      } else if (this.name.length > VALIDATION.NAME_MAX_LENGTH) {
        errors.push(`Name cannot exceed ${VALIDATION.NAME_MAX_LENGTH} characters`);
      }
    }

    // Email validation (optional)
    if (this.email !== undefined) {
      if (!this.email) {
        errors.push('Email cannot be empty');
      } else if (!this.isValidEmail(this.email)) {
        errors.push('Invalid email format');
      }
    }

    // Username validation (optional)
    if (this.username !== undefined) {
      if (!this.username) {
        errors.push('Username cannot be empty');
      } else if (this.username.length < VALIDATION.USERNAME_MIN_LENGTH) {
        errors.push(`Username must be at least ${VALIDATION.USERNAME_MIN_LENGTH} characters`);
      } else if (this.username.length > VALIDATION.USERNAME_MAX_LENGTH) {
        errors.push(`Username cannot exceed ${VALIDATION.USERNAME_MAX_LENGTH} characters`);
      } else if (!this.isValidUsername(this.username)) {
        errors.push('Username can only contain letters, numbers, and underscores');
      }
    }

    // Role validation (optional)
    if (this.role !== undefined && !Object.values(UserRole).includes(this.role)) {
      errors.push('Invalid user role');
    }

    // Status validation (optional)
    if (this.status !== undefined && !Object.values(UserStatus).includes(this.status)) {
      errors.push('Invalid user status');
    }

    // Phone number validation (optional)
    if (this.phoneNumber !== undefined && this.phoneNumber && !this.isValidPhoneNumber(this.phoneNumber)) {
      errors.push('Invalid phone number format');
    }

    // Avatar URL validation (optional)
    if (this.avatar !== undefined && this.avatar && !this.isValidUrl(this.avatar)) {
      errors.push('Invalid avatar URL format');
    }

    if (errors.length > 0) {
      throw new ValidationException('Validation failed', { errors });
    }
  }

  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  isValidUsername(username) {
    const usernameRegex = /^[a-zA-Z0-9_]+$/;
    return usernameRegex.test(username);
  }

  isValidPhoneNumber(phoneNumber) {
    const phoneRegex = /^(\+\d{1,3}[- ]?)?\d{10,12}$/;
    return phoneRegex.test(phoneNumber);
  }

  isValidUrl(url) {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  toUpdateData() {
    const updateData = {};

    if (this.name !== undefined) {
      updateData.name = this.name;
    }

    if (this.email !== undefined) {
      updateData.email = this.email;
    }

    if (this.username !== undefined) {
      updateData.username = this.username;
    }

    if (this.role !== undefined) {
      updateData.role = this.role;
    }

    if (this.status !== undefined) {
      updateData.status = this.status;
    }

    if (this.isEmailVerified !== undefined) {
      updateData.isEmailVerified = this.isEmailVerified;
    }

    if (this.phoneNumber !== undefined || this.address !== undefined || this.avatar !== undefined) {
      updateData.profile = {};

      if (this.phoneNumber !== undefined) {
        updateData.profile.phoneNumber = this.phoneNumber;
      }

      if (this.address !== undefined) {
        updateData.profile.address = this.address;
      }

      if (this.avatar !== undefined) {
        updateData.profile.avatar = this.avatar;
      }
    }

    return updateData;
  }
}

module.exports = UpdateUserDto;
