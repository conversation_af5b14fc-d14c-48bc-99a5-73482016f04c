/**
 * Password Service
 * Infrastructure service for password operations
 */

const bcrypt = require('bcryptjs');
const { SECURITY } = require('../../shared/constants');

class PasswordService {
  /**
   * Hash password
   * @param {string} password - Plain text password
   * @returns {Promise<string>} - Hashed password
   */
  async hash(password) {
    try {
      const salt = await bcrypt.genSalt(SECURITY.BCRYPT_ROUNDS);
      return await bcrypt.hash(password, salt);
    } catch (error) {
      throw new Error(`Password hashing failed: ${error.message}`);
    }
  }

  /**
   * Compare password with hash
   * @param {string} password - Plain text password
   * @param {string} hash - Hashed password
   * @returns {Promise<boolean>} - Password match result
   */
  async compare(password, hash) {
    try {
      return await bcrypt.compare(password, hash);
    } catch (error) {
      throw new Error(`Password comparison failed: ${error.message}`);
    }
  }

  /**
   * Validate password strength
   * @param {string} password - Password to validate
   * @returns {Object} - Validation result
   */
  validateStrength(password) {
    const result = {
      isValid: true,
      errors: [],
      score: 0
    };

    // Length check
    if (password.length < 8) {
      result.errors.push('Password must be at least 8 characters long');
      result.isValid = false;
    } else {
      result.score += 1;
    }

    // Uppercase check
    if (!/[A-Z]/.test(password)) {
      result.errors.push('Password must contain at least one uppercase letter');
    } else {
      result.score += 1;
    }

    // Lowercase check
    if (!/[a-z]/.test(password)) {
      result.errors.push('Password must contain at least one lowercase letter');
    } else {
      result.score += 1;
    }

    // Number check
    if (!/\d/.test(password)) {
      result.errors.push('Password must contain at least one number');
    } else {
      result.score += 1;
    }

    // Special character check
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      result.errors.push('Password must contain at least one special character');
    } else {
      result.score += 1;
    }

    // Common password check
    const commonPasswords = [
      'password', '123456', '123456789', 'qwerty', 'abc123',
      'password123', 'admin', 'letmein', 'welcome', 'monkey'
    ];
    
    if (commonPasswords.includes(password.toLowerCase())) {
      result.errors.push('Password is too common');
      result.isValid = false;
      result.score = 0;
    }

    // Set overall validity
    if (result.errors.length > 2) {
      result.isValid = false;
    }

    return result;
  }

  /**
   * Generate random password
   * @param {number} length - Password length
   * @param {Object} options - Generation options
   * @returns {string} - Generated password
   */
  generateRandomPassword(length = 12, options = {}) {
    const {
      includeUppercase = true,
      includeLowercase = true,
      includeNumbers = true,
      includeSpecialChars = true,
      excludeSimilar = true
    } = options;

    let charset = '';
    
    if (includeLowercase) {
      charset += excludeSimilar ? 'abcdefghjkmnpqrstuvwxyz' : 'abcdefghijklmnopqrstuvwxyz';
    }
    
    if (includeUppercase) {
      charset += excludeSimilar ? 'ABCDEFGHJKMNPQRSTUVWXYZ' : 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    }
    
    if (includeNumbers) {
      charset += excludeSimilar ? '23456789' : '0123456789';
    }
    
    if (includeSpecialChars) {
      charset += '!@#$%^&*()_+-=[]{}|;:,.<>?';
    }

    if (!charset) {
      throw new Error('At least one character type must be included');
    }

    let password = '';
    for (let i = 0; i < length; i++) {
      const randomIndex = Math.floor(Math.random() * charset.length);
      password += charset[randomIndex];
    }

    return password;
  }

  /**
   * Check if password has been compromised (basic implementation)
   * In production, this could integrate with HaveIBeenPwned API
   * @param {string} password - Password to check
   * @returns {Promise<boolean>} - Whether password is compromised
   */
  async isCompromised(password) {
    // Basic implementation - check against common passwords
    const commonPasswords = [
      'password', '123456', '123456789', 'qwerty', 'abc123',
      'password123', 'admin', 'letmein', 'welcome', 'monkey',
      '1234567890', 'qwertyuiop', 'asdfghjkl', 'zxcvbnm',
      'password1', 'password12', 'password123', 'admin123'
    ];

    return commonPasswords.includes(password.toLowerCase());
  }

  /**
   * Generate password hash with custom salt rounds
   * @param {string} password - Plain text password
   * @param {number} saltRounds - Number of salt rounds
   * @returns {Promise<string>} - Hashed password
   */
  async hashWithCustomSalt(password, saltRounds = SECURITY.BCRYPT_ROUNDS) {
    try {
      const salt = await bcrypt.genSalt(saltRounds);
      return await bcrypt.hash(password, salt);
    } catch (error) {
      throw new Error(`Password hashing with custom salt failed: ${error.message}`);
    }
  }

  /**
   * Get password hash info
   * @param {string} hash - Password hash
   * @returns {Object} - Hash information
   */
  getHashInfo(hash) {
    try {
      // Extract salt rounds from bcrypt hash
      const saltRounds = parseInt(hash.substring(4, 6));
      
      return {
        algorithm: 'bcrypt',
        saltRounds,
        isValid: hash.length === 60 && hash.startsWith('$2')
      };
    } catch (error) {
      return {
        algorithm: 'unknown',
        saltRounds: null,
        isValid: false
      };
    }
  }
}

module.exports = PasswordService;
