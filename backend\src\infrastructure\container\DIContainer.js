/**
 * Dependency Injection Container
 * Manages application dependencies and their lifecycles
 */

class DIContainer {
  constructor() {
    this.services = new Map();
    this.singletons = new Map();
    this.factories = new Map();
  }

  /**
   * Register a service with its dependencies
   * @param {string} name - Service name
   * @param {Function} factory - Factory function to create the service
   * @param {Object} options - Registration options
   */
  register(name, factory, options = {}) {
    const { singleton = false, dependencies = [] } = options;

    this.services.set(name, {
      factory,
      dependencies,
      singleton
    });

    return this;
  }

  /**
   * Register a singleton service
   * @param {string} name - Service name
   * @param {Function} factory - Factory function
   * @param {Array} dependencies - Service dependencies
   */
  registerSingleton(name, factory, dependencies = []) {
    return this.register(name, factory, { singleton: true, dependencies });
  }

  /**
   * Register a transient service (new instance each time)
   * @param {string} name - Service name
   * @param {Function} factory - Factory function
   * @param {Array} dependencies - Service dependencies
   */
  registerTransient(name, factory, dependencies = []) {
    return this.register(name, factory, { singleton: false, dependencies });
  }

  /**
   * Register a class as a service
   * @param {string} name - Service name
   * @param {Function} ClassConstructor - Class constructor
   * @param {Object} options - Registration options
   */
  registerClass(name, ClassConstructor, options = {}) {
    const factory = (...dependencies) => new ClassConstructor(...dependencies);
    return this.register(name, factory, options);
  }

  /**
   * Register an instance directly
   * @param {string} name - Service name
   * @param {*} instance - Service instance
   */
  registerInstance(name, instance) {
    this.singletons.set(name, instance);
    return this;
  }

  /**
   * Resolve a service by name
   * @param {string} name - Service name
   * @returns {*} Service instance
   */
  resolve(name) {
    // Check if it's already a singleton instance
    if (this.singletons.has(name)) {
      return this.singletons.get(name);
    }

    // Check if service is registered
    if (!this.services.has(name)) {
      throw new Error(`Service '${name}' is not registered`);
    }

    const serviceConfig = this.services.get(name);
    const { factory, dependencies, singleton } = serviceConfig;

    // Resolve dependencies
    const resolvedDependencies = dependencies.map(dep => this.resolve(dep));

    // Create instance
    const instance = factory(...resolvedDependencies);

    // Store singleton
    if (singleton) {
      this.singletons.set(name, instance);
    }

    return instance;
  }

  /**
   * Check if a service is registered
   * @param {string} name - Service name
   * @returns {boolean}
   */
  has(name) {
    return this.services.has(name) || this.singletons.has(name);
  }

  /**
   * Get all registered service names
   * @returns {Array<string>}
   */
  getRegisteredServices() {
    return [
      ...Array.from(this.services.keys()),
      ...Array.from(this.singletons.keys())
    ];
  }

  /**
   * Clear all registrations
   */
  clear() {
    this.services.clear();
    this.singletons.clear();
    this.factories.clear();
  }

  /**
   * Create a child container
   * @returns {DIContainer}
   */
  createChild() {
    const child = new DIContainer();
    
    // Copy parent registrations
    for (const [name, config] of this.services) {
      child.services.set(name, config);
    }
    
    for (const [name, instance] of this.singletons) {
      child.singletons.set(name, instance);
    }

    return child;
  }

  /**
   * Batch register services
   * @param {Object} registrations - Service registrations
   */
  registerBatch(registrations) {
    for (const [name, config] of Object.entries(registrations)) {
      if (config.instance) {
        this.registerInstance(name, config.instance);
      } else if (config.class) {
        this.registerClass(name, config.class, {
          singleton: config.singleton,
          dependencies: config.dependencies
        });
      } else if (config.factory) {
        this.register(name, config.factory, {
          singleton: config.singleton,
          dependencies: config.dependencies
        });
      }
    }
    return this;
  }

  /**
   * Auto-wire a class constructor
   * @param {Function} ClassConstructor - Class to auto-wire
   * @returns {*} Instance with dependencies injected
   */
  autoWire(ClassConstructor) {
    // Get constructor parameter names (requires reflection or manual specification)
    const dependencies = ClassConstructor.dependencies || [];
    const resolvedDependencies = dependencies.map(dep => this.resolve(dep));
    return new ClassConstructor(...resolvedDependencies);
  }

  /**
   * Dispose all disposable services
   */
  async dispose() {
    const disposalPromises = [];

    for (const [name, instance] of this.singletons) {
      if (instance && typeof instance.dispose === 'function') {
        disposalPromises.push(instance.dispose());
      }
    }

    await Promise.all(disposalPromises);
    this.clear();
  }
}

module.exports = DIContainer;
