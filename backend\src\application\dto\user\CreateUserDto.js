/**
 * Create User DTO
 * Data Transfer Object for user creation
 */

const { ValidationException } = require('../../../core/exceptions');
const { UserRole } = require('../../../shared/enums');
const { VALIDATION } = require('../../../shared/constants');

class CreateUserDto {
  constructor(data) {
    this.name = data.name?.trim();
    this.email = data.email?.toLowerCase().trim();
    this.username = data.username?.toLowerCase().trim();
    this.password = data.password;
    this.role = data.role || UserRole.USER;
    this.phoneNumber = data.phoneNumber?.trim();
    this.address = data.address;

    this.validate();
  }

  validate() {
    const errors = [];

    // Name validation
    if (!this.name) {
      errors.push('Name is required');
    } else if (this.name.length < VALIDATION.NAME_MIN_LENGTH) {
      errors.push(`Name must be at least ${VALIDATION.NAME_MIN_LENGTH} characters`);
    } else if (this.name.length > VALIDATION.NAME_MAX_LENGTH) {
      errors.push(`Name cannot exceed ${VALIDATION.NAME_MAX_LENGTH} characters`);
    }

    // Email validation
    if (!this.email) {
      errors.push('Email is required');
    } else if (!this.isValidEmail(this.email)) {
      errors.push('Invalid email format');
    }

    // Username validation
    if (!this.username) {
      errors.push('Username is required');
    } else if (this.username.length < VALIDATION.USERNAME_MIN_LENGTH) {
      errors.push(`Username must be at least ${VALIDATION.USERNAME_MIN_LENGTH} characters`);
    } else if (this.username.length > VALIDATION.USERNAME_MAX_LENGTH) {
      errors.push(`Username cannot exceed ${VALIDATION.USERNAME_MAX_LENGTH} characters`);
    } else if (!this.isValidUsername(this.username)) {
      errors.push('Username can only contain letters, numbers, and underscores');
    }

    // Password validation
    if (!this.password) {
      errors.push('Password is required');
    } else if (this.password.length < VALIDATION.PASSWORD_MIN_LENGTH) {
      errors.push(`Password must be at least ${VALIDATION.PASSWORD_MIN_LENGTH} characters`);
    } else if (this.password.length > VALIDATION.PASSWORD_MAX_LENGTH) {
      errors.push(`Password cannot exceed ${VALIDATION.PASSWORD_MAX_LENGTH} characters`);
    }

    // Role validation
    if (!Object.values(UserRole).includes(this.role)) {
      errors.push('Invalid user role');
    }

    // Phone number validation (optional)
    if (this.phoneNumber && !this.isValidPhoneNumber(this.phoneNumber)) {
      errors.push('Invalid phone number format');
    }

    if (errors.length > 0) {
      throw new ValidationException('Validation failed', { errors });
    }
  }

  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  isValidUsername(username) {
    const usernameRegex = /^[a-zA-Z0-9_]+$/;
    return usernameRegex.test(username);
  }

  isValidPhoneNumber(phoneNumber) {
    const phoneRegex = /^(\+\d{1,3}[- ]?)?\d{10,12}$/;
    return phoneRegex.test(phoneNumber);
  }

  toEntity() {
    return {
      name: this.name,
      email: this.email,
      username: this.username,
      password: this.password,
      role: this.role,
      profile: {
        phoneNumber: this.phoneNumber,
        address: this.address
      }
    };
  }
}

module.exports = CreateUserDto;
