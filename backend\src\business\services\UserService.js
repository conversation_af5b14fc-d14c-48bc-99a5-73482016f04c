/**
 * User Business Service
 * Business logic layer for user operations
 */

const CreateUserUseCase = require('../../core/usecases/user/CreateUserUseCase');
const AuthenticateUserUseCase = require('../../core/usecases/user/AuthenticateUserUseCase');
const {
  UserNotFoundException,
  ValidationException,
  AuthorizationException
} = require('../../core/exceptions');
const { UserRole, ApprovalStatus } = require('../../shared/enums');

class UserService {
  constructor(userRepository, passwordService, tokenService, emailService) {
    this.userRepository = userRepository;
    this.passwordService = passwordService;
    this.tokenService = tokenService;
    this.emailService = emailService;

    // Initialize use cases
    this.createUserUseCase = new CreateUserUseCase(
      userRepository,
      passwordService,
      emailService
    );

    this.authenticateUserUseCase = new AuthenticateUserUseCase(
      userRepository,
      passwordService,
      tokenService
    );
  }

  /**
   * <PERSON><PERSON> k<PERSON> oluştur
   */
  async createUser(userData) {
    return await this.createUserUseCase.execute(userData);
  }

  /**
   * Kullanıcı kimlik doğrulama
   */
  async authenticateUser(emailOrUsername, password) {
    console.log('🔍 UserService.authenticateUser called with:', { emailOrUsername, hasPassword: !!password });
    try {
      const result = await this.authenticateUserUseCase.execute(emailOrUsername, password);
      console.log('✅ UserService.authenticateUser success:', { userId: result.user?.id, email: result.user?.email });
      return result;
    } catch (error) {
      console.error('❌ UserService.authenticateUser error:', error.message);
      throw error;
    }
  }

  /**
   * Kullanıcı bilgilerini getir
   */
  async getUserById(id) {
    const user = await this.userRepository.findById(id);
    if (!user) {
      throw new UserNotFoundException(id);
    }
    return user.toJSON();
  }

  /**
   * Kullanıcı profilini güncelle
   */
  async updateUserProfile(userId, profileData, requestingUserId) {
    // Authorization check
    await this.checkUserUpdatePermission(userId, requestingUserId);

    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new UserNotFoundException(userId);
    }

    // Check if this is an admin update (contains admin-only fields)
    const requestingUser = await this.userRepository.findById(requestingUserId);
    const isAdminUpdate = requestingUser && requestingUser.isAdmin() &&
      (profileData.email || profileData.username || profileData.role || profileData.status || profileData.isEmailVerified !== undefined);

    if (isAdminUpdate) {
      // Admin can update all fields
      if (profileData.name !== undefined) user.name = profileData.name;
      if (profileData.email !== undefined) user.email = profileData.email;
      if (profileData.username !== undefined) user.username = profileData.username;
      if (profileData.role !== undefined) user.role = profileData.role;
      if (profileData.status !== undefined) user.status = profileData.status;
      if (profileData.isEmailVerified !== undefined) user.isEmailVerified = profileData.isEmailVerified;

      // Update profile data if provided
      if (profileData.profile) {
        user.updateProfile(profileData.profile);
      }
    } else {
      // Regular profile update
      user.updateProfile(profileData);
    }

    const updatedUser = await this.userRepository.update(userId, user);
    return updatedUser.toJSON();
  }

  /**
   * Kullanıcı şifresini değiştir
   */
  async changePassword(userId, currentPassword, newPassword, requestingUserId) {
    // Authorization check
    await this.checkUserUpdatePermission(userId, requestingUserId);

    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new UserNotFoundException(userId);
    }

    // Verify current password
    const isCurrentPasswordValid = await this.passwordService.compare(
      currentPassword,
      user.password
    );

    if (!isCurrentPasswordValid) {
      throw new ValidationException('Current password is incorrect');
    }

    // Hash new password
    const hashedNewPassword = await this.passwordService.hash(newPassword);

    // Update password
    await this.userRepository.updatePassword(userId, hashedNewPassword);

    return { message: 'Password updated successfully' };
  }

  /**
   * Kullanıcı onaylama (Admin)
   */
  async approveUser(userId, adminId, reason = '') {
    const admin = await this.userRepository.findById(adminId);
    if (!admin || !admin.isAdmin()) {
      throw new AuthorizationException('Only admins can approve users');
    }

    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new UserNotFoundException(userId);
    }

    user.updateApprovalStatus(ApprovalStatus.APPROVED);
    user.updateBidPermission(true);
    user.updateLotteryPermission(true);

    const updatedUser = await this.userRepository.update(userId, user);

    // Send approval email
    await this.emailService.sendApprovalEmail(user.email, {
      name: user.name,
      approved: true,
      reason
    });

    return updatedUser.toJSON();
  }

  /**
   * Kullanıcı reddetme (Admin)
   */
  async rejectUser(userId, adminId, reason = '') {
    const admin = await this.userRepository.findById(adminId);
    if (!admin || !admin.isAdmin()) {
      throw new AuthorizationException('Only admins can reject users');
    }

    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new UserNotFoundException(userId);
    }

    user.updateApprovalStatus(ApprovalStatus.REJECTED);
    user.updateBidPermission(false);
    user.updateLotteryPermission(false);

    const updatedUser = await this.userRepository.update(userId, user);

    // Send rejection email
    await this.emailService.sendApprovalEmail(user.email, {
      name: user.name,
      approved: false,
      reason
    });

    return updatedUser.toJSON();
  }

  /**
   * Kullanıcı izinlerini güncelle (Admin)
   */
  async updateUserPermissions(userId, permissions, adminId) {
    const admin = await this.userRepository.findById(adminId);
    if (!admin || !admin.isAdmin()) {
      throw new AuthorizationException('Only admins can update user permissions');
    }

    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new UserNotFoundException(userId);
    }

    // Update permissions
    if (permissions.canBid !== undefined) {
      user.updateBidPermission(permissions.canBid);
    }

    if (permissions.canParticipateInLottery !== undefined) {
      user.updateLotteryPermission(permissions.canParticipateInLottery);
    }

    const updatedUser = await this.userRepository.update(userId, user);
    return updatedUser.toJSON();
  }

  /**
   * Kullanıcıları listele (Admin)
   */
  async getUsers(options, requestingUserId) {
    const requestingUser = await this.userRepository.findById(requestingUserId);
    if (!requestingUser || !requestingUser.isAdmin()) {
      throw new AuthorizationException('Only admins can list users');
    }

    const result = await this.userRepository.findAll(options);

    return {
      ...result,
      users: result.users.map(user => user.toJSON())
    };
  }

  /**
   * Kullanıcı arama (Admin)
   */
  async searchUsers(searchTerm, options, requestingUserId) {
    const requestingUser = await this.userRepository.findById(requestingUserId);
    if (!requestingUser || !requestingUser.isAdmin()) {
      throw new AuthorizationException('Only admins can search users');
    }

    const result = await this.userRepository.search(searchTerm, options);

    return {
      ...result,
      users: result.users.map(user => user.toJSON())
    };
  }

  /**
   * Kullanıcı güncelleme izni kontrolü
   */
  async checkUserUpdatePermission(targetUserId, requestingUserId) {
    // Users can update their own profile
    if (targetUserId === requestingUserId) {
      return true;
    }

    // Admins can update any user
    const requestingUser = await this.userRepository.findById(requestingUserId);
    if (requestingUser && requestingUser.isAdmin()) {
      return true;
    }

    throw new AuthorizationException('You can only update your own profile');
  }

  /**
   * Email doğrulama
   */
  async verifyEmail(userId, token) {
    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new UserNotFoundException(userId);
    }

    // Verify token (implementation depends on token service)
    const isTokenValid = await this.tokenService.verifyEmailToken(token, user.email);
    if (!isTokenValid) {
      throw new ValidationException('Invalid or expired verification token');
    }

    user.verifyEmail();
    const updatedUser = await this.userRepository.update(userId, user);

    return updatedUser.toJSON();
  }

  /**
   * Şifre sıfırlama isteği
   */
  async requestPasswordReset(email) {
    const user = await this.userRepository.findByEmail(email);
    if (!user) {
      // Don't reveal if email exists
      return { message: 'If email exists, reset instructions have been sent' };
    }

    const resetToken = await this.tokenService.generatePasswordResetToken(user.id);

    await this.emailService.sendPasswordResetEmail(user.email, {
      name: user.name,
      resetToken,
      resetUrl: `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`
    });

    return { message: 'If email exists, reset instructions have been sent' };
  }

  /**
   * Şifre sıfırlama
   */
  async resetPassword(token, newPassword) {
    const userId = await this.tokenService.verifyPasswordResetToken(token);
    if (!userId) {
      throw new ValidationException('Invalid or expired reset token');
    }

    const hashedPassword = await this.passwordService.hash(newPassword);
    await this.userRepository.updatePassword(userId, hashedPassword);

    return { message: 'Password reset successfully' };
  }

  /**
   * Kullanıcı silme (Admin)
   */
  async deleteUser(userId, adminId) {
    const admin = await this.userRepository.findById(adminId);
    if (!admin || !admin.isAdmin()) {
      throw new AuthorizationException('Only admins can delete users');
    }

    // Admin kendini silemez
    if (userId === adminId) {
      throw new ValidationException('Admin cannot delete their own account');
    }

    const user = await this.userRepository.findById(userId);
    if (!user) {
      throw new UserNotFoundException(userId);
    }

    // Kullanıcıyı sil
    await this.userRepository.delete(userId);

    return { message: 'User deleted successfully' };
  }
}

module.exports = UserService;
