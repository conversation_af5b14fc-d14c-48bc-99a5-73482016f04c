/**
 * Error Handling Middleware
 * Centralized error handling for the application
 */

const { BusinessException } = require('../../core/exceptions');
const { HttpStatus, ErrorType } = require('../../shared/enums');

class ErrorMiddleware {
  /**
   * Global error handler
   */
  static handle() {
    return (error, req, res, next) => {
      // Log error
      console.error('Error occurred:', {
        message: error.message,
        stack: error.stack,
        url: req.url,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        timestamp: new Date().toISOString()
      });

      // Business exceptions (our custom exceptions)
      if (error instanceof BusinessException) {
        return res.status(error.statusCode).json({
          success: false,
          message: error.message,
          type: error.type,
          details: error.details,
          timestamp: error.timestamp
        });
      }

      // Mongoose validation errors
      if (error.name === 'ValidationError') {
        const validationErrors = Object.values(error.errors).map(err => ({
          field: err.path,
          message: err.message,
          value: err.value
        }));

        return res.status(HttpStatus.UNPROCESSABLE_ENTITY).json({
          success: false,
          message: 'Validation failed',
          type: ErrorType.VALIDATION_ERROR,
          details: { errors: validationErrors },
          timestamp: new Date().toISOString()
        });
      }

      // Mongoose cast errors (invalid ObjectId, etc.)
      if (error.name === 'CastError') {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          message: `Invalid ${error.path}: ${error.value}`,
          type: ErrorType.VALIDATION_ERROR,
          timestamp: new Date().toISOString()
        });
      }

      // Mongoose duplicate key errors
      if (error.code === 11000) {
        const field = Object.keys(error.keyPattern)[0];
        const value = error.keyValue[field];
        
        return res.status(HttpStatus.CONFLICT).json({
          success: false,
          message: `${field} '${value}' already exists`,
          type: ErrorType.VALIDATION_ERROR,
          details: { field, value },
          timestamp: new Date().toISOString()
        });
      }

      // JWT errors
      if (error.name === 'JsonWebTokenError') {
        return res.status(HttpStatus.UNAUTHORIZED).json({
          success: false,
          message: 'Invalid token',
          type: ErrorType.AUTHENTICATION_ERROR,
          timestamp: new Date().toISOString()
        });
      }

      if (error.name === 'TokenExpiredError') {
        return res.status(HttpStatus.UNAUTHORIZED).json({
          success: false,
          message: 'Token has expired',
          type: ErrorType.AUTHENTICATION_ERROR,
          timestamp: new Date().toISOString()
        });
      }

      // Multer errors (file upload)
      if (error.code === 'LIMIT_FILE_SIZE') {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          message: 'File size too large',
          type: ErrorType.VALIDATION_ERROR,
          timestamp: new Date().toISOString()
        });
      }

      if (error.code === 'LIMIT_FILE_COUNT') {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          message: 'Too many files',
          type: ErrorType.VALIDATION_ERROR,
          timestamp: new Date().toISOString()
        });
      }

      if (error.code === 'LIMIT_UNEXPECTED_FILE') {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          message: 'Unexpected file field',
          type: ErrorType.VALIDATION_ERROR,
          timestamp: new Date().toISOString()
        });
      }

      // Syntax errors
      if (error instanceof SyntaxError && error.status === 400 && 'body' in error) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          message: 'Invalid JSON syntax',
          type: ErrorType.VALIDATION_ERROR,
          timestamp: new Date().toISOString()
        });
      }

      // Rate limiting errors
      if (error.status === 429) {
        return res.status(HttpStatus.TOO_MANY_REQUESTS).json({
          success: false,
          message: 'Too many requests, please try again later',
          type: ErrorType.VALIDATION_ERROR,
          timestamp: new Date().toISOString()
        });
      }

      // Database connection errors
      if (error.name === 'MongoNetworkError' || error.name === 'MongoTimeoutError') {
        return res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
          success: false,
          message: 'Database connection error',
          type: ErrorType.DATABASE_ERROR,
          timestamp: new Date().toISOString()
        });
      }

      // Default error response
      const statusCode = error.statusCode || error.status || HttpStatus.INTERNAL_SERVER_ERROR;
      const message = process.env.NODE_ENV === 'production' 
        ? 'Internal server error' 
        : error.message;

      res.status(statusCode).json({
        success: false,
        message,
        type: ErrorType.INTERNAL_SERVER_ERROR,
        ...(process.env.NODE_ENV === 'development' && {
          stack: error.stack,
          details: error
        }),
        timestamp: new Date().toISOString()
      });
    };
  }

  /**
   * 404 Not Found handler
   */
  static notFound() {
    return (req, res, next) => {
      res.status(HttpStatus.NOT_FOUND).json({
        success: false,
        message: `Route ${req.originalUrl} not found`,
        type: ErrorType.NOT_FOUND_ERROR,
        timestamp: new Date().toISOString()
      });
    };
  }

  /**
   * Async error wrapper
   */
  static asyncHandler(fn) {
    return (req, res, next) => {
      Promise.resolve(fn(req, res, next)).catch(next);
    };
  }

  /**
   * Validation error formatter
   */
  static formatValidationError(errors) {
    if (Array.isArray(errors)) {
      return errors.map(error => ({
        field: error.field || error.param,
        message: error.message || error.msg,
        value: error.value
      }));
    }

    if (typeof errors === 'object') {
      return Object.keys(errors).map(field => ({
        field,
        message: errors[field].message || errors[field],
        value: errors[field].value
      }));
    }

    return [{ message: errors }];
  }

  /**
   * Development error details
   */
  static getDevelopmentErrorDetails(error) {
    return {
      message: error.message,
      stack: error.stack,
      name: error.name,
      code: error.code,
      status: error.status,
      statusCode: error.statusCode
    };
  }

  /**
   * Production error message
   */
  static getProductionErrorMessage(error) {
    // Only expose safe error messages in production
    const safeErrors = [
      'ValidationError',
      'CastError',
      'JsonWebTokenError',
      'TokenExpiredError'
    ];

    if (error instanceof BusinessException || safeErrors.includes(error.name)) {
      return error.message;
    }

    return 'Internal server error';
  }
}

module.exports = ErrorMiddleware;
