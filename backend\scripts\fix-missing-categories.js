/**
 * Fix Missing Categories Script
 * Bu script eksik kategorileri tespit eder ve oluşturur
 */

const mongoose = require('mongoose');
require('dotenv').config();

// Models
const ProductModel = require('../src/infrastructure/database/models/Product');
const CategoryModel = require('../src/infrastructure/database/models/Category');

async function connectDatabase() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/robinhood_database');
    console.log('✅ Database connected successfully');
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    process.exit(1);
  }
}

async function fixMissingCategories() {
  try {
    console.log('🚀 Starting Missing Categories Fix...');

    // 1. Tüm products'ları getir
    const products = await ProductModel.find({}).lean();
    console.log(`📦 Found ${products.length} products`);

    // 2. Tüm kategorileri getir
    const categories = await CategoryModel.find({}).lean();
    console.log(`📋 Found ${categories.length} categories:`);
    categories.forEach(cat => {
      console.log(`   - ${cat.name} (${cat._id}) - Active: ${cat.isActive}`);
    });

    // 3. Products'larda kullanılan categoryId'leri topla
    const usedCategoryIds = new Set();
    products.forEach(product => {
      if (product.categoryId) {
        usedCategoryIds.add(product.categoryId.toString());
      }
    });

    console.log(`\n🔍 Used category IDs in products: ${usedCategoryIds.size}`);
    usedCategoryIds.forEach(id => {
      console.log(`   - ${id}`);
    });

    // 4. Eksik kategorileri tespit et
    const existingCategoryIds = new Set(categories.map(cat => cat._id.toString()));
    const missingCategoryIds = [...usedCategoryIds].filter(id => !existingCategoryIds.has(id));

    console.log(`\n❌ Missing categories: ${missingCategoryIds.length}`);
    missingCategoryIds.forEach(id => {
      console.log(`   - ${id}`);
    });

    // 5. Eksik kategoriler için products'ları listele
    if (missingCategoryIds.length > 0) {
      console.log('\n📋 Products with missing categories:');
      missingCategoryIds.forEach(missingId => {
        const productsWithMissingCategory = products.filter(p => p.categoryId && p.categoryId.toString() === missingId);
        console.log(`\n   Category ID: ${missingId}`);
        productsWithMissingCategory.forEach(product => {
          console.log(`     - ${product.name}`);
        });
      });

      // 6. Kullanıcıdan onay al
      const readline = require('readline');
      const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
      });

      const answer = await new Promise(resolve => {
        rl.question('\n🤔 Do you want to create default categories for missing IDs? (y/n): ', resolve);
      });
      rl.close();

      if (answer.toLowerCase() === 'y') {
        // 7. Eksik kategoriler için default kategoriler oluştur
        let createdCount = 0;
        for (const missingId of missingCategoryIds) {
          const productsWithMissingCategory = products.filter(p => p.categoryId && p.categoryId.toString() === missingId);
          const categoryName = `Kategori ${missingId.substring(0, 8)}`;
          
          try {
            const newCategory = new CategoryModel({
              _id: new mongoose.Types.ObjectId(missingId),
              name: categoryName,
              slug: categoryName.toLowerCase().replace(/\s+/g, '-'),
              description: `Auto-generated category for ${productsWithMissingCategory.length} products`,
              isActive: true,
              status: 'active',
              level: 0,
              order: 999,
              showInMenu: true,
              showInHomepage: false,
              isFeatured: false
            });

            await newCategory.save();
            console.log(`✅ Created category: ${categoryName} (${missingId})`);
            createdCount++;
          } catch (error) {
            console.error(`❌ Failed to create category ${missingId}:`, error.message);
          }
        }

        console.log(`\n✅ Created ${createdCount} categories`);
      } else {
        console.log('❌ Category creation cancelled by user');
      }
    } else {
      console.log('\n✅ No missing categories found!');
    }

    // 8. Final verification
    const updatedCategories = await CategoryModel.find({}).lean();
    console.log(`\n📊 Final Summary:`);
    console.log(`📋 Total categories: ${updatedCategories.length}`);
    console.log(`📦 Total products: ${products.length}`);
    console.log(`🔗 Products with categoryId: ${products.filter(p => p.categoryId).length}`);
    console.log(`❌ Products without categoryId: ${products.filter(p => !p.categoryId).length}`);

  } catch (error) {
    console.error('❌ Fix failed:', error);
    throw error;
  }
}

async function main() {
  try {
    await connectDatabase();
    await fixMissingCategories();
    console.log('\n🎉 Fix completed successfully!');
  } catch (error) {
    console.error('❌ Fix failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('👋 Database disconnected');
    process.exit(0);
  }
}

// Script'i çalıştır
if (require.main === module) {
  main();
}

module.exports = { fixMissingCategories };
