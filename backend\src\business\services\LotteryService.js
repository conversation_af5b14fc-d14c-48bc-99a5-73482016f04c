/**
 * Lottery Service
 * Business logic for lottery operations
 */

const { BusinessException, ValidationException, AuthorizationException } = require('../../core/exceptions');
const { LotteryStatus, UserRole } = require('../../shared/enums');
const Lottery = require('../../core/entities/Lottery');

class LotteryService {
  constructor(lotteryRepository, userRepository, emailService) {
    this.lotteryRepository = lotteryRepository;
    this.userRepository = userRepository;
    this.emailService = emailService;
  }

  /**
   * Get all lotteries
   */
  async getAllLotteries(options = {}) {
    try {
      return await this.lotteryRepository.findAll(options);
    } catch (error) {
      throw new BusinessException(`Failed to get lotteries: ${error.message}`);
    }
  }

  /**
   * Get lottery by ID
   */
  async getLotteryById(id) {
    try {
      const lottery = await this.lotteryRepository.findById(id);
      if (!lottery) {
        throw new ValidationException('Lottery not found');
      }
      return lottery;
    } catch (error) {
      if (error instanceof ValidationException) throw error;
      throw new BusinessException(`Failed to get lottery: ${error.message}`);
    }
  }

  /**
   * Create new lottery
   */
  async createLottery(lotteryData, createdBy) {
    try {
      // Validate user permissions
      const user = await this.userRepository.findById(createdBy);
      if (!user) {
        throw new ValidationException('User not found');
      }

      if (user.role !== UserRole.ADMIN) {
        throw new AuthorizationException('Only admins can create lotteries');
      }

      // Create lottery entity
      const lottery = new Lottery({
        ...lotteryData,
        metadata: {
          createdBy
        }
      });

      // Save to repository
      const savedLottery = await this.lotteryRepository.create(lottery.toPersistence());

      return savedLottery;
    } catch (error) {
      if (error instanceof ValidationException || error instanceof AuthorizationException) {
        throw error;
      }
      throw new BusinessException(`Failed to create lottery: ${error.message}`);
    }
  }

  /**
   * Update lottery
   */
  async updateLottery(id, updateData, updatedBy) {
    try {
      const lottery = await this.lotteryRepository.findById(id);
      if (!lottery) {
        throw new ValidationException('Lottery not found');
      }

      // Check permissions
      const user = await this.userRepository.findById(updatedBy);
      if (!user || user.role !== UserRole.ADMIN) {
        throw new AuthorizationException('Only admins can update lotteries');
      }

      // Validate lottery can be updated
      if (lottery.status === LotteryStatus.COMPLETED || lottery.status === LotteryStatus.CANCELLED) {
        throw new ValidationException('Cannot update completed or cancelled lottery');
      }

      // Update metadata
      updateData.metadata = {
        ...lottery.metadata,
        updatedBy,
        updatedAt: new Date()
      };

      const updatedLottery = await this.lotteryRepository.update(id, updateData);
      return updatedLottery;
    } catch (error) {
      if (error instanceof ValidationException || error instanceof AuthorizationException) {
        throw error;
      }
      throw new BusinessException(`Failed to update lottery: ${error.message}`);
    }
  }

  /**
   * Delete lottery
   */
  async deleteLottery(id, deletedBy) {
    try {
      const lottery = await this.lotteryRepository.findById(id);
      if (!lottery) {
        throw new ValidationException('Lottery not found');
      }

      // Check permissions
      const user = await this.userRepository.findById(deletedBy);
      if (!user || user.role !== UserRole.ADMIN) {
        throw new AuthorizationException('Only admins can delete lotteries');
      }

      // Validate lottery can be deleted
      if (lottery.status === LotteryStatus.ACTIVE && lottery.tickets.length > 0) {
        throw new ValidationException('Cannot delete active lottery with tickets sold');
      }

      const success = await this.lotteryRepository.delete(id);
      if (!success) {
        throw new BusinessException('Failed to delete lottery');
      }

      return true;
    } catch (error) {
      if (error instanceof ValidationException || error instanceof AuthorizationException) {
        throw error;
      }
      throw new BusinessException(`Failed to delete lottery: ${error.message}`);
    }
  }

  /**
   * Purchase lottery ticket
   */
  async purchaseTicket(lotteryId, userId, quantity = 1) {
    try {
      // Get lottery
      const lottery = await this.lotteryRepository.findById(lotteryId);
      if (!lottery) {
        throw new ValidationException('Lottery not found');
      }

      // Get user
      const user = await this.userRepository.findById(userId);
      if (!user) {
        throw new ValidationException('User not found');
      }

      // Check user permissions
      if (!user.canParticipateInLottery) {
        throw new AuthorizationException('You do not have permission to participate in lotteries');
      }

      if (!user.isActive()) {
        throw new AuthorizationException('Your account is not active');
      }

      // Create lottery entity and purchase ticket
      const lotteryEntity = Lottery.fromPersistence(lottery);
      const newTickets = lotteryEntity.purchaseTicket(userId, user.username, quantity);

      // Save updated lottery
      const ticketData = newTickets.map(ticket => ({
        userId,
        username: user.username,
        numbers: ticket.numbers,
        purchaseTime: ticket.purchaseTime,
        ticketId: ticket.ticketId,
        price: lottery.ticketPrice
      }));

      let updatedLottery = lottery;
      for (const ticket of ticketData) {
        updatedLottery = await this.lotteryRepository.purchaseTicket(lotteryId, ticket);
      }

      // Send confirmation email
      this.sendTicketPurchaseConfirmation(user, lottery, newTickets);

      return { lottery: updatedLottery, tickets: newTickets };
    } catch (error) {
      if (error instanceof ValidationException || error instanceof AuthorizationException) {
        throw error;
      }
      throw new BusinessException(`Failed to purchase ticket: ${error.message}`);
    }
  }

  /**
   * Get active lotteries
   */
  async getActiveLotteries(options = {}) {
    try {
      return await this.lotteryRepository.findActive(options);
    } catch (error) {
      throw new BusinessException(`Failed to get active lotteries: ${error.message}`);
    }
  }

  /**
   * Get lotteries by status
   */
  async getLotteriesByStatus(status, options = {}) {
    try {
      return await this.lotteryRepository.findByStatus(status, options);
    } catch (error) {
      throw new BusinessException(`Failed to get lotteries by status: ${error.message}`);
    }
  }

  /**
   * Get user's tickets
   */
  async getUserTickets(userId, options = {}) {
    try {
      return await this.lotteryRepository.getUserTickets(userId, options);
    } catch (error) {
      throw new BusinessException(`Failed to get user tickets: ${error.message}`);
    }
  }

  /**
   * Get lottery tickets
   */
  async getLotteryTickets(lotteryId, options = {}) {
    try {
      return await this.lotteryRepository.getTickets(lotteryId, options);
    } catch (error) {
      throw new BusinessException(`Failed to get lottery tickets: ${error.message}`);
    }
  }

  /**
   * Start lottery
   */
  async startLottery(id, startedBy) {
    try {
      const user = await this.userRepository.findById(startedBy);
      if (!user || user.role !== UserRole.ADMIN) {
        throw new AuthorizationException('Only admins can start lotteries');
      }

      const lottery = await this.lotteryRepository.updateStatus(id, LotteryStatus.ACTIVE);
      if (!lottery) {
        throw new ValidationException('Lottery not found');
      }

      return lottery;
    } catch (error) {
      if (error instanceof ValidationException || error instanceof AuthorizationException) {
        throw error;
      }
      throw new BusinessException(`Failed to start lottery: ${error.message}`);
    }
  }

  /**
   * End lottery and draw winner
   */
  async endLottery(id, endedBy) {
    try {
      const user = await this.userRepository.findById(endedBy);
      if (!user || user.role !== UserRole.ADMIN) {
        throw new AuthorizationException('Only admins can end lotteries');
      }

      const lottery = await this.lotteryRepository.findById(id);
      if (!lottery) {
        throw new ValidationException('Lottery not found');
      }

      if (lottery.status !== LotteryStatus.ACTIVE) {
        throw new ValidationException('Only active lotteries can be ended');
      }

      if (lottery.tickets.length === 0) {
        throw new ValidationException('Cannot end lottery with no tickets sold');
      }

      // Create lottery entity and end it
      const lotteryEntity = Lottery.fromPersistence(lottery);
      lotteryEntity.endLottery();

      // Update lottery with winner information
      const updatedLottery = await this.lotteryRepository.setWinner(
        id,
        lotteryEntity.winnerId,
        lotteryEntity.winningNumber,
        lotteryEntity.winnerUsername
      );

      // Update status to completed
      await this.lotteryRepository.updateStatus(id, LotteryStatus.COMPLETED);

      // Send winner notification
      if (lotteryEntity.winnerId) {
        this.sendWinnerNotification(lotteryEntity.winnerId, updatedLottery);
      }

      return updatedLottery;
    } catch (error) {
      if (error instanceof ValidationException || error instanceof AuthorizationException) {
        throw error;
      }
      throw new BusinessException(`Failed to end lottery: ${error.message}`);
    }
  }

  /**
   * Cancel lottery
   */
  async cancelLottery(id, cancelledBy, reason = '') {
    try {
      const user = await this.userRepository.findById(cancelledBy);
      if (!user || user.role !== UserRole.ADMIN) {
        throw new AuthorizationException('Only admins can cancel lotteries');
      }

      const lottery = await this.lotteryRepository.findById(id);
      if (!lottery) {
        throw new ValidationException('Lottery not found');
      }

      if (lottery.status === LotteryStatus.COMPLETED) {
        throw new ValidationException('Cannot cancel completed lottery');
      }

      const updatedLottery = await this.lotteryRepository.updateStatus(id, LotteryStatus.CANCELLED);

      // Send cancellation notifications to participants
      this.sendCancellationNotifications(lottery, reason);

      return updatedLottery;
    } catch (error) {
      if (error instanceof ValidationException || error instanceof AuthorizationException) {
        throw error;
      }
      throw new BusinessException(`Failed to cancel lottery: ${error.message}`);
    }
  }

  /**
   * Get ending soon lotteries
   */
  async getEndingSoonLotteries(hours = 24) {
    try {
      return await this.lotteryRepository.findEndingSoon(hours);
    } catch (error) {
      throw new BusinessException(`Failed to get ending soon lotteries: ${error.message}`);
    }
  }

  /**
   * Get lottery statistics
   */
  async getLotteryStatistics(id) {
    try {
      return await this.lotteryRepository.getStatistics(id);
    } catch (error) {
      throw new BusinessException(`Failed to get lottery statistics: ${error.message}`);
    }
  }

  /**
   * Update lottery statuses (system operation)
   */
  async updateLotteryStatuses() {
    try {
      const now = new Date();
      let stats = {
        pendingToActive: 0,
        activeToEnded: 0,
        winnersSet: 0
      };

      // Get pending lotteries that should be active
      const pendingLotteries = await this.lotteryRepository.findByStatus(LotteryStatus.PENDING);
      for (const lottery of pendingLotteries.lotteries) {
        if (lottery.startTime <= now && lottery.endTime > now) {
          await this.lotteryRepository.updateStatus(lottery.id, LotteryStatus.ACTIVE);
          stats.pendingToActive++;
        }
      }

      // Get expired lotteries
      const expiredLotteries = await this.lotteryRepository.findExpired();
      for (const lottery of expiredLotteries) {
        if (lottery.tickets.length > 0) {
          // End lottery and set winner
          const lotteryEntity = Lottery.fromPersistence(lottery);
          lotteryEntity.endLottery();

          await this.lotteryRepository.setWinner(
            lottery.id,
            lotteryEntity.winnerId,
            lotteryEntity.winningNumber,
            lotteryEntity.winnerUsername
          );

          stats.winnersSet++;

          // Send winner notification
          if (lotteryEntity.winnerId) {
            this.sendWinnerNotification(lotteryEntity.winnerId, lottery);
          }
        }

        await this.lotteryRepository.updateStatus(lottery.id, LotteryStatus.COMPLETED);
        stats.activeToEnded++;
      }

      return stats;
    } catch (error) {
      throw new BusinessException(`Failed to update lottery statuses: ${error.message}`);
    }
  }

  /**
   * Get lottery counts by status
   */
  async getLotteryCounts() {
    try {
      return await this.lotteryRepository.getCountByStatus();
    } catch (error) {
      throw new BusinessException(`Failed to get lottery counts: ${error.message}`);
    }
  }

  /**
   * Search lotteries
   */
  async searchLotteries(query, options = {}) {
    try {
      return await this.lotteryRepository.search(query, options);
    } catch (error) {
      throw new BusinessException(`Failed to search lotteries: ${error.message}`);
    }
  }

  /**
   * Send ticket purchase confirmation
   */
  async sendTicketPurchaseConfirmation(user, lottery, tickets) {
    try {
      if (user.email) {
        await this.emailService.sendNotificationEmail(
          user.email,
          'Lottery Ticket Purchase Confirmation',
          `You have successfully purchased ${tickets.length} ticket(s) for lottery "${lottery.title}". Good luck!`
        );
      }
    } catch (error) {
      console.error(`Failed to send purchase confirmation: ${error.message}`);
    }
  }

  /**
   * Send winner notification
   */
  async sendWinnerNotification(userId, lottery) {
    try {
      const user = await this.userRepository.findById(userId);
      if (user && user.email) {
        await this.emailService.sendLotteryWonEmail(user.email, {
          name: user.name,
          lotteryTitle: lottery.title,
          winningNumber: lottery.winningNumber,
          prize: lottery.totalPrize
        });
      }
    } catch (error) {
      console.error(`Failed to send winner notification: ${error.message}`);
    }
  }

  /**
   * Send cancellation notifications
   */
  async sendCancellationNotifications(lottery, reason) {
    try {
      const participantIds = [...new Set(lottery.tickets.map(ticket => ticket.userId))];
      
      for (const userId of participantIds) {
        const user = await this.userRepository.findById(userId);
        if (user && user.email) {
          await this.emailService.sendNotificationEmail(
            user.email,
            'Lottery Cancelled',
            `The lottery "${lottery.title}" has been cancelled. ${reason ? `Reason: ${reason}` : ''} Your tickets will be refunded.`
          );
        }
      }
    } catch (error) {
      console.error(`Failed to send cancellation notifications: ${error.message}`);
    }
  }
}

module.exports = LotteryService;
