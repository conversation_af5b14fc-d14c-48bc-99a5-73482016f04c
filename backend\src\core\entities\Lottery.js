/**
 * Lottery Entity (Domain Object)
 * <PERSON>ş mantığından bağımsız çekiliş varlığı
 */

const { LotteryStatus } = require('../../shared/enums');
const { LOTTERY, VALIDATION } = require('../../shared/constants');

class Lottery {
  constructor({
    id = null,
    title,
    description,
    ticketPrice,
    maxTickets,
    numbers = [],
    tickets = [],
    startTime,
    endTime,
    status = LotteryStatus.PENDING,
    winnerId = null,
    winningNumber = null,
    winnerUsername = null,
    drawTime = null,
    totalPrize = 0,
    createdAt = new Date(),
    updatedAt = new Date()
  }) {
    this.id = id;
    this.title = title;
    this.description = description;
    this.ticketPrice = ticketPrice;
    this.maxTickets = maxTickets;
    this.numbers = numbers.length > 0 ? numbers : this.generateNumbers();
    this.tickets = tickets;
    this.startTime = new Date(startTime);
    this.endTime = new Date(endTime);
    this.status = status;
    this.winnerId = winnerId;
    this.winningNumber = winningNumber;
    this.winnerUsername = winnerUsername;
    this.drawTime = drawTime;
    this.totalPrize = totalPrize;
    this.createdAt = createdAt;
    this.updatedAt = updatedAt;

    // Validation
    this.validate();
  }

  /**
   * Entity validation
   */
  validate() {
    if (!this.title || this.title.length < VALIDATION.NAME_MIN_LENGTH) {
      throw new Error(`Title must be at least ${VALIDATION.NAME_MIN_LENGTH} characters`);
    }

    if (this.title.length > VALIDATION.TITLE_MAX_LENGTH) {
      throw new Error(`Title cannot exceed ${VALIDATION.TITLE_MAX_LENGTH} characters`);
    }

    if (!this.description || this.description.length > VALIDATION.DESCRIPTION_MAX_LENGTH) {
      throw new Error(`Description cannot exceed ${VALIDATION.DESCRIPTION_MAX_LENGTH} characters`);
    }

    if (this.ticketPrice <= 0) {
      throw new Error('Ticket price must be greater than 0');
    }

    if (this.maxTickets <= 0) {
      throw new Error('Max tickets must be greater than 0');
    }

    if (this.endTime <= this.startTime) {
      throw new Error('End time must be after start time');
    }

    if (!Object.values(LotteryStatus).includes(this.status)) {
      throw new Error('Invalid lottery status');
    }

    // Numbers validation
    if (this.numbers.length !== LOTTERY.MIN_NUMBERS) {
      throw new Error(`Lottery must have exactly ${LOTTERY.MIN_NUMBERS} numbers`);
    }

    // Check number uniqueness and range
    const uniqueNumbers = new Set(this.numbers);
    if (uniqueNumbers.size !== this.numbers.length) {
      throw new Error('All lottery numbers must be unique');
    }

    this.numbers.forEach(number => {
      if (number < LOTTERY.NUMBER_RANGE_MIN || number > LOTTERY.NUMBER_RANGE_MAX) {
        throw new Error(`Numbers must be between ${LOTTERY.NUMBER_RANGE_MIN} and ${LOTTERY.NUMBER_RANGE_MAX}`);
      }
    });

    // Check minimum difference between numbers
    this.validateNumberDifferences();
  }

  /**
   * Sayılar arası minimum fark kontrolü
   */
  validateNumberDifferences() {
    const sortedNumbers = [...this.numbers].sort((a, b) => a - b);
    
    for (let i = 1; i < sortedNumbers.length; i++) {
      const difference = sortedNumbers[i] - sortedNumbers[i - 1];
      if (difference < LOTTERY.MIN_DIFFERENCE) {
        throw new Error(`Minimum difference between numbers must be ${LOTTERY.MIN_DIFFERENCE}`);
      }
    }
  }

  /**
   * Çekilişin aktif olup olmadığını kontrol et
   */
  isActive() {
    const now = new Date();
    return this.status === LotteryStatus.ACTIVE && 
           now >= this.startTime && 
           now <= this.endTime;
  }

  /**
   * Çekilişin başlamış olup olmadığını kontrol et
   */
  hasStarted() {
    return new Date() >= this.startTime;
  }

  /**
   * Çekilişin bitmiş olup olmadığını kontrol et
   */
  hasEnded() {
    return new Date() > this.endTime;
  }

  /**
   * Bilet satın alınabilir mi kontrol et
   */
  canPurchaseTicket() {
    return this.isActive() && 
           !this.hasEnded() && 
           this.tickets.length < this.maxTickets;
  }

  /**
   * Kullanıcının kaç bilet aldığını kontrol et
   */
  getUserTicketCount(userId) {
    return this.tickets.filter(ticket => ticket.userId === userId).length;
  }

  /**
   * Kullanıcı bilet satın alabilir mi kontrol et
   */
  canUserPurchaseTicket(userId, quantity = 1) {
    if (!this.canPurchaseTicket()) {
      return false;
    }

    const userTicketCount = this.getUserTicketCount(userId);
    return (userTicketCount + quantity) <= LOTTERY.MAX_TICKETS_PER_USER;
  }

  /**
   * Bilet satın al
   */
  purchaseTicket(userId, username, quantity = 1) {
    if (!this.canUserPurchaseTicket(userId, quantity)) {
      throw new Error('Cannot purchase ticket');
    }

    if (this.tickets.length + quantity > this.maxTickets) {
      throw new Error('Not enough tickets available');
    }

    // Mevcut biletleri kontrol et (duplicate önleme)
    const availableNumbers = this.getAvailableNumbers();
    
    if (availableNumbers.length < quantity) {
      throw new Error('Not enough unique tickets available');
    }

    // Bilet(ler) oluştur
    const newTickets = [];
    for (let i = 0; i < quantity; i++) {
      const ticketNumbers = [...this.numbers]; // Tüm sayıları içeren bilet
      const ticket = {
        userId,
        username,
        numbers: ticketNumbers,
        purchaseTime: new Date(),
        ticketId: this.generateTicketId()
      };
      
      newTickets.push(ticket);
      this.tickets.push(ticket);
    }

    this.totalPrize += this.ticketPrice * quantity;
    this.updatedAt = new Date();

    return newTickets;
  }

  /**
   * Mevcut bilet sayılarını getir
   */
  getAvailableNumbers() {
    // Her bilet tüm sayıları içerdiği için, maksimum bilet sayısına kadar satılabilir
    const soldTickets = this.tickets.length;
    const availableTickets = this.maxTickets - soldTickets;
    
    // Her bilet için aynı sayı seti kullanılır
    const availableNumbers = [];
    for (let i = 0; i < availableTickets; i++) {
      availableNumbers.push([...this.numbers]);
    }
    
    return availableNumbers;
  }

  /**
   * Çekilişi başlat
   */
  start() {
    if (this.status !== LotteryStatus.PENDING) {
      throw new Error('Only pending lotteries can be started');
    }

    this.status = LotteryStatus.ACTIVE;
    this.updatedAt = new Date();
  }

  /**
   * Çekilişi bitir ve kazanan belirle
   */
  end() {
    if (this.status !== LotteryStatus.ACTIVE) {
      throw new Error('Only active lotteries can be ended');
    }

    if (this.tickets.length === 0) {
      throw new Error('Cannot end lottery with no tickets sold');
    }

    // Kazanan numarayı seç
    this.winningNumber = this.selectWinningNumber();
    
    // Kazananı belirle
    const winner = this.findWinner(this.winningNumber);
    if (winner) {
      this.winnerId = winner.userId;
      this.winnerUsername = winner.username;
    }

    this.status = LotteryStatus.COMPLETED;
    this.drawTime = new Date();
    this.updatedAt = new Date();
  }

  /**
   * Çekilişi iptal et
   */
  cancel() {
    if (this.status === LotteryStatus.COMPLETED) {
      throw new Error('Cannot cancel completed lottery');
    }

    this.status = LotteryStatus.CANCELLED;
    this.updatedAt = new Date();
  }

  /**
   * Kazanan numarayı seç
   */
  selectWinningNumber() {
    const randomIndex = Math.floor(Math.random() * this.numbers.length);
    return this.numbers[randomIndex];
  }

  /**
   * Kazananı bul
   */
  findWinner(winningNumber) {
    // Tüm biletler aynı sayıları içerdiği için, rastgele bir bilet seç
    if (this.tickets.length === 0) {
      return null;
    }

    const randomTicketIndex = Math.floor(Math.random() * this.tickets.length);
    return this.tickets[randomTicketIndex];
  }

  /**
   * Kullanıcının biletlerini getir
   */
  getUserTickets(userId) {
    return this.tickets.filter(ticket => ticket.userId === userId);
  }

  /**
   * Çekiliş sayılarını oluştur
   */
  generateNumbers() {
    const numbers = [];
    const usedNumbers = new Set();

    while (numbers.length < LOTTERY.MIN_NUMBERS) {
      const number = Math.floor(
        Math.random() * (LOTTERY.NUMBER_RANGE_MAX - LOTTERY.NUMBER_RANGE_MIN + 1)
      ) + LOTTERY.NUMBER_RANGE_MIN;

      if (!usedNumbers.has(number)) {
        // Minimum fark kontrolü
        const isValidNumber = numbers.every(existingNumber => 
          Math.abs(number - existingNumber) >= LOTTERY.MIN_DIFFERENCE
        );

        if (isValidNumber) {
          numbers.push(number);
          usedNumbers.add(number);
        }
      }
    }

    return numbers.sort((a, b) => a - b);
  }

  /**
   * Bilet ID oluştur
   */
  generateTicketId() {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9);
  }

  /**
   * Entity'yi plain object'e çevir
   */
  toJSON() {
    return {
      id: this.id,
      title: this.title,
      description: this.description,
      ticketPrice: this.ticketPrice,
      maxTickets: this.maxTickets,
      numbers: this.numbers,
      tickets: this.tickets,
      startTime: this.startTime,
      endTime: this.endTime,
      status: this.status,
      winnerId: this.winnerId,
      winningNumber: this.winningNumber,
      winnerUsername: this.winnerUsername,
      drawTime: this.drawTime,
      totalPrize: this.totalPrize,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }

  /**
   * Entity'yi database için hazırla
   */
  toPersistence() {
    return {
      _id: this.id,
      title: this.title,
      description: this.description,
      ticketPrice: this.ticketPrice,
      maxTickets: this.maxTickets,
      numbers: this.numbers,
      tickets: this.tickets,
      startTime: this.startTime,
      endTime: this.endTime,
      status: this.status,
      winnerId: this.winnerId,
      winningNumber: this.winningNumber,
      winnerUsername: this.winnerUsername,
      drawTime: this.drawTime,
      totalPrize: this.totalPrize,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    };
  }

  /**
   * Database'den entity oluştur
   */
  static fromPersistence(data) {
    return new Lottery({
      id: data._id,
      title: data.title,
      description: data.description,
      ticketPrice: data.ticketPrice,
      maxTickets: data.maxTickets,
      numbers: data.numbers,
      tickets: data.tickets,
      startTime: data.startTime,
      endTime: data.endTime,
      status: data.status,
      winnerId: data.winnerId,
      winningNumber: data.winningNumber,
      winnerUsername: data.winnerUsername,
      drawTime: data.drawTime,
      totalPrize: data.totalPrize,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt
    });
  }
}

module.exports = Lottery;
