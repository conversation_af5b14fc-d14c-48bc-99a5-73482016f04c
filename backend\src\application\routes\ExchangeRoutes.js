/**
 * Exchange Routes Configuration
 * Defines all exchange/barter related API endpoints
 */

const express = require('express');
const ErrorMiddleware = require('../middleware/ErrorMiddleware');

class ExchangeRoutes {
  constructor(container) {
    this.container = container;
    this.router = express.Router();
    this.setupRoutes();
  }

  setupRoutes() {
    const exchangeController = new (require('../controllers/ExchangeController'))(this.container);
    const authMiddleware = this.container.resolve('authMiddleware');

    // Public routes
    this.router.get('/products', 
      ErrorMiddleware.asyncHandler(exchangeController.getExchangeProducts.bind(exchangeController))
    );

    this.router.get('/products/:id', 
      ErrorMiddleware.asyncHandler(exchangeController.getExchangeProductById.bind(exchangeController))
    );

    // Protected routes (authentication required)
    this.router.post('/products', 
      authMiddleware.authenticate(),
      ErrorMiddleware.asyncHandler(exchangeController.createExchangeProduct.bind(exchangeController))
    );

    this.router.put('/products/:id', 
      authMiddleware.authenticate(),
      ErrorMiddleware.asyncHandler(exchangeController.updateExchangeProduct.bind(exchangeController))
    );

    this.router.delete('/products/:id', 
      authMiddleware.authenticate(),
      ErrorMiddleware.asyncHandler(exchangeController.deleteExchangeProduct.bind(exchangeController))
    );

    this.router.get('/user/products', 
      authMiddleware.authenticate(),
      ErrorMiddleware.asyncHandler(exchangeController.getUserExchangeProducts.bind(exchangeController))
    );
  }

  getRouter() {
    return this.router;
  }
}

module.exports = ExchangeRoutes;
