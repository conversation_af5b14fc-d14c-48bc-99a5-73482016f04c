/**
 * Auction Service
 * Business logic for auction operations
 */

const { BusinessException, ValidationException, AuthorizationException } = require('../../core/exceptions');
const { AuctionStatus, UserRole } = require('../../shared/enums');
const Auction = require('../../core/entities/Auction');

class AuctionService {
  constructor(auctionRepository, userRepository, productRepository, emailService) {
    this.auctionRepository = auctionRepository;
    this.userRepository = userRepository;
    this.productRepository = productRepository;
    this.emailService = emailService;
  }

  /**
   * Get all auctions
   */
  async getAllAuctions(options = {}) {
    try {
      return await this.auctionRepository.findAll(options);
    } catch (error) {
      throw new BusinessException(`Failed to get auctions: ${error.message}`);
    }
  }

  /**
   * Get auction by ID
   */
  async getAuctionById(id) {
    try {
      const auction = await this.auctionRepository.findById(id);
      if (!auction) {
        throw new ValidationException('Auction not found');
      }
      return auction;
    } catch (error) {
      if (error instanceof ValidationException) throw error;
      throw new BusinessException(`Failed to get auction: ${error.message}`);
    }
  }

  /**
   * Create new auction
   */
  async createAuction(auctionData, createdBy) {
    try {
      // Validate user permissions
      const user = await this.userRepository.findById(createdBy);
      if (!user) {
        throw new ValidationException('User not found');
      }

      if (user.role !== UserRole.ADMIN && user.role !== UserRole.SELLER) {
        throw new AuthorizationException('Only admins and sellers can create auctions');
      }

      // Validate product exists
      if (auctionData.productId) {
        const product = await this.productRepository.findById(auctionData.productId);
        if (!product) {
          throw new ValidationException('Product not found');
        }
      }

      // Create auction entity
      const auction = new Auction({
        ...auctionData,
        sellerId: createdBy,
        metadata: {
          createdBy
        }
      });

      // Save to repository
      const savedAuction = await this.auctionRepository.create(auction.toPersistence());

      return savedAuction;
    } catch (error) {
      if (error instanceof ValidationException || error instanceof AuthorizationException) {
        throw error;
      }
      throw new BusinessException(`Failed to create auction: ${error.message}`);
    }
  }

  /**
   * Update auction
   */
  async updateAuction(id, updateData, updatedBy) {
    try {
      const auction = await this.auctionRepository.findById(id);
      if (!auction) {
        throw new ValidationException('Auction not found');
      }

      // Check permissions
      const user = await this.userRepository.findById(updatedBy);
      if (!user) {
        throw new ValidationException('User not found');
      }

      if (user.role !== UserRole.ADMIN && auction.sellerId !== updatedBy) {
        throw new AuthorizationException('You can only update your own auctions');
      }

      // Validate auction can be updated
      if (auction.status === AuctionStatus.COMPLETED || auction.status === AuctionStatus.CANCELLED) {
        throw new ValidationException('Cannot update completed or cancelled auction');
      }

      // Update metadata
      updateData.metadata = {
        ...auction.metadata,
        updatedBy,
        updatedAt: new Date()
      };

      const updatedAuction = await this.auctionRepository.update(id, updateData);
      return updatedAuction;
    } catch (error) {
      if (error instanceof ValidationException || error instanceof AuthorizationException) {
        throw error;
      }
      throw new BusinessException(`Failed to update auction: ${error.message}`);
    }
  }

  /**
   * Delete auction
   */
  async deleteAuction(id, deletedBy) {
    try {
      const auction = await this.auctionRepository.findById(id);
      if (!auction) {
        throw new ValidationException('Auction not found');
      }

      // Check permissions
      const user = await this.userRepository.findById(deletedBy);
      if (!user) {
        throw new ValidationException('User not found');
      }

      if (user.role !== UserRole.ADMIN && auction.sellerId !== deletedBy) {
        throw new AuthorizationException('You can only delete your own auctions');
      }

      // Validate auction can be deleted
      if (auction.status === AuctionStatus.ACTIVE && auction.bids.length > 0) {
        throw new ValidationException('Cannot delete active auction with bids');
      }

      const success = await this.auctionRepository.delete(id);
      if (!success) {
        throw new BusinessException('Failed to delete auction');
      }

      return true;
    } catch (error) {
      if (error instanceof ValidationException || error instanceof AuthorizationException) {
        throw error;
      }
      throw new BusinessException(`Failed to delete auction: ${error.message}`);
    }
  }

  /**
   * Place bid on auction
   */
  async placeBid(auctionId, userId, amount, userName) {
    try {
      // Get auction
      const auction = await this.auctionRepository.findById(auctionId);
      if (!auction) {
        throw new ValidationException('Auction not found');
      }

      // Get user
      const user = await this.userRepository.findById(userId);
      if (!user) {
        throw new ValidationException('User not found');
      }

      // Check user permissions
      if (!user.canBid) {
        throw new AuthorizationException('You do not have permission to place bids');
      }

      if (!user.isActive()) {
        throw new AuthorizationException('Your account is not active');
      }

      // Check if user is the seller
      if (auction.sellerId === userId) {
        throw new ValidationException('You cannot bid on your own auction');
      }

      // Create auction entity and place bid
      const auctionEntity = Auction.fromPersistence(auction);
      auctionEntity.placeBid(userId, amount, userName);

      // Save updated auction
      const bidData = {
        userId,
        amount,
        name: userName,
        timestamp: new Date()
      };

      const updatedAuction = await this.auctionRepository.addBid(auctionId, bidData);

      // Send notification to previous highest bidder (if any)
      if (auction.bids.length > 0) {
        const previousHighestBid = auction.bids[auction.bids.length - 1];
        if (previousHighestBid.userId !== userId) {
          // Send outbid notification
          this.sendOutbidNotification(previousHighestBid.userId, auction, amount);
        }
      }

      return updatedAuction;
    } catch (error) {
      if (error instanceof ValidationException || error instanceof AuthorizationException) {
        throw error;
      }
      throw new BusinessException(`Failed to place bid: ${error.message}`);
    }
  }

  /**
   * Get active auctions
   */
  async getActiveAuctions(options = {}) {
    try {
      return await this.auctionRepository.findActive(options);
    } catch (error) {
      throw new BusinessException(`Failed to get active auctions: ${error.message}`);
    }
  }

  /**
   * Get user's auctions
   */
  async getUserAuctions(userId, options = {}) {
    try {
      return await this.auctionRepository.findBySellerId(userId, options);
    } catch (error) {
      throw new BusinessException(`Failed to get user auctions: ${error.message}`);
    }
  }

  /**
   * Get user's bids
   */
  async getUserBids(userId, options = {}) {
    try {
      return await this.auctionRepository.getUserBids(userId, options);
    } catch (error) {
      throw new BusinessException(`Failed to get user bids: ${error.message}`);
    }
  }

  /**
   * Get auction bids
   */
  async getAuctionBids(auctionId, options = {}) {
    try {
      return await this.auctionRepository.getBids(auctionId, options);
    } catch (error) {
      throw new BusinessException(`Failed to get auction bids: ${error.message}`);
    }
  }

  /**
   * Search auctions
   */
  async searchAuctions(query, options = {}) {
    try {
      return await this.auctionRepository.search(query, options);
    } catch (error) {
      throw new BusinessException(`Failed to search auctions: ${error.message}`);
    }
  }

  /**
   * Get ending soon auctions
   */
  async getEndingSoonAuctions(hours = 24) {
    try {
      return await this.auctionRepository.findEndingSoon(hours);
    } catch (error) {
      throw new BusinessException(`Failed to get ending soon auctions: ${error.message}`);
    }
  }

  /**
   * Update auction status
   */
  async updateAuctionStatus(id, status, updatedBy) {
    try {
      const user = await this.userRepository.findById(updatedBy);
      if (!user || user.role !== UserRole.ADMIN) {
        throw new AuthorizationException('Only admins can update auction status');
      }

      const auction = await this.auctionRepository.updateStatus(id, status);
      if (!auction) {
        throw new ValidationException('Auction not found');
      }

      return auction;
    } catch (error) {
      if (error instanceof ValidationException || error instanceof AuthorizationException) {
        throw error;
      }
      throw new BusinessException(`Failed to update auction status: ${error.message}`);
    }
  }

  /**
   * Get auction statistics
   */
  async getAuctionStatistics(id) {
    try {
      return await this.auctionRepository.getStatistics(id);
    } catch (error) {
      throw new BusinessException(`Failed to get auction statistics: ${error.message}`);
    }
  }

  /**
   * Increment view count
   */
  async incrementViewCount(id) {
    try {
      await this.auctionRepository.incrementViewCount(id);
    } catch (error) {
      // Don't throw error for view count increment
      console.error(`Failed to increment view count: ${error.message}`);
    }
  }

  /**
   * Update auction statuses (system operation)
   */
  async updateAuctionStatuses() {
    try {
      const now = new Date();
      let stats = {
        pendingToActive: 0,
        activeToEnded: 0,
        winnersSet: 0
      };

      // Get pending auctions that should be active
      const pendingAuctions = await this.auctionRepository.findByStatus(AuctionStatus.PENDING);
      for (const auction of pendingAuctions.auctions) {
        if (auction.startTime <= now && auction.endTime > now) {
          await this.auctionRepository.updateStatus(auction.id, AuctionStatus.ACTIVE);
          stats.pendingToActive++;
        }
      }

      // Get expired auctions
      const expiredAuctions = await this.auctionRepository.findExpired();
      for (const auction of expiredAuctions) {
        await this.auctionRepository.updateStatus(auction.id, AuctionStatus.COMPLETED);
        stats.activeToEnded++;

        // Set winner if there are bids
        if (auction.bids.length > 0) {
          const winningBid = auction.getHighestBid();
          await this.auctionRepository.update(auction.id, {
            winnerId: winningBid.userId,
            winningBid: winningBid
          });
          stats.winnersSet++;

          // Send winner notification
          this.sendWinnerNotification(winningBid.userId, auction);
        }
      }

      return stats;
    } catch (error) {
      throw new BusinessException(`Failed to update auction statuses: ${error.message}`);
    }
  }

  /**
   * Get auction counts by status
   */
  async getAuctionCounts(sellerId = null) {
    try {
      return await this.auctionRepository.getCountByStatus(sellerId);
    } catch (error) {
      throw new BusinessException(`Failed to get auction counts: ${error.message}`);
    }
  }

  /**
   * Send outbid notification
   */
  async sendOutbidNotification(userId, auction, newBidAmount) {
    try {
      const user = await this.userRepository.findById(userId);
      if (user && user.email) {
        await this.emailService.sendNotificationEmail(
          user.email,
          'You have been outbid',
          `Your bid on "${auction.title}" has been outbid with a new bid of $${newBidAmount}.`
        );
      }
    } catch (error) {
      console.error(`Failed to send outbid notification: ${error.message}`);
    }
  }

  /**
   * Send winner notification
   */
  async sendWinnerNotification(userId, auction) {
    try {
      const user = await this.userRepository.findById(userId);
      if (user && user.email) {
        await this.emailService.sendAuctionWonEmail(user.email, {
          name: user.name,
          auctionTitle: auction.title,
          winningBid: auction.currentPrice,
          productName: auction.title
        });
      }
    } catch (error) {
      console.error(`Failed to send winner notification: ${error.message}`);
    }
  }
}

module.exports = AuctionService;
