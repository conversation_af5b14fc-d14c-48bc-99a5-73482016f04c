/**
 * Authenticate User Use Case
 * Core business logic for user authentication
 */

const {
  InvalidCredentialsException,
  UserNotActiveException,
  UserNotApprovedException,
  ValidationException
} = require('../../exceptions');
const { UserStatus, ApprovalStatus } = require('../../../shared/enums');

class AuthenticateUserUseCase {
  constructor(userRepository, passwordService, tokenService) {
    this.userRepository = userRepository;
    this.passwordService = passwordService;
    this.tokenService = tokenService;
  }

  /**
   * Kullanıcı kimlik doğrulama
   * @param {string} emailOrUsername - Email veya kullanıcı adı
   * @param {string} password - Şifre
   * @returns {Promise<{user: Object, token: string}>} - Kullan<PERSON>c<PERSON> ve token
   */
  async execute(emailOrUsername, password) {
    // Input validation
    this.validateInput(emailOrUsername, password);

    // Find user by email or username
    const user = await this.findUser(emailOrUsername);

    // Verify password
    await this.verifyPassword(user, password);

    // Check user status and permissions
    this.checkUserPermissions(user);

    // Update last login
    await this.updateLastLogin(user);

    // Generate JWT token
    const token = this.tokenService.generateAccessToken({
      id: user.id,
      email: user.email,
      username: user.username,
      role: user.role
    });

    // Return user data and token
    console.log('🔍 User before toJSON():', user);
    const userJson = user.toJSON();
    console.log('🔍 User after toJSON():', userJson);

    return {
      user: userJson,
      token
    };
  }

  /**
   * Input validation
   */
  validateInput(emailOrUsername, password) {
    if (!emailOrUsername) {
      throw new ValidationException('Email or username is required');
    }

    if (!password) {
      throw new ValidationException('Password is required');
    }

    if (emailOrUsername.length < 3) {
      throw new ValidationException('Email or username must be at least 3 characters');
    }

    if (password.length < 6) {
      throw new ValidationException('Password must be at least 6 characters');
    }
  }

  /**
   * Find user by email or username
   */
  async findUser(emailOrUsername) {
    const identifier = emailOrUsername.toLowerCase();

    // Try to find by email first
    let user = await this.userRepository.findByEmail(identifier);

    // If not found by email, try username
    if (!user) {
      user = await this.userRepository.findByUsername(identifier);
    }

    if (!user) {
      throw new InvalidCredentialsException();
    }

    return user;
  }

  /**
   * Verify password
   */
  async verifyPassword(user, password) {
    const isPasswordValid = await this.passwordService.compare(password, user.password);

    if (!isPasswordValid) {
      throw new InvalidCredentialsException();
    }
  }

  /**
   * Check user permissions and status
   */
  checkUserPermissions(user) {
    // Check if user is active
    if (user.status !== UserStatus.ACTIVE) {
      throw new UserNotActiveException();
    }

    // Check if user is approved
    if (user.approvalStatus !== ApprovalStatus.APPROVED) {
      throw new UserNotApprovedException();
    }
  }

  /**
   * Update last login timestamp
   */
  async updateLastLogin(user) {
    try {
      user.lastLogin = new Date();
      await this.userRepository.update(user.id, user);
    } catch (error) {
      // Log error but don't fail authentication
      console.error('Failed to update last login:', error);
    }
  }
}

module.exports = AuthenticateUserUseCase;
