/**
 * Lottery Routes Configuration
 * Defines all lottery-related API endpoints
 */

const express = require('express');
const ErrorMiddleware = require('../middleware/ErrorMiddleware');

class LotteryRoutes {
  constructor(container) {
    this.container = container;
    this.router = express.Router();
    this.setupRoutes();
  }

  setupRoutes() {
    const lotteryController = this.container.resolve('lotteryController');
    const authMiddleware = this.container.resolve('authMiddleware');

    // Public routes (no authentication required)

    // Get all lotteries with filtering and pagination
    this.router.get('/',
      ErrorMiddleware.asyncHandler(lotteryController.getAllLotteries.bind(lotteryController))
    );

    // Get active lotteries
    this.router.get('/active',
      ErrorMiddleware.asyncHandler(lotteryController.getActiveLotteries.bind(lotteryController))
    );

    // Get ending soon lotteries
    this.router.get('/ending-soon',
      ErrorMiddleware.asyncHandler(lotteryController.getEndingSoonLotteries.bind(lotteryController))
    );

    // Get lottery by ID
    this.router.get('/:id',
      ErrorMiddleware.asyncHandler(lotteryController.getLotteryById.bind(lotteryController))
    );

    // Get lottery tickets (public for transparency)
    this.router.get('/:id/tickets',
      ErrorMiddleware.asyncHandler(lotteryController.getLotteryTickets.bind(lotteryController))
    );

    // Get lottery statistics (public)
    this.router.get('/:id/statistics',
      ErrorMiddleware.asyncHandler(lotteryController.getLotteryStatistics.bind(lotteryController))
    );

    // Search lotteries
    this.router.get('/search/query',
      ErrorMiddleware.asyncHandler(lotteryController.searchLotteries.bind(lotteryController))
    );

    // Protected routes (authentication required)

    // Purchase lottery ticket (requires lottery permission)
    this.router.post('/:id/purchase',
      authMiddleware.authenticate(),
      authMiddleware.requireLotteryPermission(),
      ErrorMiddleware.asyncHandler(lotteryController.purchaseTicket.bind(lotteryController))
    );

    // Get user's lottery tickets
    this.router.get('/user/my-tickets',
      authMiddleware.authenticate(),
      ErrorMiddleware.asyncHandler(lotteryController.getUserTickets.bind(lotteryController))
    );

    // Admin routes (admin only)

    // Create new lottery
    this.router.post('/',
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(lotteryController.createLottery.bind(lotteryController))
    );

    // Update lottery
    this.router.put('/:id',
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(lotteryController.updateLottery.bind(lotteryController))
    );

    // Delete lottery
    this.router.delete('/:id',
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(lotteryController.deleteLottery.bind(lotteryController))
    );

    // Start lottery
    this.router.post('/:id/start',
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(lotteryController.startLottery.bind(lotteryController))
    );

    // End lottery and draw winner
    this.router.post('/:id/end',
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(lotteryController.endLottery.bind(lotteryController))
    );

    // Cancel lottery
    this.router.post('/:id/cancel',
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(lotteryController.cancelLottery.bind(lotteryController))
    );

    // Get lottery counts by status
    this.router.get('/admin/counts',
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(lotteryController.getLotteryCounts.bind(lotteryController))
    );

    // System operation: Update lottery statuses
    this.router.post('/admin/update-statuses',
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(lotteryController.updateLotteryStatuses.bind(lotteryController))
    );
  }

  getRouter() {
    return this.router;
  }
}

module.exports = LotteryRoutes;
