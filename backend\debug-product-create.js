const mongoose = require('mongoose');
require('dotenv').config();

async function debugProductCreate() {
  try {
    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb+srv://emrevaryemez22:<EMAIL>/robinhoot?retryWrites=true&w=majority');
    console.log('✅ Connected to database');

    // Get database
    const db = mongoose.connection.db;

    // Get categories to use for product
    const categories = await db.collection('categories').find({}).toArray();
    console.log('📂 Available categories:');
    categories.forEach(cat => {
      console.log(`  - ${cat.name} (${cat.slug}) - ID: ${cat._id}`);
    });

    const testCategoryId = categories.find(cat => cat.slug === 'elektronik')?._id || categories[0]?._id;
    console.log(`\n🎯 Using category ID: ${testCategoryId}`);

    // Get admin user
    const adminUser = await db.collection('users').findOne({ email: '<EMAIL>' });
    console.log(`👤 Admin user ID: ${adminUser._id}`);

    // Create test product directly in database
    const testProduct = {
      name: 'Debug Test Product',
      description: 'Debug test için oluşturulan ürün',
      shortDescription: 'Debug test ürünü',
      price: 1500,
      originalPrice: 2000,
      discountPercentage: 25,
      categoryId: testCategoryId,
      sellerId: adminUser._id,
      images: [
        {
          url: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500',
          alt: 'Debug Test Product Image',
          isPrimary: true
        }
      ],
      stock: 100,
      lowStockThreshold: 10,
      specifications: {
        'Marka': 'Debug Brand',
        'Model': 'DEBUG-2024',
        'Renk': 'Siyah',
        'Garanti': '2 Yıl'
      },
      tags: ['debug', 'test'],
      featured: true,
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    console.log('\n📦 Creating product directly in database...');
    console.log('Product data:', JSON.stringify(testProduct, null, 2));

    const result = await db.collection('products').insertOne(testProduct);
    console.log('✅ Product created with ID:', result.insertedId);

    // Verify product was created
    const createdProduct = await db.collection('products').findOne({ _id: result.insertedId });
    if (createdProduct) {
      console.log('\n✅ Product verification successful');
      console.log('Product name:', createdProduct.name);
      console.log('Product _id:', createdProduct._id);
      console.log('Product categoryId:', createdProduct.categoryId);
      console.log('Product sellerId:', createdProduct.sellerId);
    }

    // Now test with ProductRepository
    console.log('\n🔧 Testing with ProductRepository...');
    
    // Import ProductRepository
    const ProductRepository = require('./src/infrastructure/database/repositories/ProductRepository');
    const productRepo = new ProductRepository();

    try {
      const repoProduct = await productRepo.create({
        name: 'Repo Test Product',
        description: 'Repository test için oluşturulan ürün',
        shortDescription: 'Repo test ürünü',
        price: 1800,
        originalPrice: 2200,
        discountPercentage: 18,
        categoryId: testCategoryId.toString(),
        sellerId: adminUser._id.toString(),
        images: [
          {
            url: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500',
            alt: 'Repo Test Product Image',
            isPrimary: true
          }
        ],
        stock: 50,
        lowStockThreshold: 5,
        specifications: {
          'Marka': 'Repo Brand',
          'Model': 'REPO-2024'
        },
        tags: ['repo', 'test'],
        featured: false,
        status: 'active'
      });

      console.log('✅ ProductRepository.create successful');
      console.log('Created product ID:', repoProduct.id);
      console.log('Created product name:', repoProduct.name);

    } catch (repoError) {
      console.error('❌ ProductRepository.create failed:', repoError.message);
      console.error('Stack:', repoError.stack);
    }

    await mongoose.disconnect();
    console.log('✅ Disconnected from database');

  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

debugProductCreate();
