/**
 * Main Application Bootstrap
 * Clean Architecture implementation with Dependency Injection
 */

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const cookieParser = require('cookie-parser');
const rateLimit = require('express-rate-limit');

// Infrastructure
const ServiceRegistration = require('./infrastructure/container/ServiceRegistration');
const RouteConfiguration = require('./application/routes');
const ErrorMiddleware = require('./application/middleware/ErrorMiddleware');
const { RATE_LIMIT } = require('./shared/constants');

class Application {
  constructor() {
    this.app = express();
    this.container = null;
    this.setupContainer();
    this.setupMiddleware();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  /**
   * Setup Dependency Injection Container
   */
  setupContainer() {
    this.container = ServiceRegistration.bootstrap();
    console.log('✅ Dependency Injection Container initialized');
    console.log('📦 Registered services:', this.container.getRegisteredServices());
  }

  /**
   * Setup Express middleware
   */
  setupMiddleware() {
    // Security middleware
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
        },
      },
      crossOriginEmbedderPolicy: false
    }));

    // CORS configuration
    this.app.use(cors({
      origin: process.env.FRONTEND_URL || 'http://localhost:3000',
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization']
    }));

    // Rate limiting
    const limiter = rateLimit({
      windowMs: RATE_LIMIT.WINDOW_MS,
      max: RATE_LIMIT.MAX_REQUESTS,
      message: {
        success: false,
        message: 'Too many requests, please try again later'
      },
      standardHeaders: true,
      legacyHeaders: false
    });
    this.app.use('/api/', limiter);

    // Body parsing middleware
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));
    this.app.use(cookieParser());

    // Compression
    this.app.use(compression());

    // Request logging (development)
    if (process.env.NODE_ENV === 'development') {
      this.app.use((req, res, next) => {
        console.log(`${req.method} ${req.path} - ${new Date().toISOString()}`);
        next();
      });
    }

    console.log('✅ Express middleware configured');
  }

  /**
   * Setup application routes
   */
  setupRoutes() {
    // Health check (before other routes)
    this.app.get('/health', (req, res) => {
      res.json({
        success: true,
        message: 'Server is healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV,
        version: process.env.npm_package_version || '1.0.0'
      });
    });

    // Main API routes
    const routeConfig = new RouteConfiguration(this.container);
    this.app.use('/', routeConfig.getRouter());

    // Legacy routes support (temporary)
    this.setupLegacyRoutes();

    console.log('✅ Routes configured');
  }

  /**
   * Setup legacy routes for backward compatibility
   * Note: Legacy routes have been migrated to Clean Architecture
   */
  setupLegacyRoutes() {
    console.log('✅ Legacy routes migration completed - using Clean Architecture routes');
  }

  /**
   * Setup error handling
   */
  setupErrorHandling() {
    // 404 handler
    this.app.use(ErrorMiddleware.notFound());

    // Global error handler
    this.app.use(ErrorMiddleware.handle());

    console.log('✅ Error handling configured');
  }

  /**
   * Get Express app instance
   */
  getApp() {
    return this.app;
  }

  /**
   * Get DI Container
   */
  getContainer() {
    return this.container;
  }

  /**
   * Start the server
   */
  async start(port = process.env.PORT || 5000) {
    try {
      // Connect to database
      await this.connectDatabase();

      // Start server
      const server = this.app.listen(port, () => {
        console.log('🚀 Server started successfully!');
        console.log(`📡 Server running on port ${port}`);
        console.log(`🌍 Environment: ${process.env.NODE_ENV || 'development'}`);
        console.log(`🔗 API Base URL: http://localhost:${port}/api`);
        console.log(`📚 API Documentation: http://localhost:${port}/docs`);
        console.log(`❤️  Health Check: http://localhost:${port}/health`);
      });

      // Graceful shutdown
      this.setupGracefulShutdown(server);

      return server;
    } catch (error) {
      console.error('❌ Failed to start server:', error);
      process.exit(1);
    }
  }

  /**
   * Connect to database
   */
  async connectDatabase() {
    try {
      const mongoose = require('mongoose');

      const mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/robinhoot';

      await mongoose.connect(mongoUri, {
        useNewUrlParser: true,
        useUnifiedTopology: true,
      });

      console.log('✅ Database connected successfully');
    } catch (error) {
      console.error('❌ Database connection failed:', error);
      throw error;
    }
  }

  /**
   * Setup graceful shutdown
   */
  setupGracefulShutdown(server) {
    const gracefulShutdown = async (signal) => {
      console.log(`\n🛑 Received ${signal}. Starting graceful shutdown...`);

      // Close server
      server.close(async () => {
        console.log('📡 HTTP server closed');

        try {
          // Dispose DI container
          await this.container.dispose();
          console.log('📦 DI Container disposed');

          // Close database connection
          const mongoose = require('mongoose');
          await mongoose.connection.close();
          console.log('🗄️  Database connection closed');

          console.log('✅ Graceful shutdown completed');
          process.exit(0);
        } catch (error) {
          console.error('❌ Error during shutdown:', error);
          process.exit(1);
        }
      });

      // Force close after 10 seconds
      setTimeout(() => {
        console.error('⚠️  Forced shutdown after timeout');
        process.exit(1);
      }, 10000);
    };

    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));
  }
}

module.exports = Application;
