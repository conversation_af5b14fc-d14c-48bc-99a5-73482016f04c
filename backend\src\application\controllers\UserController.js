/**
 * User Controller
 * Application layer - HTTP request handling for user operations
 */

const CreateUserDto = require('../dto/user/CreateUserDto');
const LoginDto = require('../dto/user/LoginDto');
const UpdateUserDto = require('../dto/user/UpdateUserDto');
const { HttpStatus } = require('../../shared/enums');
const { PAGINATION } = require('../../shared/constants');

class UserController {
  constructor(userService) {
    this.userService = userService;
  }

  /**
   * Kullanıcı kaydı
   */
  async register(req, res, next) {
    try {
      const createUserDto = new CreateUserDto(req.body);
      const user = await this.userService.createUser(createUserDto.toEntity());

      res.status(HttpStatus.CREATED).json({
        success: true,
        message: 'User created successfully',
        data: { user }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * <PERSON><PERSON><PERSON><PERSON><PERSON> girişi
   */
  async login(req, res, next) {
    try {
      console.log('🔥 UserController.login called!', { email: req.body.email, hasPassword: !!req.body.password });

      const loginDto = new LoginDto(req.body);
      const result = await this.userService.authenticateUser(
        loginDto.emailOrUsername,
        loginDto.password
      );

      console.log('🔥 UserController login result:', result);

      // Set HTTP-only cookie for token
      res.cookie('token', result.token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 24 * 60 * 60 * 1000 // 24 hours
      });

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'Login successful',
        data: {
          user: result.user,
          token: result.token
        }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Kullanıcı çıkışı
   */
  async logout(req, res, next) {
    try {
      res.clearCookie('token');

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'Logout successful'
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Mevcut kullanıcı profilini getir
   */
  async getProfile(req, res, next) {
    try {
      const userId = req.user.id;
      const user = await this.userService.getUserById(userId);

      res.status(HttpStatus.OK).json({
        success: true,
        data: { user }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Kullanıcı profilini güncelle
   */
  async updateProfile(req, res, next) {
    try {
      const userId = req.user.id;
      const updateUserDto = new UpdateUserDto(req.body);

      const user = await this.userService.updateUserProfile(
        userId,
        updateUserDto.toUpdateData(),
        req.user.id
      );

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'Profile updated successfully',
        data: { user }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Şifre değiştir
   */
  async changePassword(req, res, next) {
    try {
      const userId = req.user.id;
      const { currentPassword, newPassword } = req.body;

      if (!currentPassword || !newPassword) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          message: 'Current password and new password are required'
        });
      }

      const result = await this.userService.changePassword(
        userId,
        currentPassword,
        newPassword,
        req.user.id
      );

      res.status(HttpStatus.OK).json({
        success: true,
        message: result.message
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * ID ile kullanıcı getir
   */
  async getUserById(req, res, next) {
    try {
      const { id } = req.params;
      const user = await this.userService.getUserById(id);

      res.status(HttpStatus.OK).json({
        success: true,
        data: { user }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Kullanıcıları listele (Admin)
   */
  async getUsers(req, res, next) {
    try {
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        role,
        status,
        approvalStatus,
        search
      } = req.query;

      const options = {
        page: parseInt(page),
        limit: Math.min(parseInt(limit), PAGINATION.MAX_LIMIT),
        filters: {}
      };

      if (role) options.filters.role = role;
      if (status) options.filters.status = status;
      if (approvalStatus) options.filters.approvalStatus = approvalStatus;

      let result;
      if (search) {
        result = await this.userService.searchUsers(search, options, req.user.id);
      } else {
        result = await this.userService.getUsers(options, req.user.id);
      }

      res.status(HttpStatus.OK).json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Kullanıcı onayla (Admin)
   */
  async approveUser(req, res, next) {
    try {
      const { id } = req.params;
      const { reason } = req.body;

      const user = await this.userService.approveUser(id, req.user.id, reason);

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'User approved successfully',
        data: { user }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Kullanıcı reddet (Admin)
   */
  async rejectUser(req, res, next) {
    try {
      const { id } = req.params;
      const { reason } = req.body;

      const user = await this.userService.rejectUser(id, req.user.id, reason);

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'User rejected successfully',
        data: { user }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Kullanıcı izinlerini güncelle (Admin)
   */
  async updateUserPermissions(req, res, next) {
    try {
      const { id } = req.params;
      const { canBid, canParticipateInLottery } = req.body;

      const permissions = {};
      if (canBid !== undefined) permissions.canBid = canBid;
      if (canParticipateInLottery !== undefined) permissions.canParticipateInLottery = canParticipateInLottery;

      const user = await this.userService.updateUserPermissions(id, permissions, req.user.id);

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'User permissions updated successfully',
        data: { user }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Email doğrulama
   */
  async verifyEmail(req, res, next) {
    try {
      const { token } = req.params;
      const userId = req.user.id;

      const user = await this.userService.verifyEmail(userId, token);

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'Email verified successfully',
        data: { user }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Şifre sıfırlama isteği
   */
  async requestPasswordReset(req, res, next) {
    try {
      const { email } = req.body;

      if (!email) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          message: 'Email is required'
        });
      }

      const result = await this.userService.requestPasswordReset(email);

      res.status(HttpStatus.OK).json({
        success: true,
        message: result.message
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Şifre sıfırlama
   */
  async resetPassword(req, res, next) {
    try {
      const { token, newPassword } = req.body;

      if (!token || !newPassword) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          message: 'Token and new password are required'
        });
      }

      const result = await this.userService.resetPassword(token, newPassword);

      res.status(HttpStatus.OK).json({
        success: true,
        message: result.message
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Kullanıcı oluştur (Admin)
   */
  async createUser(req, res, next) {
    try {
      console.log('🔍 UserController.createUser - req.body:', JSON.stringify(req.body, null, 2));

      const createUserDto = new CreateUserDto(req.body);
      const user = await this.userService.createUser(createUserDto.toEntity(), req.user.id);

      console.log('🔍 UserController.createUser - Created user:', JSON.stringify(user, null, 2));

      res.status(HttpStatus.CREATED).json({
        success: true,
        message: 'User created successfully',
        data: { user }
      });
    } catch (error) {
      console.log('❌ UserController.createUser - Error:', error.message);
      next(error);
    }
  }

  /**
   * Kullanıcı güncelle (Admin)
   */
  async updateUser(req, res, next) {
    try {
      const { id } = req.params;

      console.log('🔥 UserController.updateUser called:', {
        id,
        idType: typeof id,
        isValidObjectId: /^[0-9a-fA-F]{24}$/.test(id)
      });

      if (!id || id === 'undefined' || !id.match(/^[0-9a-fA-F]{24}$/)) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          message: 'Valid user ID is required'
        });
      }

      const updateUserDto = new UpdateUserDto(req.body);

      const user = await this.userService.updateUserProfile(
        id,
        updateUserDto.toUpdateData(),
        req.user.id
      );

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'User updated successfully',
        data: { user }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Kullanıcı sil (Admin)
   */
  async deleteUser(req, res, next) {
    try {
      const { id } = req.params;

      console.log('🔥 UserController.deleteUser called:', {
        id,
        idType: typeof id,
        isValidObjectId: /^[0-9a-fA-F]{24}$/.test(id)
      });

      if (!id || id === 'undefined' || !id.match(/^[0-9a-fA-F]{24}$/)) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          message: 'Valid user ID is required'
        });
      }

      const result = await this.userService.deleteUser(id, req.user.id);

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'User deleted successfully',
        data: result
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = UserController;
