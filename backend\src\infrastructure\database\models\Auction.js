/**
 * Auction Database Model
 * Mongoose schema for auction data persistence
 */

const mongoose = require('mongoose');
const { AuctionStatus } = require('../../../shared/enums');
const { AUCTION } = require('../../../shared/constants');

const bidSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  amount: {
    type: Number,
    required: true,
    min: 0
  },
  name: {
    type: String,
    required: true
  },
  timestamp: {
    type: Date,
    default: Date.now
  },
  isBuyNow: {
    type: Boolean,
    default: false
  }
}, { _id: true });

const auctionSchema = new mongoose.Schema({
  productId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: true
  },
  sellerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  description: {
    type: String,
    required: true,
    maxlength: 2000
  },
  startingPrice: {
    type: Number,
    required: true,
    min: 0
  },
  currentPrice: {
    type: Number,
    required: true,
    min: 0
  },
  buyNowPrice: {
    type: Number,
    min: 0
  },
  minBidIncrement: {
    type: Number,
    required: true,
    min: 1,
    default: AUCTION.MIN_BID_INCREMENT
  },
  startTime: {
    type: Date,
    required: true
  },
  endTime: {
    type: Date,
    required: true
  },
  status: {
    type: String,
    enum: Object.values(AuctionStatus),
    default: AuctionStatus.PENDING
  },
  bids: [bidSchema],
  winnerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  winningBid: {
    type: bidSchema
  },
  autoExtend: {
    type: Boolean,
    default: true
  },
  extendMinutes: {
    type: Number,
    default: AUCTION.AUTO_EXTEND_MINUTES,
    min: 1,
    max: 60
  },
  viewCount: {
    type: Number,
    default: 0,
    min: 0
  },
  watcherCount: {
    type: Number,
    default: 0,
    min: 0
  },
  featured: {
    type: Boolean,
    default: false
  },
  tags: [{
    type: String,
    trim: true
  }],
  metadata: {
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    approvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    approvedAt: Date,
    rejectedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    rejectedAt: Date,
    rejectionReason: String
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
auctionSchema.index({ sellerId: 1, status: 1 });
auctionSchema.index({ status: 1, endTime: 1 });
auctionSchema.index({ startTime: 1, endTime: 1 });
auctionSchema.index({ currentPrice: 1 });
auctionSchema.index({ featured: 1, status: 1 });
auctionSchema.index({ 'bids.userId': 1 });
auctionSchema.index({ title: 'text', description: 'text' });

// Virtual for auction duration
auctionSchema.virtual('duration').get(function() {
  return this.endTime - this.startTime;
});

// Virtual for time remaining
auctionSchema.virtual('timeRemaining').get(function() {
  const now = new Date();
  return Math.max(0, this.endTime - now);
});

// Virtual for is active
auctionSchema.virtual('isActive').get(function() {
  const now = new Date();
  return this.status === AuctionStatus.ACTIVE && 
         now >= this.startTime && 
         now <= this.endTime;
});

// Virtual for has started
auctionSchema.virtual('hasStarted').get(function() {
  return new Date() >= this.startTime;
});

// Virtual for has ended
auctionSchema.virtual('hasEnded').get(function() {
  return new Date() > this.endTime;
});

// Virtual for bid count
auctionSchema.virtual('bidCount').get(function() {
  return this.bids.length;
});

// Virtual for highest bid
auctionSchema.virtual('highestBid').get(function() {
  if (this.bids.length === 0) return null;
  return this.bids.reduce((highest, current) => 
    current.amount > highest.amount ? current : highest
  );
});

// Virtual for minimum next bid
auctionSchema.virtual('minimumNextBid').get(function() {
  return this.currentPrice + this.minBidIncrement;
});

// Pre-save middleware
auctionSchema.pre('save', function(next) {
  // Set current price to starting price if not set
  if (!this.currentPrice) {
    this.currentPrice = this.startingPrice;
  }

  // Validate end time is after start time
  if (this.endTime <= this.startTime) {
    return next(new Error('End time must be after start time'));
  }

  // Validate buy now price
  if (this.buyNowPrice && this.buyNowPrice <= this.startingPrice) {
    return next(new Error('Buy now price must be greater than starting price'));
  }

  next();
});

// Instance methods
auctionSchema.methods.canPlaceBid = function() {
  const now = new Date();
  return this.status === AuctionStatus.ACTIVE && 
         now >= this.startTime && 
         now <= this.endTime;
};

auctionSchema.methods.getMinimumBidAmount = function() {
  return this.currentPrice + this.minBidIncrement;
};

auctionSchema.methods.placeBid = function(userId, amount, userName) {
  if (!this.canPlaceBid()) {
    throw new Error('Cannot place bid on inactive auction');
  }

  const minimumBid = this.getMinimumBidAmount();
  if (amount < minimumBid) {
    throw new Error(`Bid amount must be at least ${minimumBid}`);
  }

  // Check for buy now
  if (this.buyNowPrice && amount >= this.buyNowPrice) {
    this.endAuctionWithBuyNow(userId, amount, userName);
    return;
  }

  const bid = {
    userId,
    amount,
    name: userName,
    timestamp: new Date()
  };

  this.bids.push(bid);
  this.currentPrice = amount;

  // Auto extend if needed
  if (this.autoExtend) {
    this.checkAutoExtend();
  }
};

auctionSchema.methods.endAuctionWithBuyNow = function(userId, amount, userName) {
  const bid = {
    userId,
    amount,
    name: userName,
    timestamp: new Date(),
    isBuyNow: true
  };

  this.bids.push(bid);
  this.currentPrice = amount;
  this.winnerId = userId;
  this.winningBid = bid;
  this.status = AuctionStatus.COMPLETED;
  this.endTime = new Date();
};

auctionSchema.methods.checkAutoExtend = function() {
  const now = new Date();
  const timeLeft = this.endTime - now;
  const extendThreshold = this.extendMinutes * 60 * 1000;

  if (timeLeft <= extendThreshold) {
    this.endTime = new Date(now.getTime() + extendThreshold);
  }
};

auctionSchema.methods.incrementViewCount = function() {
  this.viewCount += 1;
};

auctionSchema.methods.getUserBids = function(userId) {
  return this.bids.filter(bid => bid.userId.toString() === userId.toString());
};

// Static methods
auctionSchema.statics.findActive = function(options = {}) {
  const now = new Date();
  return this.find({
    status: AuctionStatus.ACTIVE,
    startTime: { $lte: now },
    endTime: { $gt: now }
  }, null, options);
};

auctionSchema.statics.findEndingSoon = function(hours = 24) {
  const now = new Date();
  const threshold = new Date(now.getTime() + (hours * 60 * 60 * 1000));
  
  return this.find({
    status: AuctionStatus.ACTIVE,
    endTime: { $lte: threshold, $gt: now }
  });
};

auctionSchema.statics.findExpired = function() {
  const now = new Date();
  return this.find({
    status: AuctionStatus.ACTIVE,
    endTime: { $lte: now }
  });
};

module.exports = mongoose.model('Auction', auctionSchema);
