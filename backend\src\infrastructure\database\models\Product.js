/**
 * Product Database Model
 * Mongoose schema for product data persistence
 */

const mongoose = require('mongoose');
const { ProductStatus, ProductType } = require('../../../shared/enums');
const { PRODUCT, VALIDATION } = require('../../../shared/constants');

const dynamicPricingSchema = new mongoose.Schema({
  enabled: {
    type: Boolean,
    default: false
  },
  basePrice: {
    type: Number,
    required: true,
    min: 0
  },
  discountRules: [{
    quantity: {
      type: Number,
      required: true,
      min: 1
    },
    discountPercentage: {
      type: Number,
      required: true,
      min: 0,
      max: 100
    }
  }],
  monthlyPromotion: {
    enabled: {
      type: Boolean,
      default: false
    },
    startDate: Date,
    endDate: Date,
    discountPercentage: {
      type: Number,
      min: 0,
      max: 100
    }
  }
}, { _id: false });

const reviewSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  rating: {
    type: Number,
    required: true,
    min: 1,
    max: 5
  },
  comment: {
    type: String,
    maxlength: 1000
  },
  verified: {
    type: Boolean,
    default: false
  },
  helpful: {
    type: Number,
    default: 0
  }
}, { timestamps: true });

const productSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: VALIDATION.NAME_MAX_LENGTH
  },
  description: {
    type: String,
    required: true,
    maxlength: VALIDATION.DESCRIPTION_MAX_LENGTH
  },
  shortDescription: {
    type: String,
    maxlength: 500
  },
  price: {
    type: Number,
    required: true,
    min: 0
  },
  originalPrice: {
    type: Number,
    min: 0
  },
  discountPercentage: {
    type: Number,
    min: 0,
    max: 100,
    default: 0
  },
  categoryId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category',
    required: false,
    default: null,
    index: true // Performance için index ekle
  },
  sellerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  images: [{
    url: {
      type: String,
      required: true
    },
    alt: String,
    isPrimary: {
      type: Boolean,
      default: false
    }
  }],
  specifications: {
    type: Map,
    of: String
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  sku: {
    type: String,
    unique: true,
    sparse: true
  },
  barcode: {
    type: String,
    unique: true,
    sparse: true
  },
  stock: {
    type: Number,
    required: true,
    min: 0,
    default: 0
  },
  lowStockThreshold: {
    type: Number,
    default: 10,
    min: 0
  },
  weight: {
    type: Number,
    min: 0
  },
  dimensions: {
    length: Number,
    width: Number,
    height: Number,
    unit: {
      type: String,
      enum: ['cm', 'inch'],
      default: 'cm'
    }
  },
  status: {
    type: String,
    enum: Object.values(ProductStatus),
    default: ProductStatus.PENDING
  },
  type: {
    type: String,
    enum: Object.values(ProductType),
    default: ProductType.PHYSICAL
  },
  featured: {
    type: Boolean,
    default: false
  },
  isDigital: {
    type: Boolean,
    default: false
  },
  downloadUrl: {
    type: String
  },
  dynamicPricing: dynamicPricingSchema,
  reviews: [reviewSchema],
  averageRating: {
    type: Number,
    min: 0,
    max: 5,
    default: 0
  },
  reviewCount: {
    type: Number,
    default: 0,
    min: 0
  },
  viewCount: {
    type: Number,
    default: 0,
    min: 0
  },
  likeCount: {
    type: Number,
    default: 0,
    min: 0
  },
  shareCount: {
    type: Number,
    default: 0,
    min: 0
  },
  salesCount: {
    type: Number,
    default: 0,
    min: 0
  },
  seoTitle: {
    type: String,
    maxlength: 60
  },
  seoDescription: {
    type: String,
    maxlength: 160
  },
  seoKeywords: [{
    type: String,
    trim: true
  }],
  metadata: {
    approvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    approvedAt: Date,
    rejectedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    rejectedAt: Date,
    rejectionReason: String,
    lastModifiedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
productSchema.index({ name: 'text', description: 'text', tags: 'text' });
productSchema.index({ categoryId: 1, status: 1 });
productSchema.index({ sellerId: 1, status: 1 });
productSchema.index({ status: 1, featured: 1 });
productSchema.index({ price: 1 });
productSchema.index({ averageRating: -1 });
productSchema.index({ salesCount: -1 });
productSchema.index({ createdAt: -1 });
productSchema.index({ stock: 1 });
productSchema.index({ sku: 1 });
productSchema.index({ barcode: 1 });

// Virtual for discount amount
productSchema.virtual('discountAmount').get(function() {
  if (this.originalPrice && this.discountPercentage > 0) {
    return this.originalPrice * (this.discountPercentage / 100);
  }
  return 0;
});

// Virtual for final price
productSchema.virtual('finalPrice').get(function() {
  return this.price - this.discountAmount;
});

// Virtual for is on sale
productSchema.virtual('isOnSale').get(function() {
  return this.discountPercentage > 0;
});

// Virtual for is in stock
productSchema.virtual('isInStock').get(function() {
  return this.stock > 0;
});

// Virtual for is low stock
productSchema.virtual('isLowStock').get(function() {
  return this.stock <= this.lowStockThreshold && this.stock > 0;
});

// Virtual for is out of stock
productSchema.virtual('isOutOfStock').get(function() {
  return this.stock === 0;
});

// Virtual for primary image
productSchema.virtual('primaryImage').get(function() {
  const primary = this.images.find(img => img.isPrimary);
  return primary || this.images[0] || null;
});

// Virtual for rating percentage
productSchema.virtual('ratingPercentage').get(function() {
  return this.averageRating ? (this.averageRating / 5) * 100 : 0;
});

// Pre-save middleware
productSchema.pre('save', function(next) {
  // Generate SKU if not provided
  if (!this.sku) {
    this.sku = this.generateSKU();
  }

  // Set original price if not set
  if (!this.originalPrice) {
    this.originalPrice = this.price;
  }

  // Calculate discount percentage if original price is different
  if (this.originalPrice && this.originalPrice !== this.price) {
    this.discountPercentage = Math.round(((this.originalPrice - this.price) / this.originalPrice) * 100);
  }

  // Ensure only one primary image
  const primaryImages = this.images.filter(img => img.isPrimary);
  if (primaryImages.length > 1) {
    this.images.forEach((img, index) => {
      img.isPrimary = index === 0;
    });
  } else if (primaryImages.length === 0 && this.images.length > 0) {
    this.images[0].isPrimary = true;
  }

  next();
});

// Instance methods
productSchema.methods.generateSKU = function() {
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.random().toString(36).substr(2, 4).toUpperCase();
  return `PRD-${timestamp}-${random}`;
};

productSchema.methods.updateStock = function(quantity) {
  this.stock = Math.max(0, quantity);
  return this.save();
};

productSchema.methods.decrementStock = function(quantity = 1) {
  this.stock = Math.max(0, this.stock - quantity);
  this.salesCount += quantity;
  return this.save();
};

productSchema.methods.incrementStock = function(quantity = 1) {
  this.stock += quantity;
  return this.save();
};

productSchema.methods.addReview = function(userId, rating, comment) {
  this.reviews.push({
    userId,
    rating,
    comment,
    verified: false
  });

  this.calculateAverageRating();
  return this.save();
};

productSchema.methods.calculateAverageRating = function() {
  if (this.reviews.length === 0) {
    this.averageRating = 0;
    this.reviewCount = 0;
    return;
  }

  const totalRating = this.reviews.reduce((sum, review) => sum + review.rating, 0);
  this.averageRating = Math.round((totalRating / this.reviews.length) * 10) / 10;
  this.reviewCount = this.reviews.length;
};

productSchema.methods.incrementViewCount = function() {
  this.viewCount += 1;
  return this.save();
};

productSchema.methods.incrementLikeCount = function() {
  this.likeCount += 1;
  return this.save();
};

productSchema.methods.incrementShareCount = function() {
  this.shareCount += 1;
  return this.save();
};

productSchema.methods.isAvailable = function(quantity = 1) {
  return this.status === ProductStatus.ACTIVE && this.stock >= quantity;
};

productSchema.methods.canBePurchased = function(quantity = 1) {
  return this.isAvailable(quantity) && !this.isDigital;
};

productSchema.methods.approve = function(approvedBy) {
  this.status = ProductStatus.ACTIVE;
  this.metadata.approvedBy = approvedBy;
  this.metadata.approvedAt = new Date();
  return this.save();
};

productSchema.methods.reject = function(rejectedBy, reason) {
  this.status = ProductStatus.REJECTED;
  this.metadata.rejectedBy = rejectedBy;
  this.metadata.rejectedAt = new Date();
  this.metadata.rejectionReason = reason;
  return this.save();
};

// Static methods
productSchema.statics.findByCategory = function(categoryId, options = {}) {
  return this.find({ categoryId, status: ProductStatus.ACTIVE }, null, options);
};

productSchema.statics.findBySeller = function(sellerId, options = {}) {
  return this.find({ sellerId }, null, options);
};

productSchema.statics.findFeatured = function(limit = 10) {
  return this.find({
    featured: true,
    status: ProductStatus.ACTIVE
  }).limit(limit);
};

productSchema.statics.findInStock = function(options = {}) {
  return this.find({
    stock: { $gt: 0 },
    status: ProductStatus.ACTIVE
  }, null, options);
};

productSchema.statics.findLowStock = function(threshold = 10) {
  return this.find({
    stock: { $lte: threshold, $gt: 0 },
    status: ProductStatus.ACTIVE
  });
};

productSchema.statics.findBestSellers = function(limit = 10) {
  return this.find({
    status: ProductStatus.ACTIVE
  }).sort({ salesCount: -1 }).limit(limit);
};

productSchema.statics.findNew = function(days = 7, limit = 10) {
  const date = new Date();
  date.setDate(date.getDate() - days);

  return this.find({
    createdAt: { $gte: date },
    status: ProductStatus.ACTIVE
  }).sort({ createdAt: -1 }).limit(limit);
};

productSchema.statics.findDiscounted = function(options = {}) {
  return this.find({
    discountPercentage: { $gt: 0 },
    status: ProductStatus.ACTIVE
  }, null, options);
};

module.exports = mongoose.model('Product', productSchema);
