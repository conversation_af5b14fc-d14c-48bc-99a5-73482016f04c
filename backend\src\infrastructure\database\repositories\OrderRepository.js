/**
 * Order Repository Implementation
 * Implements IOrderRepository interface
 */

const IOrderRepository = require('../../../core/interfaces/IOrderRepository');
const OrderModel = require('../models/Order');
const Order = require('../../../core/entities/Order');
const { PAGINATION } = require('../../../shared/constants');
const { DatabaseException } = require('../../../core/exceptions');

class OrderRepository extends IOrderRepository {
  constructor() {
    super();
    this.model = OrderModel;
  }

  /**
   * Create a new order
   */
  async create(persistenceData) {
    try {
      console.log('🔍 OrderRepository.create - Persistence data:', JSON.stringify(persistenceData, null, 2));

      // Create and save model directly with persistence data
      const order = new this.model(persistenceData);
      const savedOrder = await order.save();

      return Order.fromPersistence(savedOrder.toObject());
    } catch (error) {
      console.log('❌ OrderRepository.create - Error:', error.message);
      throw new DatabaseException(`Failed to create order: ${error.message}`);
    }
  }

  /**
   * Find order by ID
   */
  async findById(id) {
    try {
      const order = await this.model.findById(id)
        .populate('userId', 'name email')
        .populate('items.productId', 'name images')
        .populate('items.sellerId', 'name email')
        .lean();

      return order ? Order.fromPersistence(order) : null;
    } catch (error) {
      throw new DatabaseException(`Failed to find order: ${error.message}`);
    }
  }

  /**
   * Find order by order number
   */
  async findByOrderNumber(orderNumber) {
    try {
      const order = await this.model.findOne({ orderNumber })
        .populate('userId', 'name email')
        .populate('items.productId', 'name images')
        .populate('items.sellerId', 'name email')
        .lean();

      return order ? Order.fromPersistence(order) : null;
    } catch (error) {
      throw new DatabaseException(`Failed to find order by order number: ${error.message}`);
    }
  }

  /**
   * Find all orders
   */
  async findAll(options = {}) {
    try {
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        status,
        paymentStatus,
        shippingStatus,
        startDate,
        endDate,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = options;

      const query = {};
      if (status) query.status = status;
      if (paymentStatus) query.paymentStatus = paymentStatus;
      if (shippingStatus) query.shippingStatus = shippingStatus;

      if (startDate || endDate) {
        query.createdAt = {};
        if (startDate) query.createdAt.$gte = new Date(startDate);
        if (endDate) query.createdAt.$lte = new Date(endDate);
      }

      const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };
      const skip = (page - 1) * limit;

      const [orders, total] = await Promise.all([
        this.model.find(query)
          .populate('userId', 'name email')
          .populate('items.sellerId', 'name email')
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .lean(),
        this.model.countDocuments(query)
      ]);

      return {
        orders: orders.map(order => Order.fromPersistence(order)),
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw new DatabaseException(`Failed to find orders: ${error.message}`);
    }
  }

  /**
   * Find orders by user
   */
  async findByUser(userId, options = {}) {
    try {
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        status,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = options;

      const query = { userId };
      if (status) query.status = status;

      const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };
      const skip = (page - 1) * limit;

      const [orders, total] = await Promise.all([
        this.model.find(query)
          .populate('items.productId', 'name images')
          .populate('items.sellerId', 'name')
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .lean(),
        this.model.countDocuments(query)
      ]);

      return {
        orders: orders.map(order => Order.fromPersistence(order)),
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw new DatabaseException(`Failed to find orders by user: ${error.message}`);
    }
  }

  /**
   * Find orders by seller
   */
  async findBySeller(sellerId, options = {}) {
    try {
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        status,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = options;

      const query = { 'items.sellerId': sellerId };
      if (status) query.status = status;

      const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };
      const skip = (page - 1) * limit;

      const [orders, total] = await Promise.all([
        this.model.find(query)
          .populate('userId', 'name email')
          .populate('items.productId', 'name images')
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .lean(),
        this.model.countDocuments(query)
      ]);

      return {
        orders: orders.map(order => Order.fromPersistence(order)),
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw new DatabaseException(`Failed to find orders by seller: ${error.message}`);
    }
  }

  /**
   * Find orders by status
   */
  async findByStatus(status, options = {}) {
    try {
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = options;

      const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };
      const skip = (page - 1) * limit;

      const [orders, total] = await Promise.all([
        this.model.find({ status })
          .populate('userId', 'name email')
          .populate('items.sellerId', 'name email')
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .lean(),
        this.model.countDocuments({ status })
      ]);

      return {
        orders: orders.map(order => Order.fromPersistence(order)),
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw new DatabaseException(`Failed to find orders by status: ${error.message}`);
    }
  }

  /**
   * Find orders by date range
   */
  async findByDateRange(startDate, endDate, options = {}) {
    try {
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = options;

      const query = {
        createdAt: {
          $gte: new Date(startDate),
          $lte: new Date(endDate)
        }
      };

      const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };
      const skip = (page - 1) * limit;

      const [orders, total] = await Promise.all([
        this.model.find(query)
          .populate('userId', 'name email')
          .populate('items.sellerId', 'name email')
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .lean(),
        this.model.countDocuments(query)
      ]);

      return {
        orders: orders.map(order => Order.fromPersistence(order)),
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw new DatabaseException(`Failed to find orders by date range: ${error.message}`);
    }
  }

  /**
   * Search orders
   */
  async search(query, options = {}) {
    try {
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = options;

      const searchQuery = {
        $or: [
          { orderNumber: { $regex: query, $options: 'i' } },
          { 'items.name': { $regex: query, $options: 'i' } },
          { 'shippingAddress.firstName': { $regex: query, $options: 'i' } },
          { 'shippingAddress.lastName': { $regex: query, $options: 'i' } },
          { trackingNumber: { $regex: query, $options: 'i' } }
        ]
      };

      const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };
      const skip = (page - 1) * limit;

      const [orders, total] = await Promise.all([
        this.model.find(searchQuery)
          .populate('userId', 'name email')
          .populate('items.sellerId', 'name email')
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .lean(),
        this.model.countDocuments(searchQuery)
      ]);

      return {
        orders: orders.map(order => Order.fromPersistence(order)),
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw new DatabaseException(`Failed to search orders: ${error.message}`);
    }
  }

  /**
   * Update order
   */
  async update(id, updateData) {
    try {
      const order = await this.model.findByIdAndUpdate(
        id,
        { ...updateData, updatedAt: new Date() },
        { new: true, runValidators: true }
      ).lean();

      return order ? Order.fromPersistence(order) : null;
    } catch (error) {
      throw new DatabaseException(`Failed to update order: ${error.message}`);
    }
  }

  /**
   * Delete order
   */
  async delete(id) {
    try {
      const result = await this.model.findByIdAndDelete(id);
      return !!result;
    } catch (error) {
      throw new DatabaseException(`Failed to delete order: ${error.message}`);
    }
  }

  /**
   * Update order status
   */
  async updateStatus(id, status) {
    try {
      const order = await this.model.findByIdAndUpdate(
        id,
        { status, updatedAt: new Date() },
        { new: true, runValidators: true }
      ).lean();

      return order ? Order.fromPersistence(order) : null;
    } catch (error) {
      throw new DatabaseException(`Failed to update order status: ${error.message}`);
    }
  }

  /**
   * Add order item
   */
  async addItem(orderId, itemData) {
    try {
      const order = await this.model.findById(orderId);
      if (!order) {
        return null;
      }

      order.addItem(itemData);
      await order.save();

      return Order.fromPersistence(order.toObject());
    } catch (error) {
      throw new DatabaseException(`Failed to add order item: ${error.message}`);
    }
  }

  /**
   * Remove order item
   */
  async removeItem(orderId, itemId) {
    try {
      const order = await this.model.findById(orderId);
      if (!order) {
        return null;
      }

      order.removeItem(itemId);
      await order.save();

      return Order.fromPersistence(order.toObject());
    } catch (error) {
      throw new DatabaseException(`Failed to remove order item: ${error.message}`);
    }
  }

  /**
   * Update order item
   */
  async updateItem(orderId, itemId, updateData) {
    try {
      const order = await this.model.findById(orderId);
      if (!order) {
        return null;
      }

      const item = order.items.id(itemId);
      if (!item) {
        throw new DatabaseException('Order item not found');
      }

      Object.assign(item, updateData);
      order.calculateTotals();
      await order.save();

      return Order.fromPersistence(order.toObject());
    } catch (error) {
      throw new DatabaseException(`Failed to update order item: ${error.message}`);
    }
  }

  /**
   * Get order statistics
   */
  async getStatistics(id) {
    try {
      const order = await this.model.findById(id).lean();
      if (!order) {
        return null;
      }

      return {
        itemsCount: order.items.length,
        totalQuantity: order.items.reduce((sum, item) => sum + item.quantity, 0),
        subtotal: order.subtotal,
        total: order.total,
        totalRefunded: order.refunds.reduce((sum, refund) => sum + refund.amount, 0),
        status: order.status,
        paymentStatus: order.paymentStatus,
        shippingStatus: order.shippingStatus,
        createdAt: order.createdAt,
        updatedAt: order.updatedAt
      };
    } catch (error) {
      throw new DatabaseException(`Failed to get order statistics: ${error.message}`);
    }
  }

  /**
   * Get orders count by status
   */
  async getCountByStatus(userId = null) {
    try {
      const matchStage = userId ? { userId } : {};

      const result = await this.model.aggregate([
        { $match: matchStage },
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 }
          }
        }
      ]);

      const counts = {};
      result.forEach(item => {
        counts[item._id] = item.count;
      });

      return counts;
    } catch (error) {
      throw new DatabaseException(`Failed to get order counts: ${error.message}`);
    }
  }

  /**
   * Get revenue statistics
   */
  async getRevenueStatistics(options = {}) {
    try {
      const { startDate, endDate, sellerId } = options;

      const matchStage = {
        status: { $ne: 'cancelled' }
      };

      if (startDate || endDate) {
        matchStage.createdAt = {};
        if (startDate) matchStage.createdAt.$gte = new Date(startDate);
        if (endDate) matchStage.createdAt.$lte = new Date(endDate);
      }

      if (sellerId) {
        matchStage['items.sellerId'] = sellerId;
      }

      const result = await this.model.aggregate([
        { $match: matchStage },
        {
          $group: {
            _id: null,
            totalRevenue: { $sum: '$total' },
            totalOrders: { $sum: 1 },
            averageOrderValue: { $avg: '$total' },
            totalItems: { $sum: { $size: '$items' } }
          }
        }
      ]);

      return result[0] || {
        totalRevenue: 0,
        totalOrders: 0,
        averageOrderValue: 0,
        totalItems: 0
      };
    } catch (error) {
      throw new DatabaseException(`Failed to get revenue statistics: ${error.message}`);
    }
  }

  /**
   * Get top selling products
   */
  async getTopSellingProducts(options = {}) {
    try {
      const { limit = 10, startDate, endDate } = options;

      const matchStage = {
        status: { $ne: 'cancelled' }
      };

      if (startDate || endDate) {
        matchStage.createdAt = {};
        if (startDate) matchStage.createdAt.$gte = new Date(startDate);
        if (endDate) matchStage.createdAt.$lte = new Date(endDate);
      }

      const result = await this.model.aggregate([
        { $match: matchStage },
        { $unwind: '$items' },
        {
          $group: {
            _id: '$items.productId',
            totalQuantity: { $sum: '$items.quantity' },
            totalRevenue: { $sum: '$items.total' },
            productName: { $first: '$items.name' },
            averagePrice: { $avg: '$items.price' }
          }
        },
        { $sort: { totalQuantity: -1 } },
        { $limit: limit }
      ]);

      return result;
    } catch (error) {
      throw new DatabaseException(`Failed to get top selling products: ${error.message}`);
    }
  }

  /**
   * Get orders by payment status
   */
  async findByPaymentStatus(paymentStatus, options = {}) {
    try {
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = options;

      const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };
      const skip = (page - 1) * limit;

      const [orders, total] = await Promise.all([
        this.model.find({ paymentStatus })
          .populate('userId', 'name email')
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .lean(),
        this.model.countDocuments({ paymentStatus })
      ]);

      return {
        orders: orders.map(order => Order.fromPersistence(order)),
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw new DatabaseException(`Failed to find orders by payment status: ${error.message}`);
    }
  }

  /**
   * Update payment status
   */
  async updatePaymentStatus(id, paymentStatus) {
    try {
      const order = await this.model.findByIdAndUpdate(
        id,
        { paymentStatus, updatedAt: new Date() },
        { new: true, runValidators: true }
      ).lean();

      return order ? Order.fromPersistence(order) : null;
    } catch (error) {
      throw new DatabaseException(`Failed to update payment status: ${error.message}`);
    }
  }

  /**
   * Update shipping status
   */
  async updateShippingStatus(id, shippingStatus) {
    try {
      const updateData = { shippingStatus, updatedAt: new Date() };

      // Set shipped date if status is shipped
      if (shippingStatus === 'shipped') {
        updateData.shippedAt = new Date();
      }

      // Set delivered date if status is delivered
      if (shippingStatus === 'delivered') {
        updateData.deliveredAt = new Date();
        updateData.status = 'delivered';
      }

      const order = await this.model.findByIdAndUpdate(
        id,
        updateData,
        { new: true, runValidators: true }
      ).lean();

      return order ? Order.fromPersistence(order) : null;
    } catch (error) {
      throw new DatabaseException(`Failed to update shipping status: ${error.message}`);
    }
  }

  /**
   * Add tracking information
   */
  async addTrackingInfo(id, trackingData) {
    try {
      const updateData = {
        trackingNumber: trackingData.trackingNumber,
        carrier: trackingData.carrier,
        estimatedDelivery: trackingData.estimatedDelivery,
        updatedAt: new Date()
      };

      // Auto-update shipping status to shipped if pending
      const order = await this.model.findById(id);
      if (order && order.shippingStatus === 'pending') {
        updateData.shippingStatus = 'shipped';
        updateData.shippedAt = new Date();
      }

      const updatedOrder = await this.model.findByIdAndUpdate(
        id,
        updateData,
        { new: true, runValidators: true }
      ).lean();

      return updatedOrder ? Order.fromPersistence(updatedOrder) : null;
    } catch (error) {
      throw new DatabaseException(`Failed to add tracking info: ${error.message}`);
    }
  }

  /**
   * Get pending orders
   */
  async findPending(options = {}) {
    try {
      const orders = await this.model.findPending(options).lean();
      return orders.map(order => Order.fromPersistence(order));
    } catch (error) {
      throw new DatabaseException(`Failed to find pending orders: ${error.message}`);
    }
  }

  /**
   * Get completed orders
   */
  async findCompleted(options = {}) {
    try {
      const orders = await this.model.findCompleted(options).lean();
      return orders.map(order => Order.fromPersistence(order));
    } catch (error) {
      throw new DatabaseException(`Failed to find completed orders: ${error.message}`);
    }
  }

  /**
   * Get cancelled orders
   */
  async findCancelled(options = {}) {
    try {
      const orders = await this.model.findCancelled(options).lean();
      return orders.map(order => Order.fromPersistence(order));
    } catch (error) {
      throw new DatabaseException(`Failed to find cancelled orders: ${error.message}`);
    }
  }

  /**
   * Cancel order
   */
  async cancel(id, reason) {
    try {
      const order = await this.model.findById(id);
      if (!order) {
        return null;
      }

      order.cancel(reason);
      await order.save();

      return Order.fromPersistence(order.toObject());
    } catch (error) {
      throw new DatabaseException(`Failed to cancel order: ${error.message}`);
    }
  }

  /**
   * Refund order
   */
  async refund(id, amount, reason) {
    try {
      const order = await this.model.findById(id);
      if (!order) {
        return null;
      }

      order.addRefund(amount, reason);
      await order.save();

      return Order.fromPersistence(order.toObject());
    } catch (error) {
      throw new DatabaseException(`Failed to refund order: ${error.message}`);
    }
  }

  /**
   * Get orders requiring action
   */
  async findRequiringAction(options = {}) {
    try {
      const orders = await this.model.findRequiringAction(options).lean();
      return orders.map(order => Order.fromPersistence(order));
    } catch (error) {
      throw new DatabaseException(`Failed to find orders requiring action: ${error.message}`);
    }
  }

  /**
   * Bulk update orders
   */
  async bulkUpdate(updates) {
    try {
      const bulkOps = updates.map(update => ({
        updateOne: {
          filter: { _id: update.id },
          update: { ...update.data, updatedAt: new Date() }
        }
      }));

      const result = await this.model.bulkWrite(bulkOps);
      return {
        modifiedCount: result.modifiedCount,
        matchedCount: result.matchedCount
      };
    } catch (error) {
      throw new DatabaseException(`Failed to bulk update orders: ${error.message}`);
    }
  }

  /**
   * Get order analytics
   */
  async getAnalytics(filters = {}) {
    try {
      const { startDate, endDate, sellerId } = filters;

      const matchStage = {};
      if (startDate || endDate) {
        matchStage.createdAt = {};
        if (startDate) matchStage.createdAt.$gte = new Date(startDate);
        if (endDate) matchStage.createdAt.$lte = new Date(endDate);
      }

      if (sellerId) {
        matchStage['items.sellerId'] = sellerId;
      }

      const [
        statusCounts,
        paymentCounts,
        revenueStats,
        topProducts
      ] = await Promise.all([
        this.getCountByStatus(),
        this.getPaymentStatusCounts(matchStage),
        this.getRevenueStatistics(filters),
        this.getTopSellingProducts({ ...filters, limit: 5 })
      ]);

      return {
        statusCounts,
        paymentCounts,
        revenueStats,
        topProducts
      };
    } catch (error) {
      throw new DatabaseException(`Failed to get order analytics: ${error.message}`);
    }
  }

  /**
   * Helper method to get payment status counts
   */
  async getPaymentStatusCounts(matchStage = {}) {
    try {
      const result = await this.model.aggregate([
        { $match: matchStage },
        {
          $group: {
            _id: '$paymentStatus',
            count: { $sum: 1 }
          }
        }
      ]);

      const counts = {};
      result.forEach(item => {
        counts[item._id] = item.count;
      });

      return counts;
    } catch (error) {
      throw new DatabaseException(`Failed to get payment status counts: ${error.message}`);
    }
  }
}

module.exports = OrderRepository;
