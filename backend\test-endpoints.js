const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:5000/api';

async function testEndpoints() {
  console.log('🚀 Starting endpoint tests...\n');

  try {
    // 1. Test Login
    console.log('1️⃣ Testing Login Endpoint');
    console.log('POST /api/auth/login');
    
    const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'eEf8kbpE9G8XhiL'
      })
    });

    const loginData = await loginResponse.json();
    console.log('Status:', loginResponse.status);
    console.log('Response:', JSON.stringify(loginData, null, 2));
    
    if (!loginResponse.ok) {
      console.log('❌ Login failed, stopping tests');
      return;
    }

    const token = loginData.token || loginData.data?.token;
    console.log('✅ Login successful, token received\n');

    // 2. Test Categories
    console.log('2️⃣ Testing Categories Endpoint');
    console.log('GET /api/categories');
    
    const categoriesResponse = await fetch(`${BASE_URL}/categories`);
    const categoriesData = await categoriesResponse.json();
    console.log('Status:', categoriesResponse.status);
    console.log('Response:', JSON.stringify(categoriesData, null, 2));
    console.log('✅ Categories test completed\n');

    // 3. Test Users (with admin token)
    console.log('3️⃣ Testing Users Endpoint (Admin)');
    console.log('GET /api/users');
    
    const usersResponse = await fetch(`${BASE_URL}/users`, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    const usersData = await usersResponse.json();
    console.log('Status:', usersResponse.status);
    console.log('Response:', JSON.stringify(usersData, null, 2));
    console.log('✅ Users test completed\n');

    // 4. Test Products
    console.log('4️⃣ Testing Products Endpoint');
    console.log('GET /api/products');
    
    const productsResponse = await fetch(`${BASE_URL}/products`);
    const productsData = await productsResponse.json();
    console.log('Status:', productsResponse.status);
    console.log('Response:', JSON.stringify(productsData, null, 2));
    console.log('✅ Products test completed\n');

    // 5. Test Create Category (with admin token)
    console.log('5️⃣ Testing Create Category Endpoint (Admin)');
    console.log('POST /api/categories');
    
    const newCategory = {
      name: 'Test Kategori',
      slug: 'test-kategori',
      description: 'Test için oluşturulan kategori',
      shortDescription: 'Test kategorisi',
      isActive: true,
      isFeatured: false
    };

    const createCategoryResponse = await fetch(`${BASE_URL}/categories`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(newCategory)
    });
    const createCategoryData = await createCategoryResponse.json();
    console.log('Status:', createCategoryResponse.status);
    console.log('Response:', JSON.stringify(createCategoryData, null, 2));
    console.log('✅ Create category test completed\n');

    // 6. Test Health Check
    console.log('6️⃣ Testing Health Check');
    console.log('GET /health');
    
    const healthResponse = await fetch('http://localhost:5000/health');
    const healthData = await healthResponse.json();
    console.log('Status:', healthResponse.status);
    console.log('Response:', JSON.stringify(healthData, null, 2));
    console.log('✅ Health check completed\n');

    console.log('🎉 All endpoint tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Run tests
testEndpoints();
