/**
 * Category Service
 * Business logic for category operations
 */

const { BusinessException, ValidationException, AuthorizationException } = require('../../core/exceptions');
const { UserRole } = require('../../shared/enums');
const Category = require('../../core/entities/Category');

class CategoryService {
  constructor(categoryRepository, userRepository, productRepository) {
    this.categoryRepository = categoryRepository;
    this.userRepository = userRepository;
    this.productRepository = productRepository;
  }

  /**
   * Get all categories
   */
  async getAllCategories(options = {}) {
    try {
      return await this.categoryRepository.findAll(options);
    } catch (error) {
      throw new BusinessException(`Failed to get categories: ${error.message}`);
    }
  }

  /**
   * Get category by ID
   */
  async getCategoryById(id) {
    try {
      const category = await this.categoryRepository.findById(id);
      if (!category) {
        throw new ValidationException('Category not found');
      }
      return category;
    } catch (error) {
      if (error instanceof ValidationException) throw error;
      throw new BusinessException(`Failed to get category: ${error.message}`);
    }
  }

  /**
   * Get category by slug
   */
  async getCategoryBySlug(slug) {
    try {
      const category = await this.categoryRepository.findBySlug(slug);
      if (!category) {
        throw new ValidationException('Category not found');
      }
      return category;
    } catch (error) {
      if (error instanceof ValidationException) throw error;
      throw new BusinessException(`Failed to get category: ${error.message}`);
    }
  }

  /**
   * Create new category
   */
  async createCategory(categoryData, createdBy) {
    try {
      // Validate user permissions
      const user = await this.userRepository.findById(createdBy);
      if (!user) {
        throw new ValidationException('User not found');
      }

      if (user.role !== UserRole.ADMIN) {
        throw new AuthorizationException('Only admins can create categories');
      }

      // Validate parent category if specified
      if (categoryData.parentId) {
        const parentCategory = await this.categoryRepository.findById(categoryData.parentId);
        if (!parentCategory) {
          throw new ValidationException('Parent category not found');
        }

        // Check hierarchy depth
        if (parentCategory.level >= 4) {
          throw new ValidationException('Category hierarchy cannot exceed 5 levels');
        }
      }

      // Check slug uniqueness
      if (categoryData.slug) {
        const isUnique = await this.categoryRepository.isSlugUnique(categoryData.slug);
        if (!isUnique) {
          throw new ValidationException('Category slug already exists');
        }
      }

      // Create category entity
      const category = new Category({
        ...categoryData,
        metadata: {
          createdBy
        }
      });

      // Save to repository
      const savedCategory = await this.categoryRepository.create(category.toPersistence());

      return savedCategory;
    } catch (error) {
      if (error instanceof ValidationException || error instanceof AuthorizationException) {
        throw error;
      }
      throw new BusinessException(`Failed to create category: ${error.message}`);
    }
  }

  /**
   * Update category
   */
  async updateCategory(id, updateData, updatedBy) {
    try {
      const category = await this.categoryRepository.findById(id);
      if (!category) {
        throw new ValidationException('Category not found');
      }

      // Check permissions
      const user = await this.userRepository.findById(updatedBy);
      if (!user || user.role !== UserRole.ADMIN) {
        throw new AuthorizationException('Only admins can update categories');
      }

      // Validate parent category if being updated
      if (updateData.parentId !== undefined) {
        if (updateData.parentId) {
          const parentCategory = await this.categoryRepository.findById(updateData.parentId);
          if (!parentCategory) {
            throw new ValidationException('Parent category not found');
          }

          // Prevent circular reference
          if (updateData.parentId === id) {
            throw new ValidationException('Category cannot be its own parent');
          }

          // Check if new parent is a descendant
          const isDescendant = await this.isDescendantOf(updateData.parentId, id);
          if (isDescendant) {
            throw new ValidationException('Cannot move category to its own descendant');
          }
        }
      }

      // Check slug uniqueness if being updated
      if (updateData.slug && updateData.slug !== category.slug) {
        const isUnique = await this.categoryRepository.isSlugUnique(updateData.slug, id);
        if (!isUnique) {
          throw new ValidationException('Category slug already exists');
        }
      }

      // Update metadata
      updateData.metadata = {
        ...category.metadata,
        updatedBy
      };

      const updatedCategory = await this.categoryRepository.update(id, updateData);
      return updatedCategory;
    } catch (error) {
      if (error instanceof ValidationException || error instanceof AuthorizationException) {
        throw error;
      }
      throw new BusinessException(`Failed to update category: ${error.message}`);
    }
  }

  /**
   * Delete category
   */
  async deleteCategory(id, deletedBy) {
    try {
      const category = await this.categoryRepository.findById(id);
      if (!category) {
        throw new ValidationException('Category not found');
      }

      // Check permissions
      const user = await this.userRepository.findById(deletedBy);
      if (!user || user.role !== UserRole.ADMIN) {
        throw new AuthorizationException('Only admins can delete categories');
      }

      // Check if category can be deleted
      const hasChildren = await this.categoryRepository.hasChildren(id);
      if (hasChildren) {
        throw new ValidationException('Cannot delete category with child categories');
      }

      const hasProducts = await this.categoryRepository.hasProducts(id);
      if (hasProducts) {
        throw new ValidationException('Cannot delete category with products');
      }

      const success = await this.categoryRepository.delete(id);
      if (!success) {
        throw new BusinessException('Failed to delete category');
      }

      return true;
    } catch (error) {
      if (error instanceof ValidationException || error instanceof AuthorizationException) {
        throw error;
      }
      throw new BusinessException(`Failed to delete category: ${error.message}`);
    }
  }

  /**
   * Get active categories
   */
  async getActiveCategories(options = {}) {
    try {
      return await this.categoryRepository.findActive(options);
    } catch (error) {
      throw new BusinessException(`Failed to get active categories: ${error.message}`);
    }
  }

  /**
   * Get categories by parent
   */
  async getCategoriesByParent(parentId, options = {}) {
    try {
      return await this.categoryRepository.findByParent(parentId, options);
    } catch (error) {
      throw new BusinessException(`Failed to get categories by parent: ${error.message}`);
    }
  }

  /**
   * Get root categories
   */
  async getRootCategories(options = {}) {
    try {
      return await this.categoryRepository.findRootCategories(options);
    } catch (error) {
      throw new BusinessException(`Failed to get root categories: ${error.message}`);
    }
  }

  /**
   * Get category tree
   */
  async getCategoryTree(rootId = null) {
    try {
      return await this.categoryRepository.getCategoryTree(rootId);
    } catch (error) {
      throw new BusinessException(`Failed to get category tree: ${error.message}`);
    }
  }

  /**
   * Get category hierarchy
   */
  async getCategoryHierarchy(id) {
    try {
      return await this.categoryRepository.getHierarchy(id);
    } catch (error) {
      throw new BusinessException(`Failed to get category hierarchy: ${error.message}`);
    }
  }

  /**
   * Search categories
   */
  async searchCategories(query, options = {}) {
    try {
      return await this.categoryRepository.search(query, options);
    } catch (error) {
      throw new BusinessException(`Failed to search categories: ${error.message}`);
    }
  }

  /**
   * Get featured categories
   */
  async getFeaturedCategories(limit = 10) {
    try {
      return await this.categoryRepository.findFeatured(limit);
    } catch (error) {
      throw new BusinessException(`Failed to get featured categories: ${error.message}`);
    }
  }

  /**
   * Get categories with product counts
   */
  async getCategoriesWithProductCounts(options = {}) {
    try {
      return await this.categoryRepository.findWithProductCounts(options);
    } catch (error) {
      throw new BusinessException(`Failed to get categories with product counts: ${error.message}`);
    }
  }

  /**
   * Update category status
   */
  async updateCategoryStatus(id, isActive, updatedBy) {
    try {
      const user = await this.userRepository.findById(updatedBy);
      if (!user || user.role !== UserRole.ADMIN) {
        throw new AuthorizationException('Only admins can update category status');
      }

      const category = await this.categoryRepository.updateStatus(id, isActive);
      if (!category) {
        throw new ValidationException('Category not found');
      }

      return category;
    } catch (error) {
      if (error instanceof ValidationException || error instanceof AuthorizationException) {
        throw error;
      }
      throw new BusinessException(`Failed to update category status: ${error.message}`);
    }
  }

  /**
   * Reorder categories
   */
  async reorderCategories(orderData, updatedBy) {
    try {
      const user = await this.userRepository.findById(updatedBy);
      if (!user || user.role !== UserRole.ADMIN) {
        throw new AuthorizationException('Only admins can reorder categories');
      }

      const success = await this.categoryRepository.reorder(orderData);
      if (!success) {
        throw new BusinessException('Failed to reorder categories');
      }

      return true;
    } catch (error) {
      if (error instanceof AuthorizationException) {
        throw error;
      }
      throw new BusinessException(`Failed to reorder categories: ${error.message}`);
    }
  }

  /**
   * Move category to new parent
   */
  async moveCategoryToParent(id, newParentId, updatedBy) {
    try {
      const user = await this.userRepository.findById(updatedBy);
      if (!user || user.role !== UserRole.ADMIN) {
        throw new AuthorizationException('Only admins can move categories');
      }

      const category = await this.categoryRepository.moveToParent(id, newParentId);
      if (!category) {
        throw new ValidationException('Category not found');
      }

      return category;
    } catch (error) {
      if (error instanceof ValidationException || error instanceof AuthorizationException) {
        throw error;
      }
      throw new BusinessException(`Failed to move category: ${error.message}`);
    }
  }

  /**
   * Get category statistics
   */
  async getCategoryStatistics(id) {
    try {
      return await this.categoryRepository.getStatistics(id);
    } catch (error) {
      throw new BusinessException(`Failed to get category statistics: ${error.message}`);
    }
  }

  /**
   * Bulk update categories
   */
  async bulkUpdateCategories(updates, updatedBy) {
    try {
      const user = await this.userRepository.findById(updatedBy);
      if (!user || user.role !== UserRole.ADMIN) {
        throw new AuthorizationException('Only admins can perform bulk updates');
      }

      return await this.categoryRepository.bulkUpdate(updates);
    } catch (error) {
      if (error instanceof AuthorizationException) {
        throw error;
      }
      throw new BusinessException(`Failed to bulk update categories: ${error.message}`);
    }
  }

  /**
   * Update all product counts
   */
  async updateAllProductCounts(updatedBy) {
    try {
      const user = await this.userRepository.findById(updatedBy);
      if (!user || user.role !== UserRole.ADMIN) {
        throw new AuthorizationException('Only admins can update product counts');
      }

      await this.categoryRepository.updateAllProductCounts();
      return true;
    } catch (error) {
      if (error instanceof AuthorizationException) {
        throw error;
      }
      throw new BusinessException(`Failed to update product counts: ${error.message}`);
    }
  }

  /**
   * Check if slug is available
   */
  async isSlugAvailable(slug, excludeId = null) {
    try {
      return await this.categoryRepository.isSlugUnique(slug, excludeId);
    } catch (error) {
      throw new BusinessException(`Failed to check slug availability: ${error.message}`);
    }
  }

  /**
   * Helper method to check if category is descendant of another
   */
  async isDescendantOf(categoryId, ancestorId) {
    try {
      const hierarchy = await this.categoryRepository.getHierarchy(categoryId);
      return hierarchy.some(cat => cat.id === ancestorId);
    } catch (error) {
      return false;
    }
  }
}

module.exports = CategoryService;
