const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:5000/api';
let adminToken = '';

async function deleteTestCategory() {
  console.log('🗑️ Deleting test category...\n');

  try {
    // 1. Login to get admin token
    console.log('1️⃣ Logging in as admin...');
    const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'eEf8kbpE9G8XhiL'
      })
    });

    const loginData = await loginResponse.json();
    adminToken = loginData.data.token;
    console.log('✅ Admin login successful\n');

    // 2. Get all categories to find test category
    console.log('2️⃣ Finding test category...');
    const listResponse = await fetch(`${BASE_URL}/categories`);
    const listData = await listResponse.json();
    const categories = listData.data.categories;
    
    console.log(`📂 Found ${categories.length} categories:`);
    categories.forEach((cat, index) => {
      console.log(`  ${index + 1}. ${cat.name} (${cat.slug}) - ID: ${cat.id} - Active: ${cat.isActive}`);
    });
    console.log('');

    // 3. Find and delete test category
    const testCategory = categories.find(cat => cat.slug === 'test-crud-kategori');
    
    if (testCategory && testCategory.id) {
      console.log('3️⃣ Deleting test category...');
      console.log(`   Category: ${testCategory.name}`);
      console.log(`   ID: ${testCategory.id}`);
      console.log(`   Slug: ${testCategory.slug}`);
      
      const deleteResponse = await fetch(`${BASE_URL}/categories/${testCategory.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${adminToken}`,
          'Content-Type': 'application/json'
        }
      });

      const deleteData = await deleteResponse.json();
      
      if (deleteResponse.ok) {
        console.log('✅ Test category deleted successfully!');
      } else {
        console.log('❌ Failed to delete test category:', deleteData.message);
      }
    } else {
      console.log('ℹ️ Test category not found (already deleted or doesn\'t exist)');
    }

    // 4. Verify deletion by listing categories again
    console.log('\n4️⃣ Verifying deletion...');
    const finalListResponse = await fetch(`${BASE_URL}/categories`);
    const finalListData = await finalListResponse.json();
    const finalCategories = finalListData.data.categories;
    
    console.log(`📂 Final category count: ${finalCategories.length}`);
    finalCategories.forEach((cat, index) => {
      console.log(`  ${index + 1}. ${cat.name} (${cat.slug}) - Active: ${cat.isActive}`);
    });

    const stillExists = finalCategories.find(cat => cat.slug === 'test-crud-kategori');
    if (!stillExists) {
      console.log('\n✅ Test category successfully removed from database!');
    } else {
      console.log('\n❌ Test category still exists in database');
    }

    console.log('\n🎉 Cleanup completed!');

  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error('Stack:', error.stack);
  }
}

deleteTestCategory();
