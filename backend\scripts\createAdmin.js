/**
 * Create Admin User Script
 * Creates the default admin user for the system
 */

const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Import models
const User = require('../src/infrastructure/database/models/User');
const { UserRole, UserStatus, ApprovalStatus } = require('../src/shared/enums');

async function createAdminUser() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/robinhoot', {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });

    console.log('✅ Connected to MongoDB');

    // Check if admin already exists
    const existingAdmin = await User.findOne({ email: '<EMAIL>' });
    
    if (existingAdmin) {
      console.log('❌ Admin user already exists!');
      console.log('Admin details:', {
        id: existingAdmin._id,
        email: existingAdmin.email,
        username: existingAdmin.username,
        role: existingAdmin.role,
        status: existingAdmin.status,
        approvalStatus: existingAdmin.approvalStatus
      });
      process.exit(0);
    }

    // Create admin user
    const adminUser = new User({
      name: 'Admin User',
      email: '<EMAIL>',
      username: 'admin',
      password: 'eEf8kbpE9G8XhiL', // This will be hashed automatically
      role: UserRole.ADMIN,
      status: UserStatus.ACTIVE,
      approvalStatus: ApprovalStatus.APPROVED,
      isEmailVerified: true,
      canBid: true,
      canParticipateInLottery: true,
      approvedAt: new Date()
    });

    await adminUser.save();

    console.log('✅ Admin user created successfully!');
    console.log('Admin details:', {
      id: adminUser._id,
      email: adminUser.email,
      username: adminUser.username,
      role: adminUser.role,
      status: adminUser.status,
      approvalStatus: adminUser.approvalStatus
    });

    console.log('\n🔑 Login credentials:');
    console.log('Email: <EMAIL>');
    console.log('Password: eEf8kbpE9G8XhiL');

  } catch (error) {
    console.error('❌ Error creating admin user:', error);
  } finally {
    await mongoose.disconnect();
    console.log('✅ Disconnected from MongoDB');
    process.exit(0);
  }
}

// Run the script
createAdminUser();
