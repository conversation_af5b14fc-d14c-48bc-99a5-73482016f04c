const mongoose = require('mongoose');
const logger = require('../utils/logger');
const config = require('./config');

/**
 * MongoDB veritabanı bağlantısını yöneten modül
 */
const connectDB = async () => {
  try {
    const conn = await mongoose.connect(config.mongoUri, config.mongoOptions);
    
    logger.info(`MongoDB Atlas bağlantısı başarılı: ${conn.connection.host}`);
    
    // Bağlantı olaylarını dinle
    mongoose.connection.on('error', (err) => {
      logger.error(`MongoDB bağlantı hatası: ${err}`);
    });
    
    mongoose.connection.on('disconnected', () => {
      logger.warn('MongoDB bağlantısı kesildi');
    });
    
    // Uygulama kapandığında bağlantıyı düzgün şekilde kapat
    process.on('SIGINT', async () => {
      await mongoose.connection.close();
      logger.info('MongoDB bağlantısı kapatıldı (SIGINT)');
      process.exit(0);
    });
    
    return conn;
  } catch (error) {
    logger.error(`MongoDB bağlantı hatası: ${error.message}`);
    process.exit(1);
  }
};

module.exports = connectDB; 