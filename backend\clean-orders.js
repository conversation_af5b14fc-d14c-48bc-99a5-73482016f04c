const mongoose = require('mongoose');
require('dotenv').config();

async function cleanOrders() {
  try {
    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb+srv://emrevaryemez22:<EMAIL>/robinhoot?retryWrites=true&w=majority');
    console.log('✅ Connected to database');

    // Get database
    const db = mongoose.connection.db;

    // Delete all orders
    const result = await db.collection('orders').deleteMany({});
    console.log(`🗑️ Deleted ${result.deletedCount} orders`);

    await mongoose.disconnect();
    console.log('✅ Disconnected from database');
    console.log('🎉 Order cleanup completed!');

  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

cleanOrders();
