const mongoose = require('mongoose');
require('dotenv').config();

async function debugCategories() {
  try {
    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb+srv://emrevaryemez22:<EMAIL>/robinhoot?retryWrites=true&w=majority');
    console.log('✅ Connected to database');

    // Get categories collection directly
    const db = mongoose.connection.db;
    const categories = await db.collection('categories').find({}).toArray();
    
    console.log('📊 Total categories in database:', categories.length);
    
    if (categories.length > 0) {
      console.log('\n📂 Categories in database:');
      categories.forEach((cat, index) => {
        console.log(`${index + 1}. ${cat.name} (${cat.slug})`);
        console.log(`   _id: ${cat._id}`);
        console.log(`   isActive: ${cat.isActive}`);
        console.log(`   isFeatured: ${cat.isFeatured}`);
        console.log('');
      });
    }

    await mongoose.disconnect();
    console.log('✅ Disconnected from database');

  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

debugCategories();
