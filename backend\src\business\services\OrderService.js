/**
 * Order Service
 * Business logic for order operations
 */

const { BusinessException, ValidationException, AuthorizationException } = require('../../core/exceptions');
const { OrderStatus, PaymentStatus, ShippingStatus, UserRole } = require('../../shared/enums');
const Order = require('../../core/entities/Order');

class OrderService {
  constructor(orderRepository, userRepository, productRepository, emailService) {
    this.orderRepository = orderRepository;
    this.userRepository = userRepository;
    this.productRepository = productRepository;
    this.emailService = emailService;
  }

  /**
   * Get all orders
   */
  async getAllOrders(options = {}) {
    try {
      return await this.orderRepository.findAll(options);
    } catch (error) {
      throw new BusinessException(`Failed to get orders: ${error.message}`);
    }
  }

  /**
   * Get order by ID
   */
  async getOrderById(id) {
    try {
      const order = await this.orderRepository.findById(id);
      if (!order) {
        throw new ValidationException('Order not found');
      }
      return order;
    } catch (error) {
      if (error instanceof ValidationException) throw error;
      throw new BusinessException(`Failed to get order: ${error.message}`);
    }
  }

  /**
   * Get order by order number
   */
  async getOrderByOrderNumber(orderNumber) {
    try {
      const order = await this.orderRepository.findByOrderNumber(orderNumber);
      if (!order) {
        throw new ValidationException('Order not found');
      }
      return order;
    } catch (error) {
      if (error instanceof ValidationException) throw error;
      throw new BusinessException(`Failed to get order: ${error.message}`);
    }
  }

  /**
   * Create new order
   */
  async createOrder(orderData, userId) {
    try {
      // Validate user
      const user = await this.userRepository.findById(userId);
      if (!user) {
        throw new ValidationException('User not found');
      }

      if (!user.isActive()) {
        throw new AuthorizationException('User account is not active');
      }

      // Validate and process order items
      const processedItems = await this.validateAndProcessItems(orderData.items);

      // Calculate totals
      const subtotal = processedItems.reduce((sum, item) => sum + item.total, 0);
      const tax = this.calculateTax(subtotal, orderData.shippingAddress);
      const shipping = await this.calculateShipping(processedItems, orderData.shippingAddress);
      const discount = orderData.discount || 0;
      const total = subtotal + tax + shipping - discount;

      // Create order entity
      console.log('🔍 OrderService.createOrder - Creating Order entity with total:', total);
      const order = new Order({
        userId,
        items: processedItems,
        totalAmount: total,
        shippingAddress: orderData.shippingAddress,
        billingAddress: orderData.billingAddress,
        paymentMethod: orderData.paymentMethod,
        notes: orderData.notes || '',
        shippingCost: shipping,
        taxAmount: tax,
        discountAmount: discount,
        couponCode: orderData.couponCode
      });
      console.log('🔍 OrderService.createOrder - Order entity created, totalAmount:', order.totalAmount);

      // Save order
      const savedOrder = await this.orderRepository.create(order.toPersistence());

      // Update product inventory
      await this.updateProductInventory(processedItems, 'decrease');

      // Send order confirmation email
      this.sendOrderConfirmationEmail(user, savedOrder);

      return savedOrder;
    } catch (error) {
      if (error instanceof ValidationException || error instanceof AuthorizationException) {
        throw error;
      }
      throw new BusinessException(`Failed to create order: ${error.message}`);
    }
  }

  /**
   * Update order
   */
  async updateOrder(id, updateData, updatedBy) {
    try {
      const order = await this.orderRepository.findById(id);
      if (!order) {
        throw new ValidationException('Order not found');
      }

      // Check permissions
      const user = await this.userRepository.findById(updatedBy);
      if (!user) {
        throw new ValidationException('User not found');
      }

      // Only admin or order owner can update (and only if editable)
      if (user.role !== UserRole.ADMIN && order.userId !== updatedBy) {
        throw new AuthorizationException('You can only update your own orders');
      }

      // Check if order is editable
      if (!this.isOrderEditable(order)) {
        throw new ValidationException('Order cannot be edited in current status');
      }

      const updatedOrder = await this.orderRepository.update(id, updateData);
      return updatedOrder;
    } catch (error) {
      if (error instanceof ValidationException || error instanceof AuthorizationException) {
        throw error;
      }
      throw new BusinessException(`Failed to update order: ${error.message}`);
    }
  }

  /**
   * Cancel order
   */
  async cancelOrder(id, reason, cancelledBy) {
    try {
      const order = await this.orderRepository.findById(id);
      if (!order) {
        throw new ValidationException('Order not found');
      }

      // Check permissions
      const user = await this.userRepository.findById(cancelledBy);
      if (!user) {
        throw new ValidationException('User not found');
      }

      if (user.role !== UserRole.ADMIN && order.userId !== cancelledBy) {
        throw new AuthorizationException('You can only cancel your own orders');
      }

      // Check if order can be cancelled
      if (!this.canOrderBeCancelled(order)) {
        throw new ValidationException('Order cannot be cancelled in current status');
      }

      // Cancel order
      const cancelledOrder = await this.orderRepository.cancel(id, reason);

      // Restore product inventory
      await this.updateProductInventory(order.items, 'increase');

      // Send cancellation email
      this.sendOrderCancellationEmail(user, cancelledOrder, reason);

      return cancelledOrder;
    } catch (error) {
      if (error instanceof ValidationException || error instanceof AuthorizationException) {
        throw error;
      }
      throw new BusinessException(`Failed to cancel order: ${error.message}`);
    }
  }

  /**
   * Get user orders
   */
  async getUserOrders(userId, options = {}) {
    try {
      return await this.orderRepository.findByUser(userId, options);
    } catch (error) {
      throw new BusinessException(`Failed to get user orders: ${error.message}`);
    }
  }

  /**
   * Get seller orders
   */
  async getSellerOrders(sellerId, options = {}) {
    try {
      return await this.orderRepository.findBySeller(sellerId, options);
    } catch (error) {
      throw new BusinessException(`Failed to get seller orders: ${error.message}`);
    }
  }

  /**
   * Get orders by status
   */
  async getOrdersByStatus(status, options = {}) {
    try {
      return await this.orderRepository.findByStatus(status, options);
    } catch (error) {
      throw new BusinessException(`Failed to get orders by status: ${error.message}`);
    }
  }

  /**
   * Search orders
   */
  async searchOrders(query, options = {}) {
    try {
      return await this.orderRepository.search(query, options);
    } catch (error) {
      throw new BusinessException(`Failed to search orders: ${error.message}`);
    }
  }

  /**
   * Update order status (Admin/Seller)
   */
  async updateOrderStatus(id, status, updatedBy) {
    try {
      const order = await this.orderRepository.findById(id);
      if (!order) {
        throw new ValidationException('Order not found');
      }

      const user = await this.userRepository.findById(updatedBy);
      if (!user) {
        throw new ValidationException('User not found');
      }

      // Check permissions
      if (user.role !== UserRole.ADMIN) {
        // Sellers can only update orders containing their products
        const hasSellerItems = order.items.some(item => item.sellerId === updatedBy);
        if (!hasSellerItems) {
          throw new AuthorizationException('You can only update orders containing your products');
        }
      }

      // Validate status transition
      this.validateStatusTransition(order.status, status);

      const updatedOrder = await this.orderRepository.updateStatus(id, status);

      // Send status update notification
      this.sendOrderStatusUpdateEmail(order.userId, updatedOrder);

      return updatedOrder;
    } catch (error) {
      if (error instanceof ValidationException || error instanceof AuthorizationException) {
        throw error;
      }
      throw new BusinessException(`Failed to update order status: ${error.message}`);
    }
  }

  /**
   * Update payment status
   */
  async updatePaymentStatus(id, paymentStatus, updatedBy) {
    try {
      const user = await this.userRepository.findById(updatedBy);
      if (!user || user.role !== UserRole.ADMIN) {
        throw new AuthorizationException('Only admins can update payment status');
      }

      const order = await this.orderRepository.updatePaymentStatus(id, paymentStatus);
      if (!order) {
        throw new ValidationException('Order not found');
      }

      // Auto-update order status based on payment
      if (paymentStatus === PaymentStatus.COMPLETED && order.status === OrderStatus.PENDING) {
        await this.orderRepository.updateStatus(id, OrderStatus.CONFIRMED);
      }

      return order;
    } catch (error) {
      if (error instanceof ValidationException || error instanceof AuthorizationException) {
        throw error;
      }
      throw new BusinessException(`Failed to update payment status: ${error.message}`);
    }
  }

  /**
   * Add tracking information
   */
  async addTrackingInfo(id, trackingData, updatedBy) {
    try {
      const order = await this.orderRepository.findById(id);
      if (!order) {
        throw new ValidationException('Order not found');
      }

      const user = await this.userRepository.findById(updatedBy);
      if (!user) {
        throw new ValidationException('User not found');
      }

      // Check permissions
      if (user.role !== UserRole.ADMIN) {
        const hasSellerItems = order.items.some(item => item.sellerId === updatedBy);
        if (!hasSellerItems) {
          throw new AuthorizationException('You can only add tracking for orders containing your products');
        }
      }

      const updatedOrder = await this.orderRepository.addTrackingInfo(id, trackingData);

      // Send tracking notification
      this.sendTrackingInfoEmail(order.userId, updatedOrder);

      return updatedOrder;
    } catch (error) {
      if (error instanceof ValidationException || error instanceof AuthorizationException) {
        throw error;
      }
      throw new BusinessException(`Failed to add tracking info: ${error.message}`);
    }
  }

  /**
   * Get order statistics
   */
  async getOrderStatistics(id) {
    try {
      return await this.orderRepository.getStatistics(id);
    } catch (error) {
      throw new BusinessException(`Failed to get order statistics: ${error.message}`);
    }
  }

  /**
   * Get order counts by status
   */
  async getOrderCounts(userId = null) {
    try {
      return await this.orderRepository.getCountByStatus(userId);
    } catch (error) {
      throw new BusinessException(`Failed to get order counts: ${error.message}`);
    }
  }

  /**
   * Get revenue statistics
   */
  async getRevenueStatistics(options = {}) {
    try {
      return await this.orderRepository.getRevenueStatistics(options);
    } catch (error) {
      throw new BusinessException(`Failed to get revenue statistics: ${error.message}`);
    }
  }

  // Helper methods

  /**
   * Validate and process order items
   */
  async validateAndProcessItems(items) {
    const processedItems = [];

    for (const item of items) {
      // Get product details
      const product = await this.productRepository.findById(item.productId);
      if (!product) {
        throw new ValidationException(`Product not found: ${item.productId}`);
      }

      if (product.status !== 'active') {
        throw new ValidationException(`Product is not available: ${product.name}`);
      }

      // Check inventory
      if (product.stock < item.quantity) {
        throw new ValidationException(`Insufficient stock for product: ${product.name}`);
      }

      processedItems.push({
        productId: item.productId,
        sellerId: product.sellerId,
        name: product.name,
        price: item.price || product.price,
        quantity: item.quantity,
        total: (item.price || product.price) * item.quantity
      });
    }

    return processedItems;
  }

  /**
   * Calculate tax
   */
  calculateTax(subtotal, shippingAddress) {
    // Simple tax calculation
    const taxRate = this.getTaxRate(shippingAddress.country);
    return subtotal * taxRate;
  }

  /**
   * Get tax rate
   */
  getTaxRate(country) {
    const taxRates = {
      'US': 0.08,
      'CA': 0.12,
      'GB': 0.20,
      'DE': 0.19
    };
    return taxRates[country] || 0.1;
  }

  /**
   * Calculate shipping
   */
  async calculateShipping(items, shippingAddress) {
    // Simple shipping calculation
    const baseShipping = 5.00;
    const itemCount = items.reduce((sum, item) => sum + item.quantity, 0);
    const itemShipping = itemCount * 1.00;

    // International shipping
    if (shippingAddress.country !== 'US') {
      return baseShipping + itemShipping + 15.00;
    }

    return baseShipping + itemShipping;
  }

  /**
   * Update product inventory
   */
  async updateProductInventory(items, operation) {
    for (const item of items) {
      const product = await this.productRepository.findById(item.productId);
      if (product) {
        const newStock = operation === 'decrease'
          ? product.stock - item.quantity
          : product.stock + item.quantity;

        await this.productRepository.updateInventory(item.productId, newStock);
      }
    }
  }

  /**
   * Check if order is editable
   */
  isOrderEditable(order) {
    return order.status === OrderStatus.PENDING || order.status === OrderStatus.CONFIRMED;
  }

  /**
   * Check if order can be cancelled
   */
  canOrderBeCancelled(order) {
    return order.status !== OrderStatus.DELIVERED &&
           order.status !== OrderStatus.CANCELLED;
  }

  /**
   * Validate status transition
   */
  validateStatusTransition(currentStatus, newStatus) {
    const validTransitions = {
      [OrderStatus.PENDING]: [OrderStatus.CONFIRMED, OrderStatus.CANCELLED],
      [OrderStatus.CONFIRMED]: [OrderStatus.PROCESSING, OrderStatus.CANCELLED],
      [OrderStatus.PROCESSING]: [OrderStatus.SHIPPED, OrderStatus.CANCELLED],
      [OrderStatus.SHIPPED]: [OrderStatus.DELIVERED],
      [OrderStatus.DELIVERED]: [OrderStatus.REFUNDED],
      [OrderStatus.CANCELLED]: [],
      [OrderStatus.REFUNDED]: []
    };

    const allowedTransitions = validTransitions[currentStatus] || [];

    if (!allowedTransitions.includes(newStatus)) {
      throw new ValidationException(`Invalid status transition from ${currentStatus} to ${newStatus}`);
    }
  }

  // Email notification methods

  async sendOrderConfirmationEmail(user, order) {
    try {
      if (user.email) {
        await this.emailService.sendNotificationEmail(
          user.email,
          'Order Confirmation',
          `Your order ${order.orderNumber} has been confirmed. Total: $${order.totalAmount}`
        );
      }
    } catch (error) {
      console.error(`Failed to send order confirmation email: ${error.message}`);
    }
  }

  async sendOrderCancellationEmail(user, order, reason) {
    try {
      if (user.email) {
        await this.emailService.sendNotificationEmail(
          user.email,
          'Order Cancelled',
          `Your order ${order.orderNumber} has been cancelled. Reason: ${reason}`
        );
      }
    } catch (error) {
      console.error(`Failed to send order cancellation email: ${error.message}`);
    }
  }

  async sendOrderStatusUpdateEmail(userId, order) {
    try {
      const user = await this.userRepository.findById(userId);
      if (user && user.email) {
        await this.emailService.sendNotificationEmail(
          user.email,
          'Order Status Update',
          `Your order ${order.orderNumber} status has been updated to: ${order.status}`
        );
      }
    } catch (error) {
      console.error(`Failed to send order status update email: ${error.message}`);
    }
  }

  async sendTrackingInfoEmail(userId, order) {
    try {
      const user = await this.userRepository.findById(userId);
      if (user && user.email && order.trackingNumber) {
        await this.emailService.sendNotificationEmail(
          user.email,
          'Tracking Information',
          `Your order ${order.orderNumber} has been shipped. Tracking number: ${order.trackingNumber}`
        );
      }
    } catch (error) {
      console.error(`Failed to send tracking info email: ${error.message}`);
    }
  }
}

module.exports = OrderService;
