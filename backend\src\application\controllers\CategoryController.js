/**
 * Category Controller
 * Application layer - HTTP request handling for category operations
 */

const { HttpStatus } = require('../../shared/enums');
const { PAGINATION } = require('../../shared/constants');

class CategoryController {
  constructor(categoryService) {
    this.categoryService = categoryService;
  }

  /**
   * Get all categories
   */
  async getAllCategories(req, res, next) {
    try {
      console.log('🔥 CategoryController.getAllCategories called');
      console.log('🔥 Query params:', req.query);
      const {
        status,
        isActive, // Backward compatibility
        parentId,
        level,
        sortBy = 'order',
        sortOrder = 'asc',
        page = PAGINATION.DEFAULT_PAGE,
        limit = 100, // Increased default limit for admin operations
        search
      } = req.query;

      const options = {
        page: parseInt(page),
        limit: Math.min(parseInt(limit), PAGINATION.MAX_LIMIT),
        sortBy,
        sortOrder,
        status: status || (isActive !== undefined ? (isActive === 'true' ? 'active' : 'inactive') : undefined),
        parentId: parentId || undefined,
        level: level ? parseInt(level) : undefined
      };

      let result;
      if (search) {
        result = await this.categoryService.searchCategories(search, options);
      } else {
        result = await this.categoryService.getAllCategories(options);
      }

      console.log('🔥 CategoryService result:', result);
      console.log('🔥 CategoryService result.categories length:', result.categories?.length || 0);
      console.log('🔥 CategoryService result.categories[0]:', result.categories?.[0]);

      res.status(HttpStatus.OK).json({
        success: true,
        data: result
      });
    } catch (error) {
      console.error('❌ CategoryController.getAllCategories error:', error);
      next(error);
    }
  }

  /**
   * Get category by ID
   */
  async getCategoryById(req, res, next) {
    try {
      const { id } = req.params;
      const category = await this.categoryService.getCategoryById(id);

      res.status(HttpStatus.OK).json({
        success: true,
        data: { category }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get category by slug
   */
  async getCategoryBySlug(req, res, next) {
    try {
      const { slug } = req.params;
      const category = await this.categoryService.getCategoryBySlug(slug);

      res.status(HttpStatus.OK).json({
        success: true,
        data: { category }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Create new category
   */
  async createCategory(req, res, next) {
    try {
      const category = await this.categoryService.createCategory(req.body, req.user.id);

      res.status(HttpStatus.CREATED).json({
        success: true,
        message: 'Category created successfully',
        data: { category }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update category
   */
  async updateCategory(req, res, next) {
    try {
      const { id } = req.params;
      const category = await this.categoryService.updateCategory(id, req.body, req.user.id);

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'Category updated successfully',
        data: { category }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete category
   */
  async deleteCategory(req, res, next) {
    try {
      const { id } = req.params;
      await this.categoryService.deleteCategory(id, req.user.id);

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'Category deleted successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get active categories
   */
  async getActiveCategories(req, res, next) {
    try {
      const {
        limit,
        sortBy = 'order',
        sortOrder = 'asc'
      } = req.query;

      const options = {
        limit: limit ? parseInt(limit) : undefined,
        sortBy,
        sortOrder
      };

      const categories = await this.categoryService.getActiveCategories(options);

      res.status(HttpStatus.OK).json({
        success: true,
        data: { categories }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get categories by parent
   */
  async getCategoriesByParent(req, res, next) {
    try {
      const { parentId } = req.params;
      const {
        sortBy = 'order',
        sortOrder = 'asc'
      } = req.query;

      const options = { sortBy, sortOrder };
      const categories = await this.categoryService.getCategoriesByParent(parentId, options);

      res.status(HttpStatus.OK).json({
        success: true,
        data: { categories }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get root categories
   */
  async getRootCategories(req, res, next) {
    try {
      const {
        sortBy = 'order',
        sortOrder = 'asc'
      } = req.query;

      const options = { sortBy, sortOrder };
      const categories = await this.categoryService.getRootCategories(options);

      res.status(HttpStatus.OK).json({
        success: true,
        data: { categories }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get category tree
   */
  async getCategoryTree(req, res, next) {
    try {
      const { rootId } = req.query;
      const tree = await this.categoryService.getCategoryTree(rootId || null);

      res.status(HttpStatus.OK).json({
        success: true,
        data: { tree }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get category hierarchy
   */
  async getCategoryHierarchy(req, res, next) {
    try {
      const { id } = req.params;
      const hierarchy = await this.categoryService.getCategoryHierarchy(id);

      res.status(HttpStatus.OK).json({
        success: true,
        data: { hierarchy }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get featured categories
   */
  async getFeaturedCategories(req, res, next) {
    try {
      const { limit = 10 } = req.query;
      const categories = await this.categoryService.getFeaturedCategories(parseInt(limit));

      res.status(HttpStatus.OK).json({
        success: true,
        data: { categories }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get categories with product counts
   */
  async getCategoriesWithProductCounts(req, res, next) {
    try {
      const categories = await this.categoryService.getCategoriesWithProductCounts();

      res.status(HttpStatus.OK).json({
        success: true,
        data: { categories }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update category status (Admin)
   */
  async updateCategoryStatus(req, res, next) {
    try {
      const { id } = req.params;
      const { isActive } = req.body;

      if (isActive === undefined) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          message: 'isActive status is required'
        });
      }

      const category = await this.categoryService.updateCategoryStatus(id, isActive, req.user.id);

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'Category status updated successfully',
        data: { category }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Reorder categories (Admin)
   */
  async reorderCategories(req, res, next) {
    try {
      const { orderData } = req.body;

      if (!orderData || !Array.isArray(orderData)) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          message: 'Order data array is required'
        });
      }

      await this.categoryService.reorderCategories(orderData, req.user.id);

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'Categories reordered successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Move category to new parent (Admin)
   */
  async moveCategoryToParent(req, res, next) {
    try {
      const { id } = req.params;
      const { newParentId } = req.body;

      const category = await this.categoryService.moveCategoryToParent(id, newParentId, req.user.id);

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'Category moved successfully',
        data: { category }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get category statistics
   */
  async getCategoryStatistics(req, res, next) {
    try {
      const { id } = req.params;
      const statistics = await this.categoryService.getCategoryStatistics(id);

      res.status(HttpStatus.OK).json({
        success: true,
        data: { statistics }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Bulk update categories (Admin)
   */
  async bulkUpdateCategories(req, res, next) {
    try {
      const { updates } = req.body;

      if (!updates || !Array.isArray(updates)) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          message: 'Updates array is required'
        });
      }

      const result = await this.categoryService.bulkUpdateCategories(updates, req.user.id);

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'Categories updated successfully',
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update all product counts (Admin)
   */
  async updateAllProductCounts(req, res, next) {
    try {
      await this.categoryService.updateAllProductCounts(req.user.id);

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'Product counts updated successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Check slug availability
   */
  async checkSlugAvailability(req, res, next) {
    try {
      const { slug } = req.params;
      const { excludeId } = req.query;

      const isAvailable = await this.categoryService.isSlugAvailable(slug, excludeId);

      res.status(HttpStatus.OK).json({
        success: true,
        data: { available: isAvailable }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Search categories
   */
  async searchCategories(req, res, next) {
    try {
      const { q: query } = req.query;

      if (!query) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          message: 'Search query is required'
        });
      }

      const {
        isActive = true,
        sortBy = 'relevance',
        sortOrder = 'desc',
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT
      } = req.query;

      const options = {
        page: parseInt(page),
        limit: Math.min(parseInt(limit), PAGINATION.MAX_LIMIT),
        sortBy,
        sortOrder,
        isActive: isActive === 'true'
      };

      const result = await this.categoryService.searchCategories(query, options);

      res.status(HttpStatus.OK).json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = CategoryController;
