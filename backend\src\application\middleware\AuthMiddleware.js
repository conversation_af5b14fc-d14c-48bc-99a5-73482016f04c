/**
 * Authentication Middleware
 * JWT token verification and user authentication
 */

const { AuthenticationException, AuthorizationException } = require('../../core/exceptions');
const { HttpStatus, UserRole } = require('../../shared/enums');

class AuthMiddleware {
  constructor(tokenService, userRepository) {
    this.tokenService = tokenService;
    this.userRepository = userRepository;
  }

  /**
   * JWT token doğrulama middleware
   */
  authenticate() {
    return async (req, res, next) => {
      try {
        const token = this.extractToken(req);
        
        if (!token) {
          throw new AuthenticationException('Access token is required');
        }

        // Token'ı doğrula
        const decoded = this.tokenService.verifyAccessToken(token);
        
        // Kullanıcıyı veritabanından getir
        const user = await this.userRepository.findById(decoded.id);
        
        if (!user) {
          throw new AuthenticationException('User not found');
        }

        if (!user.isActive()) {
          throw new AuthenticationException('User account is not active');
        }

        // Request'e kullanıcı bilgilerini ekle
        req.user = {
          id: user.id,
          email: user.email,
          username: user.username,
          role: user.role,
          canBid: user.canBid,
          canParticipateInLottery: user.canParticipateInLottery
        };

        next();
      } catch (error) {
        if (error instanceof AuthenticationException) {
          return res.status(error.statusCode).json({
            success: false,
            message: error.message,
            type: error.type
          });
        }

        return res.status(HttpStatus.UNAUTHORIZED).json({
          success: false,
          message: 'Authentication failed',
          error: error.message
        });
      }
    };
  }

  /**
   * Optional authentication - kullanıcı giriş yapmış olabilir veya olmayabilir
   */
  optionalAuth() {
    return async (req, res, next) => {
      try {
        const token = this.extractToken(req);
        
        if (token) {
          const decoded = this.tokenService.verifyAccessToken(token);
          const user = await this.userRepository.findById(decoded.id);
          
          if (user && user.isActive()) {
            req.user = {
              id: user.id,
              email: user.email,
              username: user.username,
              role: user.role,
              canBid: user.canBid,
              canParticipateInLottery: user.canParticipateInLottery
            };
          }
        }

        next();
      } catch (error) {
        // Optional auth'da hata olursa devam et
        next();
      }
    };
  }

  /**
   * Admin yetkisi kontrolü
   */
  requireAdmin() {
    return (req, res, next) => {
      try {
        if (!req.user) {
          throw new AuthenticationException('Authentication required');
        }

        if (req.user.role !== UserRole.ADMIN) {
          throw new AuthorizationException('Admin access required');
        }

        next();
      } catch (error) {
        return res.status(error.statusCode).json({
          success: false,
          message: error.message,
          type: error.type
        });
      }
    };
  }

  /**
   * Seller yetkisi kontrolü
   */
  requireSeller() {
    return (req, res, next) => {
      try {
        if (!req.user) {
          throw new AuthenticationException('Authentication required');
        }

        if (req.user.role !== UserRole.SELLER && req.user.role !== UserRole.ADMIN) {
          throw new AuthorizationException('Seller access required');
        }

        next();
      } catch (error) {
        return res.status(error.statusCode).json({
          success: false,
          message: error.message,
          type: error.type
        });
      }
    };
  }

  /**
   * Teklif verme yetkisi kontrolü
   */
  requireBidPermission() {
    return (req, res, next) => {
      try {
        if (!req.user) {
          throw new AuthenticationException('Authentication required');
        }

        if (!req.user.canBid) {
          throw new AuthorizationException('Bid permission required');
        }

        next();
      } catch (error) {
        return res.status(error.statusCode).json({
          success: false,
          message: error.message,
          type: error.type
        });
      }
    };
  }

  /**
   * Çekiliş katılım yetkisi kontrolü
   */
  requireLotteryPermission() {
    return (req, res, next) => {
      try {
        if (!req.user) {
          throw new AuthenticationException('Authentication required');
        }

        if (!req.user.canParticipateInLottery) {
          throw new AuthorizationException('Lottery participation permission required');
        }

        next();
      } catch (error) {
        return res.status(error.statusCode).json({
          success: false,
          message: error.message,
          type: error.type
        });
      }
    };
  }

  /**
   * Kaynak sahibi veya admin kontrolü
   */
  requireOwnershipOrAdmin(resourceUserIdField = 'userId') {
    return (req, res, next) => {
      try {
        if (!req.user) {
          throw new AuthenticationException('Authentication required');
        }

        // Admin her şeye erişebilir
        if (req.user.role === UserRole.ADMIN) {
          return next();
        }

        // Kaynak sahibi kontrolü
        const resourceUserId = req.params[resourceUserIdField] || req.body[resourceUserIdField];
        
        if (!resourceUserId) {
          throw new AuthorizationException('Resource user ID not found');
        }

        if (req.user.id !== resourceUserId) {
          throw new AuthorizationException('Access denied: You can only access your own resources');
        }

        next();
      } catch (error) {
        return res.status(error.statusCode).json({
          success: false,
          message: error.message,
          type: error.type
        });
      }
    };
  }

  /**
   * Multiple role kontrolü
   */
  requireRoles(allowedRoles) {
    return (req, res, next) => {
      try {
        if (!req.user) {
          throw new AuthenticationException('Authentication required');
        }

        if (!allowedRoles.includes(req.user.role)) {
          throw new AuthorizationException(`Access denied: Required roles: ${allowedRoles.join(', ')}`);
        }

        next();
      } catch (error) {
        return res.status(error.statusCode).json({
          success: false,
          message: error.message,
          type: error.type
        });
      }
    };
  }

  /**
   * Token'ı request'ten çıkar
   */
  extractToken(req) {
    // Authorization header'dan
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    // Cookie'den
    if (req.cookies && req.cookies.token) {
      return req.cookies.token;
    }

    // Query parameter'dan (sadece development için)
    if (process.env.NODE_ENV === 'development' && req.query.token) {
      return req.query.token;
    }

    return null;
  }
}

module.exports = AuthMiddleware;
