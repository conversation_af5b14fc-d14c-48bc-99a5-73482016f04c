/**
 * Order Database Model
 * Mongoose schema for order data persistence
 */

const mongoose = require('mongoose');
const { OrderStatus, PaymentStatus, ShippingStatus } = require('../../../shared/enums');
const { ORDER, VALIDATION } = require('../../../shared/constants');

const addressSchema = new mongoose.Schema({
  firstName: {
    type: String,
    required: true,
    trim: true,
    maxlength: VALIDATION.NAME_MAX_LENGTH
  },
  lastName: {
    type: String,
    required: true,
    trim: true,
    maxlength: VALIDATION.NAME_MAX_LENGTH
  },
  company: {
    type: String,
    trim: true,
    maxlength: 100
  },
  street: {
    type: String,
    trim: true,
    maxlength: 200
  },
  address1: {
    type: String,
    trim: true,
    maxlength: 200
  },
  address2: {
    type: String,
    trim: true,
    maxlength: 200
  },
  apartment: {
    type: String,
    trim: true,
    maxlength: 100
  },
  city: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  state: {
    type: String,
    trim: true,
    maxlength: 100
  },
  postalCode: {
    type: String,
    required: true,
    trim: true,
    maxlength: 20
  },
  country: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100
  },
  phone: {
    type: String,
    trim: true,
    maxlength: 20
  }
}, { _id: false });

const orderItemSchema = new mongoose.Schema({
  productId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
    required: true
  },
  sellerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  name: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    trim: true
  },
  sku: {
    type: String,
    trim: true
  },
  image: {
    type: String,
    trim: true
  },
  price: {
    type: Number,
    required: true,
    min: 0
  },
  originalPrice: {
    type: Number,
    min: 0
  },
  quantity: {
    type: Number,
    required: true,
    min: 1
  },
  total: {
    type: Number,
    required: true,
    min: 0
  },
  weight: {
    type: Number,
    min: 0
  },
  dimensions: {
    length: Number,
    width: Number,
    height: Number
  },
  metadata: {
    type: Map,
    of: mongoose.Schema.Types.Mixed
  }
}, { _id: false });

const paymentMethodSchema = new mongoose.Schema({
  type: {
    type: String,
    required: true,
    enum: ['credit_card', 'debit_card', 'paypal', 'bank_transfer', 'cash_on_delivery', 'crypto']
  },
  provider: {
    type: String,
    trim: true
  },
  last4: {
    type: String,
    trim: true
  },
  brand: {
    type: String,
    trim: true
  },
  expiryMonth: Number,
  expiryYear: Number,
  holderName: {
    type: String,
    trim: true
  }
}, { _id: false });

const refundSchema = new mongoose.Schema({
  amount: {
    type: Number,
    required: true,
    min: 0
  },
  reason: {
    type: String,
    required: true,
    trim: true
  },
  refundId: {
    type: String,
    trim: true
  },
  processedAt: {
    type: Date,
    default: Date.now
  },
  processedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, { timestamps: true });

const orderSchema = new mongoose.Schema({
  orderNumber: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    uppercase: true
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  items: [orderItemSchema],
  subtotal: {
    type: Number,
    required: true,
    min: 0
  },
  tax: {
    type: Number,
    default: 0,
    min: 0
  },
  shipping: {
    type: Number,
    default: 0,
    min: 0
  },
  discount: {
    type: Number,
    default: 0,
    min: 0
  },
  total: {
    type: Number,
    required: true,
    min: 0
  },
  currency: {
    type: String,
    default: 'USD',
    uppercase: true,
    length: 3
  },
  status: {
    type: String,
    enum: Object.values(OrderStatus),
    default: OrderStatus.PENDING
  },
  paymentStatus: {
    type: String,
    enum: Object.values(PaymentStatus),
    default: PaymentStatus.PENDING
  },
  shippingStatus: {
    type: String,
    enum: Object.values(ShippingStatus),
    default: ShippingStatus.PENDING
  },
  shippingAddress: {
    type: addressSchema,
    required: true
  },
  billingAddress: {
    type: addressSchema
  },
  paymentMethod: paymentMethodSchema,
  notes: {
    type: String,
    maxlength: 1000
  },
  internalNotes: {
    type: String,
    maxlength: 1000
  },
  couponCode: {
    type: String,
    trim: true,
    uppercase: true
  },
  trackingNumber: {
    type: String,
    trim: true
  },
  carrier: {
    type: String,
    trim: true
  },
  estimatedDelivery: Date,
  shippedAt: Date,
  deliveredAt: Date,
  cancelledAt: Date,
  cancellationReason: {
    type: String,
    trim: true
  },
  refunds: [refundSchema],
  source: {
    type: String,
    enum: ['web', 'mobile', 'admin', 'api'],
    default: 'web'
  },
  ipAddress: {
    type: String,
    trim: true
  },
  userAgent: {
    type: String,
    trim: true
  },
  metadata: {
    type: Map,
    of: mongoose.Schema.Types.Mixed
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
orderSchema.index({ orderNumber: 1 });
orderSchema.index({ userId: 1, createdAt: -1 });
orderSchema.index({ status: 1, createdAt: -1 });
orderSchema.index({ paymentStatus: 1 });
orderSchema.index({ shippingStatus: 1 });
orderSchema.index({ 'items.sellerId': 1, createdAt: -1 });
orderSchema.index({ 'items.productId': 1 });
orderSchema.index({ createdAt: -1 });
orderSchema.index({ total: 1 });
orderSchema.index({ trackingNumber: 1 });

// Virtual for total refunded amount
orderSchema.virtual('totalRefunded').get(function() {
  return this.refunds.reduce((sum, refund) => sum + refund.amount, 0);
});

// Virtual for remaining amount
orderSchema.virtual('remainingAmount').get(function() {
  return this.total - this.totalRefunded;
});

// Virtual for total items count
orderSchema.virtual('itemsCount').get(function() {
  return this.items.length;
});

// Virtual for total quantity
orderSchema.virtual('totalQuantity').get(function() {
  return this.items.reduce((sum, item) => sum + item.quantity, 0);
});

// Virtual for is refundable
orderSchema.virtual('isRefundable').get(function() {
  return this.paymentStatus === PaymentStatus.PAID &&
         this.status !== OrderStatus.CANCELLED &&
         this.totalRefunded < this.total;
});

// Virtual for is cancellable
orderSchema.virtual('isCancellable').get(function() {
  return this.status !== OrderStatus.DELIVERED &&
         this.status !== OrderStatus.CANCELLED &&
         this.shippingStatus !== ShippingStatus.DELIVERED;
});

// Virtual for is editable
orderSchema.virtual('isEditable').get(function() {
  return this.status === OrderStatus.PENDING ||
         this.status === OrderStatus.CONFIRMED;
});

// Pre-save middleware
orderSchema.pre('save', function(next) {
  // Generate order number if not provided
  if (!this.orderNumber) {
    this.orderNumber = this.generateOrderNumber();
  }

  // Set billing address to shipping address if not provided
  if (!this.billingAddress) {
    this.billingAddress = this.shippingAddress;
  }

  // Handle address field compatibility
  if (this.shippingAddress) {
    if (this.shippingAddress.address1 && !this.shippingAddress.street) {
      this.shippingAddress.street = this.shippingAddress.address1;
    }
  }

  if (this.billingAddress) {
    if (this.billingAddress.address1 && !this.billingAddress.street) {
      this.billingAddress.street = this.billingAddress.address1;
    }
  }

  // Calculate totals
  this.calculateTotals();

  next();
});

// Instance methods
orderSchema.methods.generateOrderNumber = function() {
  const timestamp = Date.now().toString();
  const random = Math.random().toString(36).substr(2, 4).toUpperCase();
  return `ORD-${timestamp.slice(-8)}-${random}`;
};

orderSchema.methods.calculateTotals = function() {
  this.subtotal = this.items.reduce((sum, item) => sum + item.total, 0);
  this.total = this.subtotal + this.tax + this.shipping - this.discount;
};

orderSchema.methods.addItem = function(item) {
  // Check if item already exists
  const existingItemIndex = this.items.findIndex(
    orderItem => orderItem.productId.toString() === item.productId.toString()
  );

  if (existingItemIndex >= 0) {
    // Update existing item
    this.items[existingItemIndex].quantity += item.quantity;
    this.items[existingItemIndex].total =
      this.items[existingItemIndex].quantity * this.items[existingItemIndex].price;
  } else {
    // Add new item
    this.items.push({
      ...item,
      total: item.quantity * item.price
    });
  }

  this.calculateTotals();
};

orderSchema.methods.removeItem = function(productId) {
  const itemIndex = this.items.findIndex(
    item => item.productId.toString() === productId.toString()
  );

  if (itemIndex !== -1) {
    this.items.splice(itemIndex, 1);
    this.calculateTotals();
  }
};

orderSchema.methods.updateItemQuantity = function(productId, quantity) {
  const item = this.items.find(
    item => item.productId.toString() === productId.toString()
  );

  if (item) {
    item.quantity = quantity;
    item.total = item.quantity * item.price;
    this.calculateTotals();
  }
};

orderSchema.methods.cancel = function(reason) {
  if (!this.isCancellable) {
    throw new Error('Order cannot be cancelled');
  }

  this.status = OrderStatus.CANCELLED;
  this.cancelledAt = new Date();
  this.cancellationReason = reason;
};

orderSchema.methods.addRefund = function(amount, reason, refundId = null, processedBy = null) {
  if (amount <= 0) {
    throw new Error('Refund amount must be greater than 0');
  }

  if (this.totalRefunded + amount > this.total) {
    throw new Error('Refund amount exceeds order total');
  }

  this.refunds.push({
    amount,
    reason,
    refundId,
    processedBy
  });
};

orderSchema.methods.setTrackingInfo = function(trackingNumber, carrier = null) {
  this.trackingNumber = trackingNumber;
  this.carrier = carrier;

  if (this.shippingStatus === ShippingStatus.PENDING) {
    this.shippingStatus = ShippingStatus.SHIPPED;
    this.shippedAt = new Date();
  }
};

orderSchema.methods.markAsDelivered = function() {
  this.status = OrderStatus.DELIVERED;
  this.shippingStatus = ShippingStatus.DELIVERED;
  this.deliveredAt = new Date();
};

// Static methods
orderSchema.statics.findByUser = function(userId, options = {}) {
  return this.find({ userId }, null, options);
};

orderSchema.statics.findBySeller = function(sellerId, options = {}) {
  return this.find({ 'items.sellerId': sellerId }, null, options);
};

orderSchema.statics.findByStatus = function(status, options = {}) {
  return this.find({ status }, null, options);
};

orderSchema.statics.findByPaymentStatus = function(paymentStatus, options = {}) {
  return this.find({ paymentStatus }, null, options);
};

orderSchema.statics.findByShippingStatus = function(shippingStatus, options = {}) {
  return this.find({ shippingStatus }, null, options);
};

orderSchema.statics.findPending = function(options = {}) {
  return this.find({ status: OrderStatus.PENDING }, null, options);
};

orderSchema.statics.findCompleted = function(options = {}) {
  return this.find({ status: OrderStatus.DELIVERED }, null, options);
};

orderSchema.statics.findCancelled = function(options = {}) {
  return this.find({ status: OrderStatus.CANCELLED }, null, options);
};

orderSchema.statics.findRequiringAction = function(options = {}) {
  return this.find({
    $or: [
      { status: OrderStatus.PENDING, paymentStatus: PaymentStatus.PAID },
      { status: OrderStatus.CONFIRMED, shippingStatus: ShippingStatus.PENDING },
      { paymentStatus: PaymentStatus.FAILED }
    ]
  }, null, options);
};

orderSchema.statics.getRevenueStatistics = async function(startDate, endDate) {
  return await this.aggregate([
    {
      $match: {
        createdAt: { $gte: startDate, $lte: endDate },
        status: { $ne: OrderStatus.CANCELLED }
      }
    },
    {
      $group: {
        _id: null,
        totalRevenue: { $sum: '$total' },
        totalOrders: { $sum: 1 },
        averageOrderValue: { $avg: '$total' }
      }
    }
  ]);
};

orderSchema.statics.getTopSellingProducts = async function(limit = 10, startDate = null, endDate = null) {
  const matchStage = {
    status: { $ne: OrderStatus.CANCELLED }
  };

  if (startDate && endDate) {
    matchStage.createdAt = { $gte: startDate, $lte: endDate };
  }

  return await this.aggregate([
    { $match: matchStage },
    { $unwind: '$items' },
    {
      $group: {
        _id: '$items.productId',
        totalQuantity: { $sum: '$items.quantity' },
        totalRevenue: { $sum: '$items.total' },
        productName: { $first: '$items.name' }
      }
    },
    { $sort: { totalQuantity: -1 } },
    { $limit: limit }
  ]);
};

module.exports = mongoose.model('Order', orderSchema);
