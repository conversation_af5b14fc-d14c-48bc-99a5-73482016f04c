/**
 * Test Setup Configuration
 * Global test setup and teardown
 */

const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');

let mongoServer;

// Global test setup
beforeAll(async () => {
  // Start in-memory MongoDB instance
  mongoServer = await MongoMemoryServer.create();
  const mongoUri = mongoServer.getUri();

  // Connect to the in-memory database
  await mongoose.connect(mongoUri, {
    useNewUrlParser: true,
    useUnifiedTopology: true,
  });
});

// Global test teardown
afterAll(async () => {
  // Close database connection
  await mongoose.connection.dropDatabase();
  await mongoose.connection.close();
  
  // Stop the in-memory MongoDB instance
  await mongoServer.stop();
});

// Clean up after each test
afterEach(async () => {
  // Clear all collections
  const collections = mongoose.connection.collections;
  for (const key in collections) {
    const collection = collections[key];
    await collection.deleteMany({});
  }
});

// Global test utilities
global.testUtils = {
  // Create test user data
  createTestUser: (overrides = {}) => ({
    name: 'Test User',
    email: '<EMAIL>',
    password: 'password123',
    role: 'user',
    isActive: true,
    ...overrides
  }),

  // Create test admin data
  createTestAdmin: (overrides = {}) => ({
    name: 'Test Admin',
    email: '<EMAIL>',
    password: 'admin123',
    role: 'admin',
    isActive: true,
    permissions: {
      canManageUsers: true,
      canManageProducts: true,
      canManageOrders: true,
      canManageAuctions: true,
      canManageLotteries: true,
      canViewAnalytics: true
    },
    ...overrides
  }),

  // Create test seller data
  createTestSeller: (overrides = {}) => ({
    name: 'Test Seller',
    email: '<EMAIL>',
    password: 'seller123',
    role: 'seller',
    isActive: true,
    permissions: {
      canSell: true,
      canParticipateInAuctions: true
    },
    ...overrides
  }),

  // Create test product data
  createTestProduct: (overrides = {}) => ({
    name: 'Test Product',
    description: 'Test product description',
    price: 99.99,
    originalPrice: 129.99,
    stock: 10,
    sku: 'TEST-001',
    status: 'active',
    categoryId: null,
    sellerId: null,
    images: ['test-image.jpg'],
    specifications: {
      weight: 1.5,
      dimensions: {
        length: 10,
        width: 8,
        height: 6
      }
    },
    ...overrides
  }),

  // Create test category data
  createTestCategory: (overrides = {}) => ({
    name: 'Test Category',
    slug: 'test-category',
    description: 'Test category description',
    isActive: true,
    isFeatured: false,
    showInMenu: true,
    order: 0,
    level: 0,
    parentId: null,
    ...overrides
  }),

  // Create test order data
  createTestOrder: (overrides = {}) => ({
    userId: null,
    items: [
      {
        productId: null,
        sellerId: null,
        name: 'Test Product',
        price: 99.99,
        quantity: 1,
        total: 99.99
      }
    ],
    subtotal: 99.99,
    tax: 8.00,
    shipping: 5.99,
    discount: 0,
    total: 113.98,
    currency: 'USD',
    status: 'pending',
    paymentStatus: 'pending',
    shippingStatus: 'pending',
    shippingAddress: {
      firstName: 'John',
      lastName: 'Doe',
      street: '123 Test St',
      city: 'Test City',
      state: 'TS',
      postalCode: '12345',
      country: 'US',
      phone: '555-0123'
    },
    billingAddress: {
      firstName: 'John',
      lastName: 'Doe',
      street: '123 Test St',
      city: 'Test City',
      state: 'TS',
      postalCode: '12345',
      country: 'US',
      phone: '555-0123'
    },
    paymentMethod: {
      type: 'credit_card',
      last4: '1234',
      brand: 'visa'
    },
    ...overrides
  }),

  // Create test auction data
  createTestAuction: (overrides = {}) => ({
    title: 'Test Auction',
    description: 'Test auction description',
    productId: null,
    sellerId: null,
    startingBid: 50.00,
    currentBid: 50.00,
    buyNowPrice: 200.00,
    startTime: new Date(),
    endTime: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
    status: 'active',
    bids: [],
    ...overrides
  }),

  // Create test lottery data
  createTestLottery: (overrides = {}) => ({
    title: 'Test Lottery',
    description: 'Test lottery description',
    ticketPrice: 10.00,
    maxTickets: 100,
    startTime: new Date(),
    endTime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
    status: 'active',
    tickets: [],
    numbers: Array.from({ length: 12 }, (_, i) => 100 + i * 10), // [100, 110, 120, ...]
    ...overrides
  }),

  // Generate random string
  randomString: (length = 10) => {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },

  // Generate random email
  randomEmail: () => {
    return `test${Math.random().toString(36).substr(2, 9)}@example.com`;
  },

  // Wait for a specified time
  wait: (ms) => new Promise(resolve => setTimeout(resolve, ms)),

  // Mock request object
  mockRequest: (overrides = {}) => ({
    body: {},
    params: {},
    query: {},
    user: null,
    headers: {},
    ...overrides
  }),

  // Mock response object
  mockResponse: () => {
    const res = {};
    res.status = jest.fn().mockReturnValue(res);
    res.json = jest.fn().mockReturnValue(res);
    res.send = jest.fn().mockReturnValue(res);
    res.cookie = jest.fn().mockReturnValue(res);
    res.clearCookie = jest.fn().mockReturnValue(res);
    return res;
  },

  // Mock next function
  mockNext: () => jest.fn()
};

// Set test environment
process.env.NODE_ENV = 'test';
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.JWT_EXPIRES_IN = '1h';

// Suppress console logs during tests (optional)
if (process.env.SUPPRESS_LOGS === 'true') {
  global.console = {
    ...console,
    log: jest.fn(),
    debug: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  };
}
