/**
 * Lottery Repository Implementation
 * Implements ILotteryRepository interface
 */

const ILotteryRepository = require('../../../core/interfaces/ILotteryRepository');
const LotteryModel = require('../models/Lottery');
const Lottery = require('../../../core/entities/Lottery');
const { PAGINATION } = require('../../../shared/constants');
const { DatabaseException } = require('../../../core/exceptions');

class LotteryRepository extends ILotteryRepository {
  constructor() {
    super();
    this.model = LotteryModel;
  }

  /**
   * Create a new lottery
   */
  async create(lotteryData) {
    try {
      const lottery = new this.model(lotteryData);
      const savedLottery = await lottery.save();
      return Lottery.fromPersistence(savedLottery.toObject());
    } catch (error) {
      throw new DatabaseException(`Failed to create lottery: ${error.message}`);
    }
  }

  /**
   * Find lottery by ID
   */
  async findById(id) {
    try {
      const lottery = await this.model.findById(id)
        .populate('winnerId', 'name username')
        .lean();

      return lottery ? Lottery.fromPersistence(lottery) : null;
    } catch (error) {
      throw new DatabaseException(`Failed to find lottery: ${error.message}`);
    }
  }

  /**
   * Find all lotteries
   */
  async findAll(options = {}) {
    try {
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        status,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = options;

      const query = {};
      if (status) query.status = status;

      const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };
      const skip = (page - 1) * limit;

      const [lotteries, total] = await Promise.all([
        this.model.find(query)
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .lean(),
        this.model.countDocuments(query)
      ]);

      return {
        lotteries: lotteries.map(lottery => Lottery.fromPersistence(lottery)),
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw new DatabaseException(`Failed to find lotteries: ${error.message}`);
    }
  }

  /**
   * Find active lotteries
   */
  async findActive(options = {}) {
    try {
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        sortBy = 'endTime',
        sortOrder = 'asc'
      } = options;

      const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };
      const skip = (page - 1) * limit;

      const [lotteries, total] = await Promise.all([
        this.model.findActive()
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .lean(),
        this.model.findActive().countDocuments()
      ]);

      return {
        lotteries: lotteries.map(lottery => Lottery.fromPersistence(lottery)),
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw new DatabaseException(`Failed to find active lotteries: ${error.message}`);
    }
  }

  /**
   * Find lotteries by status
   */
  async findByStatus(status, options = {}) {
    try {
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = options;

      const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };
      const skip = (page - 1) * limit;

      const [lotteries, total] = await Promise.all([
        this.model.find({ status })
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .lean(),
        this.model.countDocuments({ status })
      ]);

      return {
        lotteries: lotteries.map(lottery => Lottery.fromPersistence(lottery)),
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw new DatabaseException(`Failed to find lotteries by status: ${error.message}`);
    }
  }

  /**
   * Update lottery
   */
  async update(id, updateData) {
    try {
      const lottery = await this.model.findByIdAndUpdate(
        id,
        { ...updateData, updatedAt: new Date() },
        { new: true, runValidators: true }
      ).lean();

      return lottery ? Lottery.fromPersistence(lottery) : null;
    } catch (error) {
      throw new DatabaseException(`Failed to update lottery: ${error.message}`);
    }
  }

  /**
   * Delete lottery
   */
  async delete(id) {
    try {
      const result = await this.model.findByIdAndDelete(id);
      return !!result;
    } catch (error) {
      throw new DatabaseException(`Failed to delete lottery: ${error.message}`);
    }
  }

  /**
   * Purchase ticket
   */
  async purchaseTicket(lotteryId, ticketData) {
    try {
      const lottery = await this.model.findByIdAndUpdate(
        lotteryId,
        {
          $push: { tickets: ticketData },
          $inc: { totalPrize: ticketData.price || 0 },
          $set: { updatedAt: new Date() }
        },
        { new: true, runValidators: true }
      ).lean();

      return lottery ? Lottery.fromPersistence(lottery) : null;
    } catch (error) {
      throw new DatabaseException(`Failed to purchase ticket: ${error.message}`);
    }
  }

  /**
   * Get user's tickets
   */
  async getUserTickets(userId, options = {}) {
    try {
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        status
      } = options;

      const matchStage = { 'tickets.userId': userId };
      if (status) matchStage.status = status;

      const skip = (page - 1) * limit;

      const lotteries = await this.model.aggregate([
        { $match: matchStage },
        { $unwind: '$tickets' },
        { $match: { 'tickets.userId': userId } },
        {
          $project: {
            lotteryId: '$_id',
            title: 1,
            status: 1,
            endTime: 1,
            ticketPrice: 1,
            winningNumber: 1,
            winnerUsername: 1,
            ticket: '$tickets'
          }
        },
        { $sort: { 'ticket.purchaseTime': -1 } },
        { $skip: skip },
        { $limit: limit }
      ]);

      const total = await this.model.aggregate([
        { $match: matchStage },
        { $unwind: '$tickets' },
        { $match: { 'tickets.userId': userId } },
        { $count: 'total' }
      ]);

      return {
        tickets: lotteries,
        pagination: {
          page,
          limit,
          total: total[0]?.total || 0,
          pages: Math.ceil((total[0]?.total || 0) / limit)
        }
      };
    } catch (error) {
      throw new DatabaseException(`Failed to get user tickets: ${error.message}`);
    }
  }

  /**
   * Get lottery tickets
   */
  async getTickets(lotteryId, options = {}) {
    try {
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        sortBy = 'purchaseTime',
        sortOrder = 'desc'
      } = options;

      const lottery = await this.model.findById(lotteryId)
        .select('tickets')
        .lean();

      if (!lottery) return [];

      const sort = sortOrder === 'desc' ? -1 : 1;
      const sortedTickets = lottery.tickets.sort((a, b) => {
        if (sortBy === 'purchaseTime') {
          return sort * (new Date(b.purchaseTime) - new Date(a.purchaseTime));
        }
        return 0;
      });

      const skip = (page - 1) * limit;
      const paginatedTickets = sortedTickets.slice(skip, skip + limit);

      return {
        tickets: paginatedTickets,
        pagination: {
          page,
          limit,
          total: lottery.tickets.length,
          pages: Math.ceil(lottery.tickets.length / limit)
        }
      };
    } catch (error) {
      throw new DatabaseException(`Failed to get lottery tickets: ${error.message}`);
    }
  }

  /**
   * Update lottery status
   */
  async updateStatus(id, status) {
    try {
      const lottery = await this.model.findByIdAndUpdate(
        id,
        { status, updatedAt: new Date() },
        { new: true, runValidators: true }
      ).lean();

      return lottery ? Lottery.fromPersistence(lottery) : null;
    } catch (error) {
      throw new DatabaseException(`Failed to update lottery status: ${error.message}`);
    }
  }

  /**
   * Set lottery winner
   */
  async setWinner(id, winnerId, winningNumber, winnerUsername) {
    try {
      const lottery = await this.model.findByIdAndUpdate(
        id,
        {
          winnerId,
          winningNumber,
          winnerUsername,
          drawTime: new Date(),
          updatedAt: new Date()
        },
        { new: true, runValidators: true }
      ).lean();

      return lottery ? Lottery.fromPersistence(lottery) : null;
    } catch (error) {
      throw new DatabaseException(`Failed to set lottery winner: ${error.message}`);
    }
  }

  /**
   * Get ending soon lotteries
   */
  async findEndingSoon(hours = 24, options = {}) {
    try {
      const lotteries = await this.model.findEndingSoon(hours)
        .sort({ endTime: 1 })
        .lean();

      return lotteries.map(lottery => Lottery.fromPersistence(lottery));
    } catch (error) {
      throw new DatabaseException(`Failed to find ending soon lotteries: ${error.message}`);
    }
  }

  /**
   * Get expired lotteries
   */
  async findExpired(options = {}) {
    try {
      const lotteries = await this.model.findExpired()
        .lean();

      return lotteries.map(lottery => Lottery.fromPersistence(lottery));
    } catch (error) {
      throw new DatabaseException(`Failed to find expired lotteries: ${error.message}`);
    }
  }

  /**
   * Get lottery statistics
   */
  async getStatistics(id) {
    try {
      const lottery = await this.model.findById(id).lean();
      if (!lottery) return null;

      return {
        ticketsSold: lottery.tickets.length,
        ticketsRemaining: lottery.maxTickets - lottery.tickets.length,
        totalPrize: lottery.totalPrize,
        uniqueParticipants: new Set(lottery.tickets.map(ticket => ticket.userId.toString())).size,
        averageTicketsPerUser: lottery.tickets.length > 0 
          ? lottery.tickets.length / new Set(lottery.tickets.map(ticket => ticket.userId.toString())).size 
          : 0
      };
    } catch (error) {
      throw new DatabaseException(`Failed to get lottery statistics: ${error.message}`);
    }
  }

  /**
   * Get lotteries count by status
   */
  async getCountByStatus() {
    try {
      const counts = await this.model.aggregate([
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 }
          }
        }
      ]);

      const result = {};
      counts.forEach(item => {
        result[item._id] = item.count;
      });

      return result;
    } catch (error) {
      throw new DatabaseException(`Failed to get lottery counts: ${error.message}`);
    }
  }

  /**
   * Search lotteries
   */
  async search(query, options = {}) {
    try {
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        status,
        sortBy = 'relevance',
        sortOrder = 'desc'
      } = options;

      const searchQuery = {
        $text: { $search: query }
      };

      if (status) searchQuery.status = status;

      let sort = {};
      if (sortBy === 'relevance') {
        sort = { score: { $meta: 'textScore' } };
      } else {
        sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };
      }

      const skip = (page - 1) * limit;

      const [lotteries, total] = await Promise.all([
        this.model.find(searchQuery, { score: { $meta: 'textScore' } })
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .lean(),
        this.model.countDocuments(searchQuery)
      ]);

      return {
        lotteries: lotteries.map(lottery => Lottery.fromPersistence(lottery)),
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw new DatabaseException(`Failed to search lotteries: ${error.message}`);
    }
  }
}

module.exports = LotteryRepository;
