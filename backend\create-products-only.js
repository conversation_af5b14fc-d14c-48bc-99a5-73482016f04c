const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:5000/api';
let adminToken = '';

async function createProducts() {
  console.log('🚀 Creating test products...\n');

  try {
    // 1. Login to get admin token
    console.log('1️⃣ Logging in as admin...');
    const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'eEf8kbpE9G8XhiL'
      })
    });

    const loginData = await loginResponse.json();
    adminToken = loginData.data.token;
    const adminUserId = loginData.data.user.id;
    console.log('✅ Admin login successful\n');

    // 2. Get existing categories
    console.log('2️⃣ Getting existing categories...');
    const categoriesResponse = await fetch(`${BASE_URL}/categories`);
    const categoriesData = await categoriesResponse.json();
    const categories = categoriesData.data.categories;
    
    console.log(`📂 Found ${categories.length} categories:`);
    categories.forEach(cat => {
      console.log(`  - ${cat.name} (${cat.slug}) - ID: ${cat.id}`);
    });
    console.log('');

    // 3. Create Products with real category IDs
    console.log('3️⃣ Creating test products...');
    
    const products = [
      {
        name: 'iPhone 15 Pro',
        description: 'Apple iPhone 15 Pro 128GB - Titanium Blue. En son teknoloji ile donatılmış premium akıllı telefon.',
        shortDescription: 'Apple iPhone 15 Pro 128GB',
        price: 45000,
        originalPrice: 50000,
        discountPercentage: 10,
        categoryId: categories.find(c => c.slug === 'telefon-tablet')?.id || categories.find(c => c.slug === 'elektronik')?.id,
        sellerId: adminUserId,
        images: [
          {
            url: 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=500',
            alt: 'iPhone 15 Pro',
            isPrimary: true
          }
        ],
        stock: 25,
        lowStockThreshold: 5,
        specifications: {
          'Ekran Boyutu': '6.1 inç',
          'İşlemci': 'A17 Pro',
          'RAM': '8GB',
          'Depolama': '128GB',
          'Kamera': '48MP Ana + 12MP Ultra Wide + 12MP Telephoto'
        },
        tags: ['apple', 'iphone', 'smartphone', 'premium'],
        featured: true,
        status: 'active'
      },
      {
        name: 'Samsung Galaxy S24',
        description: 'Samsung Galaxy S24 256GB - Phantom Black. AI destekli kamera ve performans.',
        shortDescription: 'Samsung Galaxy S24 256GB',
        price: 35000,
        originalPrice: 38000,
        discountPercentage: 8,
        categoryId: categories.find(c => c.slug === 'telefon-tablet')?.id || categories.find(c => c.slug === 'elektronik')?.id,
        sellerId: adminUserId,
        images: [
          {
            url: 'https://images.unsplash.com/photo-1610945265064-0e34e5519bbf?w=500',
            alt: 'Samsung Galaxy S24',
            isPrimary: true
          }
        ],
        stock: 30,
        lowStockThreshold: 5,
        specifications: {
          'Ekran Boyutu': '6.2 inç',
          'İşlemci': 'Snapdragon 8 Gen 3',
          'RAM': '8GB',
          'Depolama': '256GB',
          'Kamera': '50MP Ana + 12MP Ultra Wide + 10MP Telephoto'
        },
        tags: ['samsung', 'galaxy', 'android', 'ai'],
        featured: true,
        status: 'active'
      },
      {
        name: 'MacBook Air M3',
        description: 'Apple MacBook Air 13" M3 Chip 8GB RAM 256GB SSD - Midnight. Ultra ince ve hafif tasarım.',
        shortDescription: 'MacBook Air M3 13" 256GB',
        price: 42000,
        originalPrice: 45000,
        discountPercentage: 7,
        categoryId: categories.find(c => c.slug === 'bilgisayar')?.id || categories.find(c => c.slug === 'elektronik')?.id,
        sellerId: adminUserId,
        images: [
          {
            url: 'https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=500',
            alt: 'MacBook Air M3',
            isPrimary: true
          }
        ],
        stock: 15,
        lowStockThreshold: 3,
        specifications: {
          'İşlemci': 'Apple M3',
          'RAM': '8GB',
          'Depolama': '256GB SSD',
          'Ekran': '13.6" Liquid Retina',
          'Batarya': '18 saate kadar'
        },
        tags: ['apple', 'macbook', 'laptop', 'm3'],
        featured: true,
        status: 'active'
      },
      {
        name: 'Nike Air Max 270',
        description: 'Nike Air Max 270 Erkek Spor Ayakkabı - Siyah/Beyaz. Maksimum konfor ve stil.',
        shortDescription: 'Nike Air Max 270 Erkek',
        price: 3500,
        originalPrice: 4000,
        discountPercentage: 12,
        categoryId: categories.find(c => c.slug === 'spor-outdoor')?.id || categories.find(c => c.slug === 'giyim')?.id,
        sellerId: adminUserId,
        images: [
          {
            url: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?w=500',
            alt: 'Nike Air Max 270',
            isPrimary: true
          }
        ],
        stock: 50,
        lowStockThreshold: 10,
        specifications: {
          'Marka': 'Nike',
          'Model': 'Air Max 270',
          'Cinsiyet': 'Erkek',
          'Renk': 'Siyah/Beyaz',
          'Malzeme': 'Mesh/Sentetik'
        },
        tags: ['nike', 'airmax', 'spor', 'ayakkabı'],
        featured: false,
        status: 'active'
      },
      {
        name: 'Kahve Makinesi Delonghi',
        description: 'Delonghi Magnifica S Tam Otomatik Kahve Makinesi. Barista kalitesinde kahve.',
        shortDescription: 'Delonghi Magnifica S',
        price: 8500,
        originalPrice: 9500,
        discountPercentage: 11,
        categoryId: categories.find(c => c.slug === 'ev-yasam')?.id || categories[0]?.id,
        sellerId: adminUserId,
        images: [
          {
            url: 'https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?w=500',
            alt: 'Kahve Makinesi',
            isPrimary: true
          }
        ],
        stock: 20,
        lowStockThreshold: 5,
        specifications: {
          'Marka': 'Delonghi',
          'Tip': 'Tam Otomatik',
          'Kapasite': '1.8L Su Tankı',
          'Özellik': 'Çift Boiler',
          'Garanti': '2 Yıl'
        },
        tags: ['delonghi', 'kahve', 'otomatik', 'ev'],
        featured: false,
        status: 'active'
      }
    ];

    const createdProducts = [];
    
    for (const product of products) {
      console.log(`Creating product: ${product.name} (Category ID: ${product.categoryId})`);
      
      const response = await fetch(`${BASE_URL}/products`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${adminToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(product)
      });
      
      const result = await response.json();
      if (response.ok) {
        createdProducts.push(result.data.product);
        console.log(`✅ Created product: ${product.name}`);
      } else {
        console.log(`❌ Failed to create product ${product.name}:`, result.message);
        console.log('Response:', JSON.stringify(result, null, 2));
      }
    }

    console.log(`\n📦 Created ${createdProducts.length} products\n`);

    // 4. Summary
    console.log('📊 SUMMARY:');
    console.log(`✅ Products created: ${createdProducts.length}`);
    console.log('\n🎉 Product creation completed successfully!');

  } catch (error) {
    console.error('❌ Error creating products:', error.message);
  }
}

createProducts();
