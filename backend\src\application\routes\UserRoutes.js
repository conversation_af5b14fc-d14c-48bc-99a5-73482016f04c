/**
 * User Routes Configuration
 * Defines all user-related API endpoints
 */

const express = require('express');
const ErrorMiddleware = require('../middleware/ErrorMiddleware');

class UserRoutes {
  constructor(container) {
    this.container = container;
    this.router = express.Router();
    this.setupRoutes();
  }

  setupRoutes() {
    const userController = this.container.resolve('userController');
    const authMiddleware = this.container.resolve('authMiddleware');

    // Admin routes (must be before public routes to avoid conflicts)
    this.router.post('/',
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(userController.createUser.bind(userController))
    );

    // Public routes (no authentication required)
    this.router.post('/register',
      ErrorMiddleware.asyncHandler(userController.register.bind(userController))
    );

    this.router.post('/login',
      ErrorMiddleware.asyncHandler(userController.login.bind(userController))
    );

    this.router.post('/logout',
      ErrorMiddleware.asyncHandler(userController.logout.bind(userController))
    );

    this.router.post('/forgot-password',
      ErrorMiddleware.asyncHandler(userController.requestPasswordReset.bind(userController))
    );

    this.router.post('/reset-password',
      ErrorMiddleware.asyncHandler(userController.resetPassword.bind(userController))
    );

    // Protected routes (authentication required)
    this.router.get('/profile',
      authMiddleware.authenticate(),
      ErrorMiddleware.asyncHandler(userController.getProfile.bind(userController))
    );

    this.router.put('/profile',
      authMiddleware.authenticate(),
      ErrorMiddleware.asyncHandler(userController.updateProfile.bind(userController))
    );

    this.router.post('/change-password',
      authMiddleware.authenticate(),
      ErrorMiddleware.asyncHandler(userController.changePassword.bind(userController))
    );

    this.router.post('/verify-email/:token',
      authMiddleware.authenticate(),
      ErrorMiddleware.asyncHandler(userController.verifyEmail.bind(userController))
    );

    // Public user info (for profiles, etc.)
    this.router.get('/:id',
      ErrorMiddleware.asyncHandler(userController.getUserById.bind(userController))
    );

    // Admin routes
    this.router.get('/',
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(userController.getUsers.bind(userController))
    );

    this.router.post('/:id/approve',
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(userController.approveUser.bind(userController))
    );

    this.router.post('/:id/reject',
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(userController.rejectUser.bind(userController))
    );

    this.router.put('/:id/permissions',
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(userController.updateUserPermissions.bind(userController))
    );


    this.router.put('/:id',
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(userController.updateUser.bind(userController))
    );

    this.router.delete('/:id',
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(userController.deleteUser.bind(userController))
    );
  }

  getRouter() {
    return this.router;
  }
}

module.exports = UserRoutes;
