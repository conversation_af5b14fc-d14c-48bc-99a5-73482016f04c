/**
 * Order Controller
 * Application layer - HTTP request handling for order operations
 */

const { HttpStatus } = require('../../shared/enums');
const { PAGINATION } = require('../../shared/constants');

class OrderController {
  constructor(orderService) {
    this.orderService = orderService;
  }

  /**
   * Get all orders (Admin)
   */
  async getAllOrders(req, res, next) {
    try {
      const {
        status,
        paymentStatus,
        shippingStatus,
        startDate,
        endDate,
        sortBy = 'createdAt',
        sortOrder = 'desc',
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        search
      } = req.query;

      const options = {
        page: parseInt(page),
        limit: Math.min(parseInt(limit), PAGINATION.MAX_LIMIT),
        sortBy,
        sortOrder,
        status,
        paymentStatus,
        shippingStatus,
        startDate,
        endDate
      };

      let result;
      if (search) {
        result = await this.orderService.searchOrders(search, options);
      } else {
        result = await this.orderService.getAllOrders(options);
      }

      res.status(HttpStatus.OK).json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get order by ID
   */
  async getOrderById(req, res, next) {
    try {
      const { id } = req.params;
      const order = await this.orderService.getOrderById(id);

      res.status(HttpStatus.OK).json({
        success: true,
        data: { order }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get order by order number
   */
  async getOrderByOrderNumber(req, res, next) {
    try {
      const { orderNumber } = req.params;
      const order = await this.orderService.getOrderByOrderNumber(orderNumber);

      res.status(HttpStatus.OK).json({
        success: true,
        data: { order }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Create new order
   */
  async createOrder(req, res, next) {
    try {
      console.log('🔍 OrderController.createOrder - req.body:', JSON.stringify(req.body, null, 2));
      console.log('🔍 OrderController.createOrder - req.user.id:', req.user.id);

      const order = await this.orderService.createOrder(req.body, req.user.id);

      res.status(HttpStatus.CREATED).json({
        success: true,
        message: 'Order created successfully',
        data: { order }
      });
    } catch (error) {
      console.log('❌ OrderController.createOrder - Error:', error.message);
      next(error);
    }
  }

  /**
   * Update order
   */
  async updateOrder(req, res, next) {
    try {
      const { id } = req.params;
      const order = await this.orderService.updateOrder(id, req.body, req.user.id);

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'Order updated successfully',
        data: { order }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Cancel order
   */
  async cancelOrder(req, res, next) {
    try {
      const { id } = req.params;
      const { reason } = req.body;

      const order = await this.orderService.cancelOrder(id, reason, req.user.id);

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'Order cancelled successfully',
        data: { order }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get user's orders
   */
  async getUserOrders(req, res, next) {
    try {
      const {
        status,
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = req.query;

      const options = {
        page: parseInt(page),
        limit: Math.min(parseInt(limit), PAGINATION.MAX_LIMIT),
        sortBy,
        sortOrder,
        status
      };

      const result = await this.orderService.getUserOrders(req.user.id, options);

      res.status(HttpStatus.OK).json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get seller's orders
   */
  async getSellerOrders(req, res, next) {
    try {
      const {
        status,
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = req.query;

      const options = {
        page: parseInt(page),
        limit: Math.min(parseInt(limit), PAGINATION.MAX_LIMIT),
        sortBy,
        sortOrder,
        status
      };

      const result = await this.orderService.getSellerOrders(req.user.id, options);

      res.status(HttpStatus.OK).json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get orders by status
   */
  async getOrdersByStatus(req, res, next) {
    try {
      const { status } = req.params;
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = req.query;

      const options = {
        page: parseInt(page),
        limit: Math.min(parseInt(limit), PAGINATION.MAX_LIMIT),
        sortBy,
        sortOrder
      };

      const result = await this.orderService.getOrdersByStatus(status, options);

      res.status(HttpStatus.OK).json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update order status (Admin/Seller)
   */
  async updateOrderStatus(req, res, next) {
    try {
      const { id } = req.params;
      const { status } = req.body;

      if (!status) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          message: 'Status is required'
        });
      }

      const order = await this.orderService.updateOrderStatus(id, status, req.user.id);

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'Order status updated successfully',
        data: { order }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update payment status (Admin)
   */
  async updatePaymentStatus(req, res, next) {
    try {
      const { id } = req.params;
      const { paymentStatus } = req.body;

      if (!paymentStatus) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          message: 'Payment status is required'
        });
      }

      const order = await this.orderService.updatePaymentStatus(id, paymentStatus, req.user.id);

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'Payment status updated successfully',
        data: { order }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Add tracking information
   */
  async addTrackingInfo(req, res, next) {
    try {
      const { id } = req.params;
      const { trackingNumber, carrier, estimatedDelivery } = req.body;

      if (!trackingNumber) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          message: 'Tracking number is required'
        });
      }

      const trackingData = {
        trackingNumber,
        carrier,
        estimatedDelivery: estimatedDelivery ? new Date(estimatedDelivery) : null
      };

      const order = await this.orderService.addTrackingInfo(id, trackingData, req.user.id);

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'Tracking information added successfully',
        data: { order }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get order statistics
   */
  async getOrderStatistics(req, res, next) {
    try {
      const { id } = req.params;
      const statistics = await this.orderService.getOrderStatistics(id);

      res.status(HttpStatus.OK).json({
        success: true,
        data: { statistics }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get order counts by status
   */
  async getOrderCounts(req, res, next) {
    try {
      const { userId } = req.query;
      const counts = await this.orderService.getOrderCounts(userId);

      res.status(HttpStatus.OK).json({
        success: true,
        data: { counts }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get revenue statistics (Admin)
   */
  async getRevenueStatistics(req, res, next) {
    try {
      const { startDate, endDate, sellerId } = req.query;

      const options = {
        startDate: startDate ? new Date(startDate) : null,
        endDate: endDate ? new Date(endDate) : null,
        sellerId
      };

      const statistics = await this.orderService.getRevenueStatistics(options);

      res.status(HttpStatus.OK).json({
        success: true,
        data: { statistics }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Search orders
   */
  async searchOrders(req, res, next) {
    try {
      const { q: query } = req.query;

      if (!query) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          message: 'Search query is required'
        });
      }

      const {
        sortBy = 'createdAt',
        sortOrder = 'desc',
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT
      } = req.query;

      const options = {
        page: parseInt(page),
        limit: Math.min(parseInt(limit), PAGINATION.MAX_LIMIT),
        sortBy,
        sortOrder
      };

      const result = await this.orderService.searchOrders(query, options);

      res.status(HttpStatus.OK).json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = OrderController;
