
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/business/services</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../../prettify.css" />
    <link rel="stylesheet" href="../../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../../index.html">All files</a> src/business/services</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">20.03% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>208/1038</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">17.97% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>119/662</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">26.2% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>38/145</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">19.94% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>204/1023</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="AuctionService.js"><a href="AuctionService.js.html">AuctionService.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="162" class="abs low">0/162</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="102" class="abs low">0/102</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="20" class="abs low">0/20</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="161" class="abs low">0/161</td>
	</tr>

<tr>
	<td class="file low" data-value="CategoryService.js"><a href="CategoryService.js.html">CategoryService.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="180" class="abs low">0/180</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="117" class="abs low">0/117</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="24" class="abs low">0/24</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="177" class="abs low">0/177</td>
	</tr>

<tr>
	<td class="file low" data-value="LotteryService.js"><a href="LotteryService.js.html">LotteryService.js</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="193" class="abs low">0/193</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="121" class="abs low">0/121</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="24" class="abs low">0/24</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="190" class="abs low">0/190</td>
	</tr>

<tr>
	<td class="file medium" data-value="OrderService.js"><a href="OrderService.js.html">OrderService.js</a></td>
	<td data-value="50.98" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 50%"></div><div class="cover-empty" style="width: 50%"></div></div>
	</td>
	<td data-value="50.98" class="pct medium">50.98%</td>
	<td data-value="204" class="abs medium">104/204</td>
	<td data-value="46.21" class="pct low">46.21%</td>
	<td data-value="132" class="abs low">61/132</td>
	<td data-value="57.57" class="pct medium">57.57%</td>
	<td data-value="33" class="abs medium">19/33</td>
	<td data-value="51.51" class="pct medium">51.51%</td>
	<td data-value="198" class="abs medium">102/198</td>
	</tr>

<tr>
	<td class="file low" data-value="ProductService.js"><a href="ProductService.js.html">ProductService.js</a></td>
	<td data-value="41.57" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 41%"></div><div class="cover-empty" style="width: 59%"></div></div>
	</td>
	<td data-value="41.57" class="pct low">41.57%</td>
	<td data-value="190" class="abs low">79/190</td>
	<td data-value="36.02" class="pct low">36.02%</td>
	<td data-value="136" class="abs low">49/136</td>
	<td data-value="44.44" class="pct low">44.44%</td>
	<td data-value="27" class="abs low">12/27</td>
	<td data-value="40.95" class="pct low">40.95%</td>
	<td data-value="188" class="abs low">77/188</td>
	</tr>

<tr>
	<td class="file low" data-value="UserService.js"><a href="UserService.js.html">UserService.js</a></td>
	<td data-value="22.93" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 22%"></div><div class="cover-empty" style="width: 78%"></div></div>
	</td>
	<td data-value="22.93" class="pct low">22.93%</td>
	<td data-value="109" class="abs low">25/109</td>
	<td data-value="16.66" class="pct low">16.66%</td>
	<td data-value="54" class="abs low">9/54</td>
	<td data-value="41.17" class="pct low">41.17%</td>
	<td data-value="17" class="abs low">7/17</td>
	<td data-value="22.93" class="pct low">22.93%</td>
	<td data-value="109" class="abs low">25/109</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-05-26T22:44:17.010Z
            </div>
        <script src="../../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../../sorter.js"></script>
        <script src="../../../block-navigation.js"></script>
    </body>
</html>
    