const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:5000/api';
let adminToken = '';
let adminUserId = '';
let testUserId = '';
let testProductId = '';

async function testOrderCRUD() {
  console.log('🚀 Testing Order CRUD Operations...\n');

  try {
    // 1. Login to get admin token
    console.log('1️⃣ Logging in as admin...');
    const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'eEf8kbpE9G8XhiL'
      })
    });

    const loginData = await loginResponse.json();
    adminToken = loginData.data.token;
    adminUserId = loginData.data.user.id;
    console.log('✅ Admin login successful\n');

    // 2. Get test user and product for order
    console.log('2️⃣ Getting test data for order...');

    // Get users
    const usersResponse = await fetch(`${BASE_URL}/users`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });
    const usersData = await usersResponse.json();
    const testUser = usersData.data.users.find(user => user.role === 'user');
    testUserId = testUser.id;

    // Get products
    const productsResponse = await fetch(`${BASE_URL}/products`);
    const productsData = await productsResponse.json();
    testProductId = productsData.data.products[0].id;

    console.log(`👤 Using test user: ${testUser.name} (${testUser.email})`);
    console.log(`📦 Using test product: ${productsData.data.products[0].name}\n`);

    // 3. List existing orders
    console.log('3️⃣ Listing existing orders...');
    const listResponse = await fetch(`${BASE_URL}/orders/admin/all`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });

    console.log('List response status:', listResponse.status);
    const listData = await listResponse.json();
    console.log('List response data:', JSON.stringify(listData, null, 2));

    if (!listData.data || !listData.data.orders) {
      console.log('❌ Orders data structure is invalid:', listData);
      return;
    }

    const existingOrders = listData.data.orders;

    console.log(`📋 Found ${existingOrders.length} existing orders:`);
    existingOrders.forEach((order, index) => {
      console.log(`  ${index + 1}. ${order.orderNumber} - Status: ${order.status} - Total: ${order.totalAmount}₺`);
    });
    console.log('');

    // 4. Clean up any existing test order
    console.log('4️⃣ Cleaning up existing test orders...');
    const existingTestOrder = existingOrders.find(o => o.orderNumber && o.orderNumber.includes('TEST-CRUD'));
    if (existingTestOrder && existingTestOrder.id) {
      try {
        await fetch(`${BASE_URL}/orders/${existingTestOrder.id}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${adminToken}`,
            'Content-Type': 'application/json'
          }
        });
        console.log('🧹 Cleaned up existing test order');
      } catch (error) {
        console.log('🧹 No existing test order to clean up');
      }
    }

    // 5. CREATE - Add new order
    console.log('5️⃣ Testing CREATE - Adding new order...');
    const newOrder = {
      userId: testUserId,
      items: [
        {
          productId: testProductId,
          sellerId: adminUserId,
          name: 'Test CRUD Product',
          price: 1500,
          quantity: 2,
          total: 3000
        }
      ],
      totalAmount: 3050,
      shippingAddress: {
        firstName: 'Test',
        lastName: 'User',
        address1: 'Test Address 123',
        city: 'Test City',
        state: 'Test State',
        postalCode: '12345',
        country: 'Turkey',
        phone: '+90 ************'
      },
      billingAddress: {
        firstName: 'Test',
        lastName: 'User',
        address1: 'Test Address 123',
        city: 'Test City',
        state: 'Test State',
        postalCode: '12345',
        country: 'Turkey',
        phone: '+90 ************'
      },
      paymentMethod: {
        type: 'credit_card',
        provider: 'test',
        last4: '4242',
        brand: 'visa'
      },
      paymentStatus: 'pending',
      status: 'pending',
      notes: 'CRUD test için oluşturulan sipariş',
      shippingCost: 50,
      taxAmount: 0,
      discountAmount: 0
    };

    const createResponse = await fetch(`${BASE_URL}/orders`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${adminToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(newOrder)
    });

    const createData = await createResponse.json();
    if (createResponse.ok) {
      const createdOrder = createData.data.order;
      console.log(`✅ CREATE successful - Order created with ID: ${createdOrder.id}`);
      console.log(`   Order Number: ${createdOrder.orderNumber}`);
      console.log(`   Total: ${createdOrder.totalAmount}₺`);
      console.log(`   Status: ${createdOrder.status}`);
      console.log(`   Items: ${createdOrder.items.length}`);
      console.log('');

      // 6. READ - Get order by ID
      console.log('6️⃣ Testing READ - Getting order by ID...');
      const readResponse = await fetch(`${BASE_URL}/orders/${createdOrder.id}`, {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      });
      const readData = await readResponse.json();

      if (readResponse.ok) {
        const fetchedOrder = readData.data.order;
        console.log(`✅ READ successful - Order fetched:`);
        console.log(`   ID: ${fetchedOrder.id}`);
        console.log(`   Order Number: ${fetchedOrder.orderNumber}`);
        console.log(`   User ID: ${fetchedOrder.userId}`);
        console.log(`   Total: ${fetchedOrder.totalAmount}₺`);
        console.log(`   Status: ${fetchedOrder.status}`);
        console.log(`   Payment Status: ${fetchedOrder.paymentStatus}`);
        console.log('');

        // 7. UPDATE - Edit order
        console.log('7️⃣ Testing UPDATE - Editing order...');
        const updateData = {
          status: 'confirmed',
          paymentStatus: 'paid',
          notes: 'CRUD test için oluşturulan ve güncellenmiş sipariş',
          trackingNumber: 'TEST123456789'
        };

        const updateResponse = await fetch(`${BASE_URL}/orders/${createdOrder.id}`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${adminToken}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(updateData)
        });

        const updatedData = await updateResponse.json();
        if (updateResponse.ok) {
          const updatedOrder = updatedData.data.order;
          console.log(`✅ UPDATE successful - Order updated:`);
          console.log(`   Status: ${updatedOrder.status} (was ${fetchedOrder.status})`);
          console.log(`   Payment Status: ${updatedOrder.paymentStatus} (was ${fetchedOrder.paymentStatus})`);
          console.log(`   Tracking Number: ${updatedOrder.trackingNumber || 'N/A'}`);
          console.log(`   Notes: ${updatedOrder.notes}`);
          console.log('');

          // 8. Verify update by reading again
          console.log('8️⃣ Verifying update by reading again...');
          const verifyResponse = await fetch(`${BASE_URL}/orders/${createdOrder.id}`, {
            headers: { 'Authorization': `Bearer ${adminToken}` }
          });
          const verifyData = await verifyResponse.json();

          if (verifyResponse.ok) {
            const verifiedOrder = verifyData.data.order;
            console.log(`✅ VERIFY successful - Changes confirmed:`);
            console.log(`   Status: ${verifiedOrder.status}`);
            console.log(`   Payment Status: ${verifiedOrder.paymentStatus}`);
            console.log(`   Tracking Number: ${verifiedOrder.trackingNumber || 'N/A'}`);
            console.log('');
          } else {
            console.log(`❌ VERIFY failed:`, verifyData.message);
          }

          // 9. DELETE - Remove order
          console.log('9️⃣ Testing DELETE - Removing order...');
          const deleteResponse = await fetch(`${BASE_URL}/orders/${createdOrder.id}`, {
            method: 'DELETE',
            headers: {
              'Authorization': `Bearer ${adminToken}`,
              'Content-Type': 'application/json'
            }
          });

          const deleteData = await deleteResponse.json();
          if (deleteResponse.ok) {
            console.log(`✅ DELETE successful - Order removed`);
            console.log('');

            // 10. Verify deletion by trying to read
            console.log('🔟 Verifying deletion by trying to read...');
            const verifyDeleteResponse = await fetch(`${BASE_URL}/orders/${createdOrder.id}`, {
              headers: { 'Authorization': `Bearer ${adminToken}` }
            });
            const verifyDeleteData = await verifyDeleteResponse.json();

            if (verifyDeleteResponse.status === 404 ||
                (verifyDeleteData && verifyDeleteData.message === 'Order not found')) {
              console.log(`✅ DELETE VERIFY successful - Order not found (as expected)`);
            } else {
              console.log(`❌ DELETE VERIFY failed - Order still exists:`, verifyDeleteData);
            }
          } else {
            console.log(`❌ DELETE failed:`, deleteData.message);
          }

        } else {
          console.log(`❌ UPDATE failed:`, updatedData.message);
        }

      } else {
        console.log(`❌ READ failed:`, readData.message);
      }

    } else {
      console.log(`❌ CREATE failed:`, createData.message);
      console.log('Response:', JSON.stringify(createData, null, 2));
    }

    console.log('\n🎉 Order CRUD testing completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

testOrderCRUD();
