{"name": "robin<PERSON>-backend", "version": "1.0.0", "description": "Backend API for Robinhoot application", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "start:legacy": "node src/index.js", "dev:legacy": "nodemon src/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false"}, "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.8.0", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.2.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "morgan": "^1.10.0", "node-fetch": "^2.7.0", "winston": "^3.11.0"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/preset-env": "^7.27.2", "babel-jest": "^29.7.0", "jest": "^29.7.0", "jest-junit": "^16.0.0", "jest-watch-typeahead": "^2.2.2", "migrate-mongo": "^12.1.3", "mongodb-memory-server": "^9.5.0", "nodemon": "^3.0.2", "supertest": "^6.3.4"}, "engines": {"node": ">=18.0.0"}}