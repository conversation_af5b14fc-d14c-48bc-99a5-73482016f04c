/**
 * Giveaway Routes Configuration
 * Defines all giveaway related API endpoints
 */

const express = require('express');
const ErrorMiddleware = require('../middleware/ErrorMiddleware');

class GiveawayRoutes {
  constructor(container) {
    this.container = container;
    this.router = express.Router();
    this.setupRoutes();
  }

  setupRoutes() {
    const giveawayController = new (require('../controllers/GiveawayController'))(this.container);
    const authMiddleware = this.container.resolve('authMiddleware');

    // Public routes
    this.router.get('/', 
      ErrorMiddleware.asyncHandler(giveawayController.getActiveGiveaways.bind(giveawayController))
    );

    this.router.get('/active', 
      ErrorMiddleware.asyncHandler(giveawayController.getActiveGiveaways.bind(giveawayController))
    );

    this.router.get('/:id', 
      ErrorMiddleware.asyncHandler(giveawayController.getGiveawayById.bind(giveawayController))
    );

    // Admin routes
    this.router.get('/admin/all', 
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(giveawayController.getAllGiveaways.bind(giveawayController))
    );

    this.router.post('/', 
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(giveawayController.createGiveaway.bind(giveawayController))
    );

    this.router.put('/:id', 
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(giveawayController.updateGiveaway.bind(giveawayController))
    );

    this.router.delete('/:id', 
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(giveawayController.deleteGiveaway.bind(giveawayController))
    );
  }

  getRouter() {
    return this.router;
  }
}

module.exports = GiveawayRoutes;
