/**
 * Lottery Database Model
 * Mongoose schema for lottery data persistence
 */

const mongoose = require('mongoose');
const { LotteryStatus } = require('../../../shared/enums');
const { LOTTERY } = require('../../../shared/constants');

const ticketSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  username: {
    type: String,
    required: true
  },
  numbers: [{
    type: Number,
    required: true
  }],
  purchaseTime: {
    type: Date,
    default: Date.now
  },
  ticketId: {
    type: String,
    required: true,
    unique: true
  }
}, { _id: true });

const lotterySchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  description: {
    type: String,
    required: true,
    maxlength: 1000
  },
  ticketPrice: {
    type: Number,
    required: true,
    min: 0
  },
  maxTickets: {
    type: Number,
    required: true,
    min: 1,
    max: LOTTERY.MAX_TICKETS
  },
  numbers: [{
    type: Number,
    required: true,
    min: LOTTERY.NUMBER_RANGE_MIN,
    max: LOTTERY.NUMBER_RANGE_MAX
  }],
  tickets: [ticketSchema],
  startTime: {
    type: Date,
    required: true
  },
  endTime: {
    type: Date,
    required: true
  },
  status: {
    type: String,
    enum: Object.values(LotteryStatus),
    default: LotteryStatus.PENDING
  },
  winnerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  winningNumber: {
    type: Number,
    min: LOTTERY.NUMBER_RANGE_MIN,
    max: LOTTERY.NUMBER_RANGE_MAX
  },
  winnerUsername: {
    type: String
  },
  drawTime: {
    type: Date
  },
  totalPrize: {
    type: Number,
    default: 0,
    min: 0
  },
  featured: {
    type: Boolean,
    default: false
  },
  metadata: {
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    approvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    approvedAt: Date
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
lotterySchema.index({ status: 1, endTime: 1 });
lotterySchema.index({ startTime: 1, endTime: 1 });
lotterySchema.index({ featured: 1, status: 1 });
lotterySchema.index({ 'tickets.userId': 1 });
lotterySchema.index({ title: 'text', description: 'text' });

// Virtual for lottery duration
lotterySchema.virtual('duration').get(function() {
  return this.endTime - this.startTime;
});

// Virtual for time remaining
lotterySchema.virtual('timeRemaining').get(function() {
  const now = new Date();
  return Math.max(0, this.endTime - now);
});

// Virtual for is active
lotterySchema.virtual('isActive').get(function() {
  const now = new Date();
  return this.status === LotteryStatus.ACTIVE && 
         now >= this.startTime && 
         now <= this.endTime;
});

// Virtual for has started
lotterySchema.virtual('hasStarted').get(function() {
  return new Date() >= this.startTime;
});

// Virtual for has ended
lotterySchema.virtual('hasEnded').get(function() {
  return new Date() > this.endTime;
});

// Virtual for tickets sold
lotterySchema.virtual('ticketsSold').get(function() {
  return this.tickets.length;
});

// Virtual for tickets remaining
lotterySchema.virtual('ticketsRemaining').get(function() {
  return this.maxTickets - this.tickets.length;
});

// Virtual for is sold out
lotterySchema.virtual('isSoldOut').get(function() {
  return this.tickets.length >= this.maxTickets;
});

// Virtual for can purchase
lotterySchema.virtual('canPurchase').get(function() {
  return this.isActive && !this.hasEnded && !this.isSoldOut;
});

// Pre-save middleware
lotterySchema.pre('save', function(next) {
  // Validate end time is after start time
  if (this.endTime <= this.startTime) {
    return next(new Error('End time must be after start time'));
  }

  // Validate numbers
  if (this.numbers.length !== LOTTERY.MIN_NUMBERS) {
    return next(new Error(`Lottery must have exactly ${LOTTERY.MIN_NUMBERS} numbers`));
  }

  // Check number uniqueness
  const uniqueNumbers = new Set(this.numbers);
  if (uniqueNumbers.size !== this.numbers.length) {
    return next(new Error('All lottery numbers must be unique'));
  }

  // Check minimum difference between numbers
  const sortedNumbers = [...this.numbers].sort((a, b) => a - b);
  for (let i = 1; i < sortedNumbers.length; i++) {
    const difference = sortedNumbers[i] - sortedNumbers[i - 1];
    if (difference < LOTTERY.MIN_DIFFERENCE) {
      return next(new Error(`Minimum difference between numbers must be ${LOTTERY.MIN_DIFFERENCE}`));
    }
  }

  next();
});

// Instance methods
lotterySchema.methods.canPurchaseTicket = function() {
  const now = new Date();
  return this.status === LotteryStatus.ACTIVE && 
         now >= this.startTime && 
         now <= this.endTime &&
         this.tickets.length < this.maxTickets;
};

lotterySchema.methods.getUserTicketCount = function(userId) {
  return this.tickets.filter(ticket => 
    ticket.userId.toString() === userId.toString()
  ).length;
};

lotterySchema.methods.canUserPurchaseTicket = function(userId, quantity = 1) {
  if (!this.canPurchaseTicket()) {
    return false;
  }

  const userTicketCount = this.getUserTicketCount(userId);
  return (userTicketCount + quantity) <= LOTTERY.MAX_TICKETS_PER_USER;
};

lotterySchema.methods.purchaseTicket = function(userId, username, quantity = 1) {
  if (!this.canUserPurchaseTicket(userId, quantity)) {
    throw new Error('Cannot purchase ticket');
  }

  if (this.tickets.length + quantity > this.maxTickets) {
    throw new Error('Not enough tickets available');
  }

  const newTickets = [];
  for (let i = 0; i < quantity; i++) {
    const ticket = {
      userId,
      username,
      numbers: [...this.numbers], // All numbers in each ticket
      purchaseTime: new Date(),
      ticketId: this.generateTicketId()
    };
    
    newTickets.push(ticket);
    this.tickets.push(ticket);
  }

  this.totalPrize += this.ticketPrice * quantity;
  return newTickets;
};

lotterySchema.methods.generateTicketId = function() {
  return Date.now().toString() + Math.random().toString(36).substr(2, 9);
};

lotterySchema.methods.selectWinningNumber = function() {
  if (this.numbers.length === 0) {
    throw new Error('No numbers available for lottery');
  }
  
  const randomIndex = Math.floor(Math.random() * this.numbers.length);
  return this.numbers[randomIndex];
};

lotterySchema.methods.findWinner = function(winningNumber) {
  if (this.tickets.length === 0) {
    return null;
  }

  // Since all tickets contain all numbers, randomly select a winner
  const randomTicketIndex = Math.floor(Math.random() * this.tickets.length);
  return this.tickets[randomTicketIndex];
};

lotterySchema.methods.endLottery = function() {
  if (this.status !== LotteryStatus.ACTIVE) {
    throw new Error('Only active lotteries can be ended');
  }

  if (this.tickets.length === 0) {
    throw new Error('Cannot end lottery with no tickets sold');
  }

  // Select winning number
  this.winningNumber = this.selectWinningNumber();
  
  // Find winner
  const winner = this.findWinner(this.winningNumber);
  if (winner) {
    this.winnerId = winner.userId;
    this.winnerUsername = winner.username;
  }

  this.status = LotteryStatus.COMPLETED;
  this.drawTime = new Date();
};

lotterySchema.methods.getUserTickets = function(userId) {
  return this.tickets.filter(ticket => 
    ticket.userId.toString() === userId.toString()
  );
};

// Static methods
lotterySchema.statics.findActive = function(options = {}) {
  const now = new Date();
  return this.find({
    status: LotteryStatus.ACTIVE,
    startTime: { $lte: now },
    endTime: { $gt: now }
  }, null, options);
};

lotterySchema.statics.findEndingSoon = function(hours = 24) {
  const now = new Date();
  const threshold = new Date(now.getTime() + (hours * 60 * 60 * 1000));
  
  return this.find({
    status: LotteryStatus.ACTIVE,
    endTime: { $lte: threshold, $gt: now }
  });
};

lotterySchema.statics.findExpired = function() {
  const now = new Date();
  return this.find({
    status: LotteryStatus.ACTIVE,
    endTime: { $lte: now }
  });
};

module.exports = mongoose.model('Lottery', lotterySchema);
