const mongoose = require('mongoose');
require('dotenv').config();

async function debugProductEntity() {
  try {
    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb+srv://emrevaryemez22:<EMAIL>/robinhoot?retryWrites=true&w=majority');
    console.log('✅ Connected to database');

    // Import ProductModel
    const ProductModel = require('./src/infrastructure/database/models/Product');
    const Product = require('./src/core/entities/Product');

    // Get categories and admin user
    const db = mongoose.connection.db;
    const categories = await db.collection('categories').find({}).toArray();
    const adminUser = await db.collection('users').findOne({ email: '<EMAIL>' });
    
    const testCategoryId = categories.find(cat => cat.slug === 'elektronik')?._id;
    
    console.log('🎯 Test data:');
    console.log('Category ID:', testCategoryId);
    console.log('Admin User ID:', adminUser._id);

    // Create product using ProductModel directly
    console.log('\n📦 Creating product with ProductModel...');
    
    const productData = {
      name: 'Entity Debug Product',
      description: 'Entity debug test için oluşturulan ürün',
      shortDescription: 'Entity debug test ürünü',
      price: 1500,
      originalPrice: 2000,
      discountPercentage: 25,
      categoryId: testCategoryId,
      sellerId: adminUser._id,
      images: [
        {
          url: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500',
          alt: 'Entity Debug Product Image',
          isPrimary: true
        }
      ],
      stock: 100,
      lowStockThreshold: 10,
      specifications: {
        'Marka': 'Entity Debug Brand',
        'Model': 'ENTITY-DEBUG-2024'
      },
      tags: ['entity', 'debug'],
      featured: true,
      status: 'active'
    };

    console.log('Product data to save:', JSON.stringify(productData, null, 2));

    const product = new ProductModel(productData);
    const savedProduct = await product.save();
    
    console.log('\n✅ Product saved successfully');
    console.log('Saved product _id:', savedProduct._id);
    console.log('Saved product toObject():', JSON.stringify(savedProduct.toObject(), null, 2));

    // Test ProductEntity.fromPersistence
    console.log('\n🔧 Testing ProductEntity.fromPersistence...');
    
    const savedProductObj = savedProduct.toObject();
    console.log('Data passed to fromPersistence:', JSON.stringify(savedProductObj, null, 2));
    
    const productEntity = Product.fromPersistence(savedProductObj);
    console.log('Created ProductEntity ID:', productEntity.id);
    console.log('Created ProductEntity name:', productEntity.name);
    console.log('ProductEntity toJSON():', JSON.stringify(productEntity.toJSON(), null, 2));

    // Clean up
    await ProductModel.findByIdAndDelete(savedProduct._id);
    console.log('\n🧹 Test product cleaned up');

    await mongoose.disconnect();
    console.log('✅ Disconnected from database');

  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

debugProductEntity();
