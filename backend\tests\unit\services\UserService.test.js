/**
 * User Service Unit Tests
 */

const UserService = require('../../../src/business/services/UserService');
const { BusinessException, ValidationException, AuthorizationException } = require('../../../src/core/exceptions');
const { UserRole } = require('../../../src/shared/enums');

describe('UserService', () => {
  let userService;
  let mockUserRepository;
  let mockPasswordService;
  let mockTokenService;
  let mockEmailService;

  beforeEach(() => {
    // Mock dependencies
    mockUserRepository = {
      findById: jest.fn(),
      findByEmail: jest.fn(),
      findByUsername: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      findAll: jest.fn(),
      search: jest.fn(),
      updateLastLogin: jest.fn(),
      updatePassword: jest.fn(),
      updatePermissions: jest.fn(),
      getStatistics: jest.fn()
    };

    mockPasswordService = {
      hash: jest.fn(),
      compare: jest.fn()
    };

    mockTokenService = {
      generateAccessToken: jest.fn(),
      generateRefreshToken: jest.fn(),
      verifyToken: jest.fn()
    };

    mockEmailService = {
      sendWelcomeEmail: jest.fn(),
      sendPasswordResetEmail: jest.fn(),
      sendNotificationEmail: jest.fn()
    };

    userService = new UserService(
      mockUserRepository,
      mockPasswordService,
      mockTokenService,
      mockEmailService
    );
  });

  describe('registerUser', () => {
    it('should register a new user successfully', async () => {
      // Arrange
      const userData = testUtils.createTestUser();
      const hashedPassword = 'hashedPassword123';
      const savedUser = { ...userData, id: '123', password: hashedPassword };

      mockUserRepository.findByEmail.mockResolvedValue(null);
      mockUserRepository.findByUsername.mockResolvedValue(null);
      mockPasswordService.hash.mockResolvedValue(hashedPassword);
      mockUserRepository.create.mockResolvedValue(savedUser);

      // Act
      const result = await userService.registerUser(userData);

      // Assert
      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith(userData.email);
      expect(mockPasswordService.hash).toHaveBeenCalledWith(userData.password);
      expect(mockUserRepository.create).toHaveBeenCalled();
      expect(mockEmailService.sendWelcomeEmail).toHaveBeenCalled();
      expect(result).toEqual(savedUser);
    });

    it('should throw ValidationException if email already exists', async () => {
      // Arrange
      const userData = testUtils.createTestUser();
      const existingUser = { id: '456', email: userData.email };

      mockUserRepository.findByEmail.mockResolvedValue(existingUser);

      // Act & Assert
      await expect(userService.registerUser(userData)).rejects.toThrow(ValidationException);
      expect(mockUserRepository.create).not.toHaveBeenCalled();
    });

    it('should throw ValidationException if username already exists', async () => {
      // Arrange
      const userData = testUtils.createTestUser({ username: 'testuser' });
      const existingUser = { id: '456', username: userData.username };

      mockUserRepository.findByEmail.mockResolvedValue(null);
      mockUserRepository.findByUsername.mockResolvedValue(existingUser);

      // Act & Assert
      await expect(userService.registerUser(userData)).rejects.toThrow(ValidationException);
      expect(mockUserRepository.create).not.toHaveBeenCalled();
    });
  });

  describe('authenticateUser', () => {
    it('should authenticate user with valid credentials', async () => {
      // Arrange
      const email = '<EMAIL>';
      const password = 'password123';
      const user = testUtils.createTestUser({ id: '123', email, password: 'hashedPassword' });
      const accessToken = 'accessToken123';
      const refreshToken = 'refreshToken123';

      mockUserRepository.findByEmail.mockResolvedValue(user);
      mockPasswordService.compare.mockResolvedValue(true);
      mockTokenService.generateAccessToken.mockReturnValue(accessToken);
      mockTokenService.generateRefreshToken.mockReturnValue(refreshToken);

      // Act
      const result = await userService.authenticateUser(email, password);

      // Assert
      expect(mockUserRepository.findByEmail).toHaveBeenCalledWith(email);
      expect(mockPasswordService.compare).toHaveBeenCalledWith(password, user.password);
      expect(mockTokenService.generateAccessToken).toHaveBeenCalledWith(user);
      expect(mockTokenService.generateRefreshToken).toHaveBeenCalledWith(user);
      expect(mockUserRepository.updateLastLogin).toHaveBeenCalledWith(user.id);
      expect(result).toEqual({
        user: expect.objectContaining({ id: user.id, email: user.email }),
        accessToken,
        refreshToken
      });
    });

    it('should throw ValidationException for invalid email', async () => {
      // Arrange
      const email = '<EMAIL>';
      const password = 'password123';

      mockUserRepository.findByEmail.mockResolvedValue(null);

      // Act & Assert
      await expect(userService.authenticateUser(email, password)).rejects.toThrow(ValidationException);
      expect(mockPasswordService.compare).not.toHaveBeenCalled();
    });

    it('should throw ValidationException for invalid password', async () => {
      // Arrange
      const email = '<EMAIL>';
      const password = 'wrongpassword';
      const user = testUtils.createTestUser({ email, password: 'hashedPassword' });

      mockUserRepository.findByEmail.mockResolvedValue(user);
      mockPasswordService.compare.mockResolvedValue(false);

      // Act & Assert
      await expect(userService.authenticateUser(email, password)).rejects.toThrow(ValidationException);
      expect(mockTokenService.generateAccessToken).not.toHaveBeenCalled();
    });

    it('should throw AuthorizationException for inactive user', async () => {
      // Arrange
      const email = '<EMAIL>';
      const password = 'password123';
      const user = testUtils.createTestUser({ email, isActive: false });

      mockUserRepository.findByEmail.mockResolvedValue(user);
      mockPasswordService.compare.mockResolvedValue(true);

      // Act & Assert
      await expect(userService.authenticateUser(email, password)).rejects.toThrow(AuthorizationException);
    });
  });

  describe('getUserById', () => {
    it('should return user by ID', async () => {
      // Arrange
      const userId = '123';
      const user = testUtils.createTestUser({ id: userId });

      mockUserRepository.findById.mockResolvedValue(user);

      // Act
      const result = await userService.getUserById(userId);

      // Assert
      expect(mockUserRepository.findById).toHaveBeenCalledWith(userId);
      expect(result).toEqual(user);
    });

    it('should throw ValidationException if user not found', async () => {
      // Arrange
      const userId = 'nonexistent';

      mockUserRepository.findById.mockResolvedValue(null);

      // Act & Assert
      await expect(userService.getUserById(userId)).rejects.toThrow(ValidationException);
    });
  });

  describe('updateUser', () => {
    it('should update user successfully', async () => {
      // Arrange
      const userId = '123';
      const updateData = { name: 'Updated Name' };
      const updatedBy = '123';
      const existingUser = testUtils.createTestUser({ id: userId });
      const updatedUser = { ...existingUser, ...updateData };

      mockUserRepository.findById.mockResolvedValue(existingUser);
      mockUserRepository.update.mockResolvedValue(updatedUser);

      // Act
      const result = await userService.updateUser(userId, updateData, updatedBy);

      // Assert
      expect(mockUserRepository.findById).toHaveBeenCalledWith(userId);
      expect(mockUserRepository.update).toHaveBeenCalledWith(userId, expect.objectContaining(updateData));
      expect(result).toEqual(updatedUser);
    });

    it('should allow admin to update any user', async () => {
      // Arrange
      const userId = '123';
      const updateData = { name: 'Updated Name' };
      const adminId = '456';
      const existingUser = testUtils.createTestUser({ id: userId });
      const admin = testUtils.createTestAdmin({ id: adminId });
      const updatedUser = { ...existingUser, ...updateData };

      mockUserRepository.findById
        .mockResolvedValueOnce(existingUser)
        .mockResolvedValueOnce(admin);
      mockUserRepository.update.mockResolvedValue(updatedUser);

      // Act
      const result = await userService.updateUser(userId, updateData, adminId);

      // Assert
      expect(result).toEqual(updatedUser);
    });

    it('should throw AuthorizationException if non-admin tries to update another user', async () => {
      // Arrange
      const userId = '123';
      const updateData = { name: 'Updated Name' };
      const otherUserId = '456';
      const existingUser = testUtils.createTestUser({ id: userId });
      const otherUser = testUtils.createTestUser({ id: otherUserId, role: UserRole.USER });

      mockUserRepository.findById
        .mockResolvedValueOnce(existingUser)
        .mockResolvedValueOnce(otherUser);

      // Act & Assert
      await expect(userService.updateUser(userId, updateData, otherUserId)).rejects.toThrow(AuthorizationException);
      expect(mockUserRepository.update).not.toHaveBeenCalled();
    });
  });

  describe('deleteUser', () => {
    it('should delete user successfully (admin)', async () => {
      // Arrange
      const userId = '123';
      const deletedBy = '456';
      const user = testUtils.createTestUser({ id: userId });
      const admin = testUtils.createTestAdmin({ id: deletedBy });

      mockUserRepository.findById
        .mockResolvedValueOnce(user)
        .mockResolvedValueOnce(admin);
      mockUserRepository.delete.mockResolvedValue(true);

      // Act
      const result = await userService.deleteUser(userId, deletedBy);

      // Assert
      expect(mockUserRepository.findById).toHaveBeenCalledWith(userId);
      expect(mockUserRepository.delete).toHaveBeenCalledWith(userId);
      expect(result).toBe(true);
    });

    it('should throw AuthorizationException if non-admin tries to delete user', async () => {
      // Arrange
      const userId = '123';
      const deletedBy = '456';
      const user = testUtils.createTestUser({ id: userId });
      const nonAdmin = testUtils.createTestUser({ id: deletedBy, role: UserRole.USER });

      mockUserRepository.findById
        .mockResolvedValueOnce(user)
        .mockResolvedValueOnce(nonAdmin);

      // Act & Assert
      await expect(userService.deleteUser(userId, deletedBy)).rejects.toThrow(AuthorizationException);
      expect(mockUserRepository.delete).not.toHaveBeenCalled();
    });
  });

  describe('changePassword', () => {
    it('should change password successfully', async () => {
      // Arrange
      const userId = '123';
      const currentPassword = 'oldPassword';
      const newPassword = 'newPassword';
      const user = testUtils.createTestUser({ id: userId, password: 'hashedOldPassword' });
      const hashedNewPassword = 'hashedNewPassword';

      mockUserRepository.findById.mockResolvedValue(user);
      mockPasswordService.compare.mockResolvedValue(true);
      mockPasswordService.hash.mockResolvedValue(hashedNewPassword);
      mockUserRepository.updatePassword.mockResolvedValue(true);

      // Act
      const result = await userService.changePassword(userId, currentPassword, newPassword);

      // Assert
      expect(mockPasswordService.compare).toHaveBeenCalledWith(currentPassword, user.password);
      expect(mockPasswordService.hash).toHaveBeenCalledWith(newPassword);
      expect(mockUserRepository.updatePassword).toHaveBeenCalledWith(userId, hashedNewPassword);
      expect(result).toBe(true);
    });

    it('should throw ValidationException for incorrect current password', async () => {
      // Arrange
      const userId = '123';
      const currentPassword = 'wrongPassword';
      const newPassword = 'newPassword';
      const user = testUtils.createTestUser({ id: userId, password: 'hashedOldPassword' });

      mockUserRepository.findById.mockResolvedValue(user);
      mockPasswordService.compare.mockResolvedValue(false);

      // Act & Assert
      await expect(userService.changePassword(userId, currentPassword, newPassword)).rejects.toThrow(ValidationException);
      expect(mockUserRepository.updatePassword).not.toHaveBeenCalled();
    });
  });

  describe('updateUserPermissions', () => {
    it('should update user permissions successfully (admin)', async () => {
      // Arrange
      const userId = '123';
      const permissions = { canSell: true };
      const updatedBy = '456';
      const user = testUtils.createTestUser({ id: userId });
      const admin = testUtils.createTestAdmin({ id: updatedBy });
      const updatedUser = { ...user, permissions };

      mockUserRepository.findById
        .mockResolvedValueOnce(user)
        .mockResolvedValueOnce(admin);
      mockUserRepository.updatePermissions.mockResolvedValue(updatedUser);

      // Act
      const result = await userService.updateUserPermissions(userId, permissions, updatedBy);

      // Assert
      expect(mockUserRepository.updatePermissions).toHaveBeenCalledWith(userId, permissions);
      expect(result).toEqual(updatedUser);
    });

    it('should throw AuthorizationException if non-admin tries to update permissions', async () => {
      // Arrange
      const userId = '123';
      const permissions = { canSell: true };
      const updatedBy = '456';
      const user = testUtils.createTestUser({ id: userId });
      const nonAdmin = testUtils.createTestUser({ id: updatedBy, role: UserRole.USER });

      mockUserRepository.findById
        .mockResolvedValueOnce(user)
        .mockResolvedValueOnce(nonAdmin);

      // Act & Assert
      await expect(userService.updateUserPermissions(userId, permissions, updatedBy)).rejects.toThrow(AuthorizationException);
      expect(mockUserRepository.updatePermissions).not.toHaveBeenCalled();
    });
  });

  describe('getAllUsers', () => {
    it('should return paginated users', async () => {
      // Arrange
      const options = { page: 1, limit: 10 };
      const users = [
        testUtils.createTestUser({ id: '1' }),
        testUtils.createTestUser({ id: '2' })
      ];
      const result = {
        users,
        pagination: { page: 1, limit: 10, total: 2, pages: 1 }
      };

      mockUserRepository.findAll.mockResolvedValue(result);

      // Act
      const response = await userService.getAllUsers(options);

      // Assert
      expect(mockUserRepository.findAll).toHaveBeenCalledWith(options);
      expect(response).toEqual(result);
    });
  });

  describe('searchUsers', () => {
    it('should search users successfully', async () => {
      // Arrange
      const query = 'test';
      const options = { page: 1, limit: 10 };
      const users = [testUtils.createTestUser({ name: 'Test User' })];
      const result = {
        users,
        pagination: { page: 1, limit: 10, total: 1, pages: 1 }
      };

      mockUserRepository.search.mockResolvedValue(result);

      // Act
      const response = await userService.searchUsers(query, options);

      // Assert
      expect(mockUserRepository.search).toHaveBeenCalledWith(query, options);
      expect(response).toEqual(result);
    });
  });
});
