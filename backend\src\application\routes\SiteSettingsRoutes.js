/**
 * Site Settings Routes Configuration
 * Defines all site settings related API endpoints
 */

const express = require('express');
const ErrorMiddleware = require('../middleware/ErrorMiddleware');

class SiteSettingsRoutes {
  constructor(container) {
    this.container = container;
    this.router = express.Router();
    this.setupRoutes();
  }

  setupRoutes() {
    const siteSettingsController = new (require('../controllers/SiteSettingsController'))(this.container);
    const authMiddleware = this.container.resolve('authMiddleware');

    // Public routes
    this.router.get('/sections', 
      ErrorMiddleware.asyncHandler(siteSettingsController.getSections.bind(siteSettingsController))
    );

    this.router.get('/', 
      ErrorMiddleware.asyncHandler(siteSettingsController.getSettings.bind(siteSettingsController))
    );

    // Admin routes
    this.router.put('/sections', 
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(siteSettingsController.updateSections.bind(siteSettingsController))
    );

    this.router.put('/', 
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(siteSettingsController.updateSettings.bind(siteSettingsController))
    );
  }

  getRouter() {
    return this.router;
  }
}

module.exports = SiteSettingsRoutes;
