/**
 * Test Login API
 * Direct API test for login functionality
 */

const fetch = require('node-fetch');

async function testLogin() {
  try {
    console.log('🧪 Testing login API...');

    const loginData = {
      email: 'nazo<PERSON><PERSON><PERSON>@gmail.com',
      password: 'eEf8kbpE9G8XhiL'
    };

    console.log('📤 Sending login request:', { email: loginData.email, hasPassword: !!loginData.password });

    // First test auth routes
    console.log('\n🧪 Testing auth routes...');
    try {
      const testResponse = await fetch('http://localhost:5000/api/auth/test');
      const testData = await testResponse.json();
      console.log('✅ Auth routes test:', testData);
    } catch (error) {
      console.error('❌ Auth routes test failed:', error.message);
    }

    // Test multiple endpoints
    const endpoints = [
      'http://localhost:5000/api/auth/login',
      'http://localhost:5000/api/users/login',
      'http://localhost:5000/api/v1/auth/login',
      'http://localhost:5000/api/v1/users/login'
    ];

    for (const endpoint of endpoints) {
      console.log(`\n🧪 Testing endpoint: ${endpoint}`);

      try {
        const response = await fetch(endpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(loginData)
        });

        console.log('📥 Response status:', response.status);
        console.log('📥 Response headers:', Object.fromEntries(response.headers.entries()));

        const responseData = await response.json();
        console.log('📥 Response data:', responseData);

        if (response.ok) {
          console.log('✅ Login successful!');
          console.log('👤 User:', responseData.data?.user);
          console.log('🔑 Token:', responseData.data?.token ? 'Present' : 'Missing');

          // Decode JWT token to see its contents
          if (responseData.data?.token) {
            try {
              const tokenParts = responseData.data.token.split('.');
              const payload = JSON.parse(Buffer.from(tokenParts[1], 'base64').toString());
              console.log('🔍 Token payload:', payload);
            } catch (error) {
              console.error('❌ Failed to decode token:', error.message);
            }
          }

          break; // Stop testing other endpoints if one works
        } else {
          console.log('❌ Login failed!');
          console.log('💬 Message:', responseData.message);
        }

      } catch (endpointError) {
        console.error(`💥 Endpoint ${endpoint} error:`, endpointError.message);
      }
    }

  } catch (error) {
    console.error('💥 Test error:', error.message);
  }
}

// Run the test
testLogin();
