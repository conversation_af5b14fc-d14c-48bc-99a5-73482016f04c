/**
 * Activate All Users Script
 * Tüm kullanıcıları aktif hale getirir
 */

const mongoose = require('mongoose');
require('dotenv').config();

// User model'ini import et
const User = require('../src/infrastructure/database/models/User');

async function activateAllUsers() {
  try {
    // MongoDB'ye bağlan
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/robinhoot');
    console.log('✅ MongoDB bağlantısı başarılı');

    // Tüm kullanıcıları aktif hale getir
    const result = await User.updateMany(
      {}, // Tüm kullanıcılar
      {
        $set: {
          status: 'active',
          isEmailVerified: true,
          approvalStatus: 'approved',
          canBid: true,
          canParticipateInLottery: true
        }
      }
    );

    console.log(`✅ ${result.modifiedCount} kullanıcı aktif hale getirildi`);

    // Admin kullanıcısını kontrol et
    const adminUser = await User.findOne({ email: '<EMAIL>' });
    if (adminUser) {
      console.log('✅ Admin kullanıcısı bulundu:', {
        email: adminUser.email,
        role: adminUser.role,
        isActive: adminUser.isActive
      });
    } else {
      console.log('❌ Admin kullanıcısı bulunamadı');
    }

    // Tüm kullanıcıları listele
    const users = await User.find({}).select('email username role status approvalStatus canBid');
    console.log('\n📋 Kullanıcı Listesi:');
    users.forEach(user => {
      console.log(`- ${user.email} (${user.username}) - ${user.role} - Status: ${user.status} - Approval: ${user.approvalStatus} - CanBid: ${user.canBid}`);
    });

  } catch (error) {
    console.error('❌ Hata:', error);
  } finally {
    await mongoose.disconnect();
    console.log('✅ MongoDB bağlantısı kapatıldı');
    process.exit(0);
  }
}

// Script'i çalıştır
activateAllUsers();
