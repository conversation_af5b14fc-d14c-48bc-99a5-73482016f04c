/**
 * Lottery Controller
 * Application layer - HTTP request handling for lottery operations
 */

const { HttpStatus } = require('../../shared/enums');
const { PAGINATION } = require('../../shared/constants');

class LotteryController {
  constructor(lotteryService) {
    this.lotteryService = lotteryService;
  }

  /**
   * Get all lotteries
   */
  async getAllLotteries(req, res, next) {
    try {
      const {
        status,
        sortBy = 'createdAt',
        sortOrder = 'desc',
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        search
      } = req.query;

      const options = {
        page: parseInt(page),
        limit: Math.min(parseInt(limit), PAGINATION.MAX_LIMIT),
        sortBy,
        sortOrder,
        status
      };

      let result;
      if (search) {
        result = await this.lotteryService.searchLotteries(search, options);
      } else {
        result = await this.lotteryService.getAllLotteries(options);
      }

      res.status(HttpStatus.OK).json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get lottery by ID
   */
  async getLotteryById(req, res, next) {
    try {
      const { id } = req.params;
      const lottery = await this.lotteryService.getLotteryById(id);

      res.status(HttpStatus.OK).json({
        success: true,
        data: { lottery }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Create new lottery
   */
  async createLottery(req, res, next) {
    try {
      const lottery = await this.lotteryService.createLottery(req.body, req.user.id);

      res.status(HttpStatus.CREATED).json({
        success: true,
        message: 'Lottery created successfully',
        data: { lottery }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update lottery
   */
  async updateLottery(req, res, next) {
    try {
      const { id } = req.params;
      const lottery = await this.lotteryService.updateLottery(id, req.body, req.user.id);

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'Lottery updated successfully',
        data: { lottery }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Delete lottery
   */
  async deleteLottery(req, res, next) {
    try {
      const { id } = req.params;
      await this.lotteryService.deleteLottery(id, req.user.id);

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'Lottery deleted successfully'
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Purchase lottery ticket
   */
  async purchaseTicket(req, res, next) {
    try {
      const { id } = req.params;
      const { quantity = 1 } = req.body;

      if (!quantity || quantity <= 0 || quantity > 10) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          message: 'Quantity must be between 1 and 10'
        });
      }

      const result = await this.lotteryService.purchaseTicket(
        id,
        req.user.id,
        parseInt(quantity)
      );

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'Ticket(s) purchased successfully',
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get active lotteries
   */
  async getActiveLotteries(req, res, next) {
    try {
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        sortBy = 'endTime',
        sortOrder = 'asc'
      } = req.query;

      const options = {
        page: parseInt(page),
        limit: Math.min(parseInt(limit), PAGINATION.MAX_LIMIT),
        sortBy,
        sortOrder
      };

      const result = await this.lotteryService.getActiveLotteries(options);

      res.status(HttpStatus.OK).json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get user's lottery tickets
   */
  async getUserTickets(req, res, next) {
    try {
      const {
        status,
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT
      } = req.query;

      const options = {
        page: parseInt(page),
        limit: Math.min(parseInt(limit), PAGINATION.MAX_LIMIT),
        status
      };

      const result = await this.lotteryService.getUserTickets(req.user.id, options);

      res.status(HttpStatus.OK).json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get lottery tickets
   */
  async getLotteryTickets(req, res, next) {
    try {
      const { id } = req.params;
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        sortBy = 'purchaseTime',
        sortOrder = 'desc'
      } = req.query;

      const options = {
        page: parseInt(page),
        limit: Math.min(parseInt(limit), PAGINATION.MAX_LIMIT),
        sortBy,
        sortOrder
      };

      const result = await this.lotteryService.getLotteryTickets(id, options);

      res.status(HttpStatus.OK).json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get ending soon lotteries
   */
  async getEndingSoonLotteries(req, res, next) {
    try {
      const { hours = 24 } = req.query;
      const lotteries = await this.lotteryService.getEndingSoonLotteries(parseInt(hours));

      res.status(HttpStatus.OK).json({
        success: true,
        data: { lotteries }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Start lottery (Admin)
   */
  async startLottery(req, res, next) {
    try {
      const { id } = req.params;
      const lottery = await this.lotteryService.startLottery(id, req.user.id);

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'Lottery started successfully',
        data: { lottery }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * End lottery and draw winner (Admin)
   */
  async endLottery(req, res, next) {
    try {
      const { id } = req.params;
      const lottery = await this.lotteryService.endLottery(id, req.user.id);

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'Lottery ended successfully',
        data: { lottery }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Cancel lottery (Admin)
   */
  async cancelLottery(req, res, next) {
    try {
      const { id } = req.params;
      const { reason } = req.body;

      const lottery = await this.lotteryService.cancelLottery(id, req.user.id, reason);

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'Lottery cancelled successfully',
        data: { lottery }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get lottery statistics
   */
  async getLotteryStatistics(req, res, next) {
    try {
      const { id } = req.params;
      const statistics = await this.lotteryService.getLotteryStatistics(id);

      res.status(HttpStatus.OK).json({
        success: true,
        data: { statistics }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Update lottery statuses (System)
   */
  async updateLotteryStatuses(req, res, next) {
    try {
      const result = await this.lotteryService.updateLotteryStatuses();

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'Lottery statuses updated successfully',
        data: result
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Get lottery counts by status (Admin)
   */
  async getLotteryCounts(req, res, next) {
    try {
      const counts = await this.lotteryService.getLotteryCounts();

      res.status(HttpStatus.OK).json({
        success: true,
        data: { counts }
      });
    } catch (error) {
      next(error);
    }
  }

  /**
   * Search lotteries
   */
  async searchLotteries(req, res, next) {
    try {
      const { q: query } = req.query;
      
      if (!query) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          message: 'Search query is required'
        });
      }

      const {
        status,
        sortBy = 'relevance',
        sortOrder = 'desc',
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT
      } = req.query;

      const options = {
        page: parseInt(page),
        limit: Math.min(parseInt(limit), PAGINATION.MAX_LIMIT),
        sortBy,
        sortOrder,
        status
      };

      const result = await this.lotteryService.searchLotteries(query, options);

      res.status(HttpStatus.OK).json({
        success: true,
        data: result
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = LotteryController;
