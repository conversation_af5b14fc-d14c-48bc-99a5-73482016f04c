/**
 * Custom Business Exceptions
 * <PERSON>ş mantığı için özel hata sınıfları
 */

const { ErrorType, HttpStatus } = require('../../shared/enums');

/**
 * Base Business Exception
 */
class BusinessException extends Error {
  constructor(message, type = ErrorType.BUSINESS_ERROR, statusCode = HttpStatus.BAD_REQUEST, details = null) {
    super(message);
    this.name = this.constructor.name;
    this.type = type;
    this.statusCode = statusCode;
    this.details = details;
    this.timestamp = new Date().toISOString();
    
    // Stack trace'i düzelt
    Error.captureStackTrace(this, this.constructor);
  }

  toJSON() {
    return {
      name: this.name,
      message: this.message,
      type: this.type,
      statusCode: this.statusCode,
      details: this.details,
      timestamp: this.timestamp
    };
  }
}

/**
 * Validation Exception
 */
class ValidationException extends BusinessException {
  constructor(message, details = null) {
    super(message, ErrorType.VALIDATION_ERROR, HttpStatus.UNPROCESSABLE_ENTITY, details);
  }
}

/**
 * Authentication Exception
 */
class AuthenticationException extends BusinessException {
  constructor(message = 'Authentication failed', details = null) {
    super(message, ErrorType.AUTHENTICATION_ERROR, HttpStatus.UNAUTHORIZED, details);
  }
}

/**
 * Authorization Exception
 */
class AuthorizationException extends BusinessException {
  constructor(message = 'Access denied', details = null) {
    super(message, ErrorType.AUTHORIZATION_ERROR, HttpStatus.FORBIDDEN, details);
  }
}

/**
 * Not Found Exception
 */
class NotFoundException extends BusinessException {
  constructor(resource = 'Resource', id = null) {
    const message = id ? `${resource} with id '${id}' not found` : `${resource} not found`;
    super(message, ErrorType.NOT_FOUND_ERROR, HttpStatus.NOT_FOUND, { resource, id });
  }
}

/**
 * Conflict Exception
 */
class ConflictException extends BusinessException {
  constructor(message, details = null) {
    super(message, ErrorType.BUSINESS_ERROR, HttpStatus.CONFLICT, details);
  }
}

/**
 * External Service Exception
 */
class ExternalServiceException extends BusinessException {
  constructor(service, message, details = null) {
    super(`External service error: ${service} - ${message}`, ErrorType.EXTERNAL_SERVICE_ERROR, HttpStatus.INTERNAL_SERVER_ERROR, details);
    this.service = service;
  }
}

/**
 * Database Exception
 */
class DatabaseException extends BusinessException {
  constructor(message, details = null) {
    super(`Database error: ${message}`, ErrorType.DATABASE_ERROR, HttpStatus.INTERNAL_SERVER_ERROR, details);
  }
}

// User specific exceptions
class UserNotFoundException extends NotFoundException {
  constructor(identifier) {
    super('User', identifier);
  }
}

class UserAlreadyExistsException extends ConflictException {
  constructor(field, value) {
    super(`User with ${field} '${value}' already exists`, { field, value });
  }
}

class InvalidCredentialsException extends AuthenticationException {
  constructor() {
    super('Invalid email or password');
  }
}

class UserNotActiveException extends AuthorizationException {
  constructor() {
    super('User account is not active');
  }
}

class UserNotApprovedException extends AuthorizationException {
  constructor() {
    super('User account is not approved');
  }
}

class EmailNotVerifiedException extends AuthorizationException {
  constructor() {
    super('Email address is not verified');
  }
}

// Product specific exceptions
class ProductNotFoundException extends NotFoundException {
  constructor(id) {
    super('Product', id);
  }
}

class InsufficientStockException extends BusinessException {
  constructor(productId, requestedQuantity, availableStock) {
    super(`Insufficient stock for product ${productId}. Requested: ${requestedQuantity}, Available: ${availableStock}`, 
          ErrorType.BUSINESS_ERROR, HttpStatus.BAD_REQUEST, 
          { productId, requestedQuantity, availableStock });
  }
}

class ProductNotAvailableException extends BusinessException {
  constructor(productId) {
    super(`Product ${productId} is not available for purchase`, ErrorType.BUSINESS_ERROR, HttpStatus.BAD_REQUEST, { productId });
  }
}

// Auction specific exceptions
class AuctionNotFoundException extends NotFoundException {
  constructor(id) {
    super('Auction', id);
  }
}

class AuctionNotActiveException extends BusinessException {
  constructor(auctionId) {
    super(`Auction ${auctionId} is not active`, ErrorType.BUSINESS_ERROR, HttpStatus.BAD_REQUEST, { auctionId });
  }
}

class InvalidBidAmountException extends BusinessException {
  constructor(bidAmount, minimumBid) {
    super(`Bid amount ${bidAmount} is below minimum bid ${minimumBid}`, 
          ErrorType.BUSINESS_ERROR, HttpStatus.BAD_REQUEST, 
          { bidAmount, minimumBid });
  }
}

class UserCannotBidException extends AuthorizationException {
  constructor() {
    super('User does not have permission to bid');
  }
}

// Order specific exceptions
class OrderNotFoundException extends NotFoundException {
  constructor(id) {
    super('Order', id);
  }
}

class OrderCannotBeCancelledException extends BusinessException {
  constructor(orderId, status) {
    super(`Order ${orderId} cannot be cancelled. Current status: ${status}`, 
          ErrorType.BUSINESS_ERROR, HttpStatus.BAD_REQUEST, 
          { orderId, status });
  }
}

// Lottery specific exceptions
class LotteryNotFoundException extends NotFoundException {
  constructor(id) {
    super('Lottery', id);
  }
}

class LotteryNotActiveException extends BusinessException {
  constructor(lotteryId) {
    super(`Lottery ${lotteryId} is not active`, ErrorType.BUSINESS_ERROR, HttpStatus.BAD_REQUEST, { lotteryId });
  }
}

class MaxTicketsExceededException extends BusinessException {
  constructor(maxTickets) {
    super(`Maximum ${maxTickets} tickets allowed per user`, ErrorType.BUSINESS_ERROR, HttpStatus.BAD_REQUEST, { maxTickets });
  }
}

class UserCannotParticipateInLotteryException extends AuthorizationException {
  constructor() {
    super('User does not have permission to participate in lottery');
  }
}

// Category specific exceptions
class CategoryNotFoundException extends NotFoundException {
  constructor(id) {
    super('Category', id);
  }
}

class CategoryHasProductsException extends BusinessException {
  constructor(categoryId, productCount) {
    super(`Category ${categoryId} cannot be deleted. It has ${productCount} products`, 
          ErrorType.BUSINESS_ERROR, HttpStatus.BAD_REQUEST, 
          { categoryId, productCount });
  }
}

module.exports = {
  // Base exceptions
  BusinessException,
  ValidationException,
  AuthenticationException,
  AuthorizationException,
  NotFoundException,
  ConflictException,
  ExternalServiceException,
  DatabaseException,
  
  // User exceptions
  UserNotFoundException,
  UserAlreadyExistsException,
  InvalidCredentialsException,
  UserNotActiveException,
  UserNotApprovedException,
  EmailNotVerifiedException,
  
  // Product exceptions
  ProductNotFoundException,
  InsufficientStockException,
  ProductNotAvailableException,
  
  // Auction exceptions
  AuctionNotFoundException,
  AuctionNotActiveException,
  InvalidBidAmountException,
  UserCannotBidException,
  
  // Order exceptions
  OrderNotFoundException,
  OrderCannotBeCancelledException,
  
  // Lottery exceptions
  LotteryNotFoundException,
  LotteryNotActiveException,
  MaxTicketsExceededException,
  UserCannotParticipateInLotteryException,
  
  // Category exceptions
  CategoryNotFoundException,
  CategoryHasProductsException
};
