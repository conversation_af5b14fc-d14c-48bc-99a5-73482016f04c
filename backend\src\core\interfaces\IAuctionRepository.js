/**
 * Auction Repository Interface
 * Defines contract for auction data operations
 */

class IAuctionRepository {
  /**
   * Create a new auction
   * @param {Object} auctionData - Auction data
   * @returns {Promise<Object>} Created auction
   */
  async create(auctionData) {
    throw new Error('Method not implemented');
  }

  /**
   * Find auction by ID
   * @param {string} id - Auction ID
   * @returns {Promise<Object|null>} Auction or null
   */
  async findById(id) {
    throw new Error('Method not implemented');
  }

  /**
   * Find auctions by seller ID
   * @param {string} sellerId - Seller ID
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Paginated auctions
   */
  async findBySellerId(sellerId, options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Find active auctions
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Paginated active auctions
   */
  async findActive(options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Find auctions by status
   * @param {string} status - Auction status
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Paginated auctions
   */
  async findByStatus(status, options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Search auctions
   * @param {string} query - Search query
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Search results
   */
  async search(query, options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Update auction
   * @param {string} id - Auction ID
   * @param {Object} updateData - Update data
   * @returns {Promise<Object>} Updated auction
   */
  async update(id, updateData) {
    throw new Error('Method not implemented');
  }

  /**
   * Delete auction
   * @param {string} id - Auction ID
   * @returns {Promise<boolean>} Success status
   */
  async delete(id) {
    throw new Error('Method not implemented');
  }

  /**
   * Add bid to auction
   * @param {string} auctionId - Auction ID
   * @param {Object} bidData - Bid data
   * @returns {Promise<Object>} Updated auction
   */
  async addBid(auctionId, bidData) {
    throw new Error('Method not implemented');
  }

  /**
   * Get auction bids
   * @param {string} auctionId - Auction ID
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Auction bids
   */
  async getBids(auctionId, options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Get user's bids
   * @param {string} userId - User ID
   * @param {Object} options - Query options
   * @returns {Promise<Object>} User's bids
   */
  async getUserBids(userId, options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Get ending soon auctions
   * @param {number} hours - Hours threshold
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Ending soon auctions
   */
  async findEndingSoon(hours = 24, options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Get expired auctions
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Expired auctions
   */
  async findExpired(options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Update auction status
   * @param {string} id - Auction ID
   * @param {string} status - New status
   * @returns {Promise<Object>} Updated auction
   */
  async updateStatus(id, status) {
    throw new Error('Method not implemented');
  }

  /**
   * Increment view count
   * @param {string} id - Auction ID
   * @returns {Promise<Object>} Updated auction
   */
  async incrementViewCount(id) {
    throw new Error('Method not implemented');
  }

  /**
   * Get auction statistics
   * @param {string} id - Auction ID
   * @returns {Promise<Object>} Auction statistics
   */
  async getStatistics(id) {
    throw new Error('Method not implemented');
  }

  /**
   * Get auctions count by status
   * @param {string} sellerId - Seller ID (optional)
   * @returns {Promise<Object>} Status counts
   */
  async getCountByStatus(sellerId = null) {
    throw new Error('Method not implemented');
  }
}

module.exports = IAuctionRepository;
