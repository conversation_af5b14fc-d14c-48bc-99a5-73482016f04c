/**
 * Lottery Repository Interface
 * Defines contract for lottery data operations
 */

class ILotteryRepository {
  /**
   * Create a new lottery
   * @param {Object} lotteryData - Lottery data
   * @returns {Promise<Object>} Created lottery
   */
  async create(lotteryData) {
    throw new Error('Method not implemented');
  }

  /**
   * Find lottery by ID
   * @param {string} id - Lottery ID
   * @returns {Promise<Object|null>} Lottery or null
   */
  async findById(id) {
    throw new Error('Method not implemented');
  }

  /**
   * Find all lotteries
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Paginated lotteries
   */
  async findAll(options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Find active lotteries
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Paginated active lotteries
   */
  async findActive(options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Find lotteries by status
   * @param {string} status - Lottery status
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Paginated lotteries
   */
  async findByStatus(status, options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Update lottery
   * @param {string} id - Lottery ID
   * @param {Object} updateData - Update data
   * @returns {Promise<Object>} Updated lottery
   */
  async update(id, updateData) {
    throw new Error('Method not implemented');
  }

  /**
   * Delete lottery
   * @param {string} id - Lottery ID
   * @returns {Promise<boolean>} Success status
   */
  async delete(id) {
    throw new Error('Method not implemented');
  }

  /**
   * Purchase ticket
   * @param {string} lotteryId - Lottery ID
   * @param {Object} ticketData - Ticket data
   * @returns {Promise<Object>} Updated lottery with new ticket
   */
  async purchaseTicket(lotteryId, ticketData) {
    throw new Error('Method not implemented');
  }

  /**
   * Get user's tickets
   * @param {string} userId - User ID
   * @param {Object} options - Query options
   * @returns {Promise<Object>} User's lottery tickets
   */
  async getUserTickets(userId, options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Get lottery tickets
   * @param {string} lotteryId - Lottery ID
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Lottery tickets
   */
  async getTickets(lotteryId, options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Update lottery status
   * @param {string} id - Lottery ID
   * @param {string} status - New status
   * @returns {Promise<Object>} Updated lottery
   */
  async updateStatus(id, status) {
    throw new Error('Method not implemented');
  }

  /**
   * Set lottery winner
   * @param {string} id - Lottery ID
   * @param {string} winnerId - Winner user ID
   * @param {number} winningNumber - Winning number
   * @param {string} winnerUsername - Winner username
   * @returns {Promise<Object>} Updated lottery
   */
  async setWinner(id, winnerId, winningNumber, winnerUsername) {
    throw new Error('Method not implemented');
  }

  /**
   * Get ending soon lotteries
   * @param {number} hours - Hours threshold
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Ending soon lotteries
   */
  async findEndingSoon(hours = 24, options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Get expired lotteries
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Expired lotteries
   */
  async findExpired(options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Get lottery statistics
   * @param {string} id - Lottery ID
   * @returns {Promise<Object>} Lottery statistics
   */
  async getStatistics(id) {
    throw new Error('Method not implemented');
  }

  /**
   * Get lotteries count by status
   * @returns {Promise<Object>} Status counts
   */
  async getCountByStatus() {
    throw new Error('Method not implemented');
  }

  /**
   * Search lotteries
   * @param {string} query - Search query
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Search results
   */
  async search(query, options = {}) {
    throw new Error('Method not implemented');
  }
}

module.exports = ILotteryRepository;
