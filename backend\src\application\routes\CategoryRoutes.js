/**
 * Category Routes Configuration
 * Defines all category-related API endpoints
 */

const express = require('express');
const ErrorMiddleware = require('../middleware/ErrorMiddleware');

class CategoryRoutes {
  constructor(container) {
    this.container = container;
    this.router = express.Router();
    this.setupRoutes();
  }

  setupRoutes() {
    const categoryController = this.container.resolve('categoryController');
    const authMiddleware = this.container.resolve('authMiddleware');

    // Public routes (no authentication required)

    // Get all categories with filtering and pagination
    this.router.get('/',
      ErrorMiddleware.asyncHandler(categoryController.getAllCategories.bind(categoryController))
    );

    // Get category by ID
    this.router.get('/:id',
      ErrorMiddleware.asyncHandler(categoryController.getCategoryById.bind(categoryController))
    );

    // Get category by slug
    this.router.get('/slug/:slug',
      ErrorMiddleware.asyncHandler(categoryController.getCategoryBySlug.bind(categoryController))
    );

    // Get active categories
    this.router.get('/active/list',
      ErrorMiddleware.asyncHandler(categoryController.getActiveCategories.bind(categoryController))
    );

    // Get categories by parent
    this.router.get('/parent/:parentId',
      ErrorMiddleware.asyncHandler(categoryController.getCategoriesByParent.bind(categoryController))
    );

    // Get root categories
    this.router.get('/root/list',
      ErrorMiddleware.asyncHandler(categoryController.getRootCategories.bind(categoryController))
    );

    // Get category tree
    this.router.get('/tree/structure',
      ErrorMiddleware.asyncHandler(categoryController.getCategoryTree.bind(categoryController))
    );

    // Get category hierarchy
    this.router.get('/:id/hierarchy',
      ErrorMiddleware.asyncHandler(categoryController.getCategoryHierarchy.bind(categoryController))
    );

    // Get featured categories
    this.router.get('/featured/list',
      ErrorMiddleware.asyncHandler(categoryController.getFeaturedCategories.bind(categoryController))
    );

    // Get categories with product counts
    this.router.get('/with-counts/list',
      ErrorMiddleware.asyncHandler(categoryController.getCategoriesWithProductCounts.bind(categoryController))
    );

    // Get category statistics (public for transparency)
    this.router.get('/:id/statistics',
      ErrorMiddleware.asyncHandler(categoryController.getCategoryStatistics.bind(categoryController))
    );

    // Search categories
    this.router.get('/search/query',
      ErrorMiddleware.asyncHandler(categoryController.searchCategories.bind(categoryController))
    );

    // Check slug availability (public for form validation)
    this.router.get('/slug/:slug/check',
      ErrorMiddleware.asyncHandler(categoryController.checkSlugAvailability.bind(categoryController))
    );

    // Admin routes (admin only)

    // Create new category
    this.router.post('/',
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(categoryController.createCategory.bind(categoryController))
    );

    // Update category
    this.router.put('/:id',
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(categoryController.updateCategory.bind(categoryController))
    );

    // Delete category
    this.router.delete('/:id',
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(categoryController.deleteCategory.bind(categoryController))
    );

    // Update category status
    this.router.put('/:id/status',
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(categoryController.updateCategoryStatus.bind(categoryController))
    );

    // Reorder categories
    this.router.post('/reorder',
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(categoryController.reorderCategories.bind(categoryController))
    );

    // Move category to new parent
    this.router.put('/:id/move',
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(categoryController.moveCategoryToParent.bind(categoryController))
    );

    // Bulk update categories
    this.router.post('/bulk-update',
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(categoryController.bulkUpdateCategories.bind(categoryController))
    );

    // Update all product counts
    this.router.post('/update-counts',
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(categoryController.updateAllProductCounts.bind(categoryController))
    );
  }

  getRouter() {
    return this.router;
  }
}

module.exports = CategoryRoutes;
