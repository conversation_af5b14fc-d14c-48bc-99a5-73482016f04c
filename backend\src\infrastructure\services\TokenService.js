/**
 * Token Service
 * Infrastructure service for JWT token operations
 */

const jwt = require('jsonwebtoken');
const crypto = require('crypto');
const { JWT } = require('../../shared/constants');

class TokenService {
  constructor() {
    this.secretKey = process.env.JWT_SECRET || 'your-secret-key';
    this.refreshSecretKey = process.env.JWT_REFRESH_SECRET || 'your-refresh-secret-key';
  }

  /**
   * Generate access token
   * @param {Object} payload - Token payload
   * @param {string} expiresIn - Token expiry
   * @returns {string} - JWT token
   */
  generateAccessToken(payload, expiresIn = JWT.ACCESS_TOKEN_EXPIRY) {
    try {
      return jwt.sign(
        {
          ...payload,
          type: 'access',
          iat: Math.floor(Date.now() / 1000)
        },
        this.secretKey,
        {
          expiresIn,
          algorithm: JWT.ALGORITHM,
          issuer: 'robinhoot-api',
          audience: 'robinhoot-client'
        }
      );
    } catch (error) {
      throw new Error(`Access token generation failed: ${error.message}`);
    }
  }

  /**
   * Generate refresh token
   * @param {Object} payload - Token payload
   * @param {string} expiresIn - Token expiry
   * @returns {string} - JWT refresh token
   */
  generateRefreshToken(payload, expiresIn = JWT.REFRESH_TOKEN_EXPIRY) {
    try {
      return jwt.sign(
        {
          ...payload,
          type: 'refresh',
          iat: Math.floor(Date.now() / 1000)
        },
        this.refreshSecretKey,
        {
          expiresIn,
          algorithm: JWT.ALGORITHM,
          issuer: 'robinhoot-api',
          audience: 'robinhoot-client'
        }
      );
    } catch (error) {
      throw new Error(`Refresh token generation failed: ${error.message}`);
    }
  }

  /**
   * Verify access token
   * @param {string} token - JWT token
   * @returns {Object} - Decoded token payload
   */
  verifyAccessToken(token) {
    try {
      const decoded = jwt.verify(token, this.secretKey, {
        algorithms: [JWT.ALGORITHM],
        issuer: 'robinhoot-api',
        audience: 'robinhoot-client'
      });

      if (decoded.type !== 'access') {
        throw new Error('Invalid token type');
      }

      return decoded;
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        throw new Error('Token has expired');
      } else if (error.name === 'JsonWebTokenError') {
        throw new Error('Invalid token');
      } else {
        throw new Error(`Token verification failed: ${error.message}`);
      }
    }
  }

  /**
   * Verify refresh token
   * @param {string} token - JWT refresh token
   * @returns {Object} - Decoded token payload
   */
  verifyRefreshToken(token) {
    try {
      const decoded = jwt.verify(token, this.refreshSecretKey, {
        algorithms: [JWT.ALGORITHM],
        issuer: 'robinhoot-api',
        audience: 'robinhoot-client'
      });

      if (decoded.type !== 'refresh') {
        throw new Error('Invalid token type');
      }

      return decoded;
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        throw new Error('Refresh token has expired');
      } else if (error.name === 'JsonWebTokenError') {
        throw new Error('Invalid refresh token');
      } else {
        throw new Error(`Refresh token verification failed: ${error.message}`);
      }
    }
  }

  /**
   * Decode token without verification
   * @param {string} token - JWT token
   * @returns {Object} - Decoded token
   */
  decodeToken(token) {
    try {
      return jwt.decode(token, { complete: true });
    } catch (error) {
      throw new Error(`Token decoding failed: ${error.message}`);
    }
  }

  /**
   * Generate password reset token
   * @param {string} userId - User ID
   * @returns {string} - Reset token
   */
  generatePasswordResetToken(userId) {
    try {
      return jwt.sign(
        {
          userId,
          type: 'password_reset',
          iat: Math.floor(Date.now() / 1000)
        },
        this.secretKey,
        {
          expiresIn: '1h', // 1 hour expiry for password reset
          algorithm: JWT.ALGORITHM,
          issuer: 'robinhoot-api'
        }
      );
    } catch (error) {
      throw new Error(`Password reset token generation failed: ${error.message}`);
    }
  }

  /**
   * Verify password reset token
   * @param {string} token - Reset token
   * @returns {string} - User ID
   */
  verifyPasswordResetToken(token) {
    try {
      const decoded = jwt.verify(token, this.secretKey, {
        algorithms: [JWT.ALGORITHM],
        issuer: 'robinhoot-api'
      });

      if (decoded.type !== 'password_reset') {
        throw new Error('Invalid token type');
      }

      return decoded.userId;
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        throw new Error('Reset token has expired');
      } else if (error.name === 'JsonWebTokenError') {
        throw new Error('Invalid reset token');
      } else {
        throw new Error(`Reset token verification failed: ${error.message}`);
      }
    }
  }

  /**
   * Generate email verification token
   * @param {string} email - User email
   * @returns {string} - Verification token
   */
  generateEmailVerificationToken(email) {
    try {
      return jwt.sign(
        {
          email,
          type: 'email_verification',
          iat: Math.floor(Date.now() / 1000)
        },
        this.secretKey,
        {
          expiresIn: '24h', // 24 hours expiry for email verification
          algorithm: JWT.ALGORITHM,
          issuer: 'robinhoot-api'
        }
      );
    } catch (error) {
      throw new Error(`Email verification token generation failed: ${error.message}`);
    }
  }

  /**
   * Verify email verification token
   * @param {string} token - Verification token
   * @param {string} email - Expected email
   * @returns {boolean} - Verification result
   */
  verifyEmailToken(token, email) {
    try {
      const decoded = jwt.verify(token, this.secretKey, {
        algorithms: [JWT.ALGORITHM],
        issuer: 'robinhoot-api'
      });

      if (decoded.type !== 'email_verification') {
        return false;
      }

      return decoded.email === email;
    } catch (error) {
      return false;
    }
  }

  /**
   * Generate random token
   * @param {number} length - Token length
   * @returns {string} - Random token
   */
  generateRandomToken(length = 32) {
    return crypto.randomBytes(length).toString('hex');
  }

  /**
   * Hash token
   * @param {string} token - Token to hash
   * @returns {string} - Hashed token
   */
  hashToken(token) {
    return crypto.createHash('sha256').update(token).digest('hex');
  }

  /**
   * Get token expiry date
   * @param {string} token - JWT token
   * @returns {Date|null} - Expiry date
   */
  getTokenExpiry(token) {
    try {
      const decoded = this.decodeToken(token);
      if (decoded.payload.exp) {
        return new Date(decoded.payload.exp * 1000);
      }
      return null;
    } catch (error) {
      return null;
    }
  }

  /**
   * Check if token is expired
   * @param {string} token - JWT token
   * @returns {boolean} - Whether token is expired
   */
  isTokenExpired(token) {
    try {
      const expiry = this.getTokenExpiry(token);
      if (!expiry) return false;
      return expiry < new Date();
    } catch (error) {
      return true;
    }
  }

  /**
   * Refresh access token using refresh token
   * @param {string} refreshToken - Refresh token
   * @returns {Object} - New tokens
   */
  refreshAccessToken(refreshToken) {
    try {
      const decoded = this.verifyRefreshToken(refreshToken);
      
      // Generate new access token
      const newAccessToken = this.generateAccessToken({
        id: decoded.id,
        email: decoded.email,
        username: decoded.username,
        role: decoded.role
      });

      // Optionally generate new refresh token
      const newRefreshToken = this.generateRefreshToken({
        id: decoded.id,
        email: decoded.email,
        username: decoded.username,
        role: decoded.role
      });

      return {
        accessToken: newAccessToken,
        refreshToken: newRefreshToken
      };
    } catch (error) {
      throw new Error(`Token refresh failed: ${error.message}`);
    }
  }
}

module.exports = TokenService;
