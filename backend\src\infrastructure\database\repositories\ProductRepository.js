/**
 * Product Repository Implementation
 * Implements IProductRepository interface
 */

const IProductRepository = require('../../../core/interfaces/IProductRepository');
const ProductModel = require('../models/Product');
const Product = require('../../../core/entities/Product');
const { PAGINATION } = require('../../../shared/constants');
const { DatabaseException } = require('../../../core/exceptions');
const { ProductStatus } = require('../../../shared/enums');

class ProductRepository extends IProductRepository {
  constructor() {
    super();
    this.model = ProductModel;
  }

  /**
   * Create a new product
   */
  async create(productData) {
    try {
      // Handle categoryId field - remove if null or invalid
      const cleanedProductData = { ...productData };

      if (cleanedProductData.categoryId === null ||
          cleanedProductData.categoryId === '' ||
          cleanedProductData.categoryId === undefined) {
        delete cleanedProductData.categoryId;
      } else if (cleanedProductData.categoryId && typeof cleanedProductData.categoryId === 'string') {
        // Check if it's a valid ObjectId format
        const mongoose = require('mongoose');
        if (!mongoose.Types.ObjectId.isValid(cleanedProductData.categoryId)) {
          console.warn('🔥 ProductRepository.create - Invalid categoryId format:', cleanedProductData.categoryId);
          delete cleanedProductData.categoryId;
        }
      }

      console.log('🔥 ProductRepository.create - Original data:', productData);
      console.log('🔥 ProductRepository.create - Cleaned data:', cleanedProductData);

      const product = new this.model(cleanedProductData);
      const savedProduct = await product.save();

      // Get the saved product object and ensure _id is included
      const savedProductObj = savedProduct.toObject();

      // Return the saved product without populate to avoid schema registration issues
      return Product.fromPersistence(savedProductObj);
    } catch (error) {
      console.error('❌ ProductRepository.create error:', error);
      throw new DatabaseException(`Failed to create product: ${error.message}`);
    }
  }

  /**
   * Find product by ID
   */
  async findById(id) {
    try {
      const product = await this.model.findById(id)
        .populate('categoryId', 'name slug')
        .populate('sellerId', 'name username email')
        .lean();

      return product ? Product.fromPersistence(product) : null;
    } catch (error) {
      throw new DatabaseException(`Failed to find product: ${error.message}`);
    }
  }

  /**
   * Find all products
   */
  async findAll(options = {}) {
    try {
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        status,
        categoryId,
        sellerId,
        featured,
        inStock,
        minPrice,
        maxPrice,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = options;

      const query = {};

      if (status) query.status = status;
      if (categoryId) query.categoryId = categoryId;
      if (sellerId) query.sellerId = sellerId;
      if (featured !== undefined) query.featured = featured;
      if (inStock) query.stock = { $gt: 0 };

      if (minPrice !== undefined || maxPrice !== undefined) {
        query.price = {};
        if (minPrice !== undefined) query.price.$gte = minPrice;
        if (maxPrice !== undefined) query.price.$lte = maxPrice;
      }

      const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };
      const skip = (page - 1) * limit;

      const [products, total] = await Promise.all([
        this.model.find(query)
          .populate('categoryId', 'name slug')
          .populate('sellerId', 'name username')
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .lean(),
        this.model.countDocuments(query)
      ]);

      return {
        products: products.map(product => Product.fromPersistence(product)),
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw new DatabaseException(`Failed to find products: ${error.message}`);
    }
  }

  /**
   * Find products by category
   */
  async findByCategory(categoryId, options = {}) {
    try {
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = options;

      const query = {
        categoryId,
        status: ProductStatus.ACTIVE
      };

      const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };
      const skip = (page - 1) * limit;

      const [products, total] = await Promise.all([
        this.model.find(query)
          .populate('sellerId', 'name username')
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .lean(),
        this.model.countDocuments(query)
      ]);

      return {
        products: products.map(product => Product.fromPersistence(product)),
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw new DatabaseException(`Failed to find products by category: ${error.message}`);
    }
  }

  /**
   * Find products by seller
   */
  async findBySeller(sellerId, options = {}) {
    try {
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        status,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = options;

      const query = { sellerId };
      if (status) query.status = status;

      const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };
      const skip = (page - 1) * limit;

      const [products, total] = await Promise.all([
        this.model.find(query)
          .populate('categoryId', 'name slug')
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .lean(),
        this.model.countDocuments(query)
      ]);

      return {
        products: products.map(product => Product.fromPersistence(product)),
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw new DatabaseException(`Failed to find products by seller: ${error.message}`);
    }
  }

  /**
   * Search products
   */
  async search(query, options = {}) {
    try {
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        status = ProductStatus.ACTIVE,
        categoryId,
        minPrice,
        maxPrice,
        sortBy = 'relevance',
        sortOrder = 'desc'
      } = options;

      const searchQuery = {
        $text: { $search: query },
        status
      };

      if (categoryId) searchQuery.categoryId = categoryId;
      if (minPrice !== undefined || maxPrice !== undefined) {
        searchQuery.price = {};
        if (minPrice !== undefined) searchQuery.price.$gte = minPrice;
        if (maxPrice !== undefined) searchQuery.price.$lte = maxPrice;
      }

      let sort = {};
      if (sortBy === 'relevance') {
        sort = { score: { $meta: 'textScore' } };
      } else {
        sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };
      }

      const skip = (page - 1) * limit;

      const [products, total] = await Promise.all([
        this.model.find(searchQuery, { score: { $meta: 'textScore' } })
          .populate('categoryId', 'name slug')
          .populate('sellerId', 'name username')
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .lean(),
        this.model.countDocuments(searchQuery)
      ]);

      return {
        products: products.map(product => Product.fromPersistence(product)),
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw new DatabaseException(`Failed to search products: ${error.message}`);
    }
  }

  /**
   * Find featured products
   */
  async findFeatured(options = {}) {
    try {
      const { limit = 10 } = options;

      const products = await this.model.findFeatured(limit)
        .populate('categoryId', 'name slug')
        .populate('sellerId', 'name username')
        .lean();

      return products.map(product => Product.fromPersistence(product));
    } catch (error) {
      throw new DatabaseException(`Failed to find featured products: ${error.message}`);
    }
  }

  /**
   * Find products by status
   */
  async findByStatus(status, options = {}) {
    try {
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = options;

      const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };
      const skip = (page - 1) * limit;

      const [products, total] = await Promise.all([
        this.model.find({ status })
          .populate('categoryId', 'name slug')
          .populate('sellerId', 'name username')
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .lean(),
        this.model.countDocuments({ status })
      ]);

      return {
        products: products.map(product => Product.fromPersistence(product)),
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw new DatabaseException(`Failed to find products by status: ${error.message}`);
    }
  }

  /**
   * Update product
   */
  async update(id, updateData) {
    try {
      // Handle categoryId field - remove if null or invalid
      const cleanedUpdateData = { ...updateData };

      if (cleanedUpdateData.categoryId === null ||
          cleanedUpdateData.categoryId === '' ||
          cleanedUpdateData.categoryId === undefined) {
        delete cleanedUpdateData.categoryId;
      } else if (cleanedUpdateData.categoryId && typeof cleanedUpdateData.categoryId === 'string') {
        // Check if it's a valid ObjectId format
        const mongoose = require('mongoose');
        if (!mongoose.Types.ObjectId.isValid(cleanedUpdateData.categoryId)) {
          console.warn('🔥 ProductRepository.update - Invalid categoryId format:', cleanedUpdateData.categoryId);
          delete cleanedUpdateData.categoryId;
        }
      }

      console.log('🔥 ProductRepository.update - Original data:', updateData);
      console.log('🔥 ProductRepository.update - Cleaned data:', cleanedUpdateData);
      let productBeforeUpdate;
      try {
        productBeforeUpdate = await this.model.findById(id).lean();
      } catch (e) {
        console.log("Error fetching product before update", e);
      }
      console.log('🔥 ProductRepository.update - Product before update:', productBeforeUpdate?.categoryId);

      const product = await this.model.findByIdAndUpdate(
        id,
        { ...cleanedUpdateData, updatedAt: new Date() },
        { new: true, runValidators: true }
      )
      .populate('categoryId', 'name slug')
      .populate('sellerId', 'name username email')
      .lean();

      return product ? Product.fromPersistence(product) : null;
    } catch (error) {
      console.error('❌ ProductRepository.update error:', error);
      throw new DatabaseException(`Failed to update product: ${error.message}`);
    }
  }

  /**
   * Delete product
   */
  async delete(id) {
    try {
      console.log('🔥 ProductRepository.delete - Deleting product ID:', id);
      const result = await this.model.findByIdAndDelete(id);
      console.log('🔥 ProductRepository.delete - Delete result:', result);
      console.log('🔥 ProductRepository.delete - Result exists:', !!result);
      return !!result;
    } catch (error) {
      console.error('❌ ProductRepository.delete error:', error);
      throw new DatabaseException(`Failed to delete product: ${error.message}`);
    }
  }

  /**
   * Update product status
   */
  async updateStatus(id, status) {
    try {
      const product = await this.model.findByIdAndUpdate(
        id,
        { status, updatedAt: new Date() },
        { new: true, runValidators: true }
      )
      .populate('categoryId', 'name slug')
      .populate('sellerId', 'name username email')
      .lean();

      return product ? Product.fromPersistence(product) : null;
    } catch (error) {
      throw new DatabaseException(`Failed to update product status: ${error.message}`);
    }
  }

  /**
   * Increment view count
   */
  async incrementViewCount(id) {
    try {
      const product = await this.model.findByIdAndUpdate(
        id,
        { $inc: { viewCount: 1 } },
        { new: true }
      ).lean();

      return product ? Product.fromPersistence(product) : null;
    } catch (error) {
      throw new DatabaseException(`Failed to increment view count: ${error.message}`);
    }
  }

  /**
   * Get product statistics
   */
  async getStatistics(id) {
    try {
      const product = await this.model.findById(id).lean();
      if (!product) return null;

      return {
        viewCount: product.viewCount,
        likeCount: product.likeCount,
        shareCount: product.shareCount,
        salesCount: product.salesCount,
        reviewCount: product.reviewCount,
        averageRating: product.averageRating,
        stock: product.stock,
        isLowStock: product.stock <= product.lowStockThreshold,
        isOutOfStock: product.stock === 0
      };
    } catch (error) {
      throw new DatabaseException(`Failed to get product statistics: ${error.message}`);
    }
  }

  /**
   * Get products count by status
   */
  async getCountByStatus(sellerId = null) {
    try {
      const matchStage = sellerId ? { sellerId } : {};

      const counts = await this.model.aggregate([
        { $match: matchStage },
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 }
          }
        }
      ]);

      const result = {};
      counts.forEach(item => {
        result[item._id] = item.count;
      });

      return result;
    } catch (error) {
      throw new DatabaseException(`Failed to get product counts: ${error.message}`);
    }
  }

  /**
   * Get products count by category
   */
  async getCountByCategory() {
    try {
      const counts = await this.model.aggregate([
        { $match: { status: ProductStatus.ACTIVE } },
        {
          $group: {
            _id: '$categoryId',
            count: { $sum: 1 }
          }
        },
        {
          $lookup: {
            from: 'categories',
            localField: '_id',
            foreignField: '_id',
            as: 'category'
          }
        },
        { $unwind: '$category' },
        {
          $project: {
            categoryId: '$_id',
            categoryName: '$category.name',
            count: 1
          }
        }
      ]);

      return counts;
    } catch (error) {
      throw new DatabaseException(`Failed to get category counts: ${error.message}`);
    }
  }

  /**
   * Find related products
   */
  async findRelated(productId, limit = 5) {
    try {
      const product = await this.model.findById(productId).lean();
      if (!product) return [];

      const relatedProducts = await this.model.find({
        _id: { $ne: productId },
        categoryId: product.categoryId,
        status: ProductStatus.ACTIVE
      })
      .limit(limit)
      .populate('categoryId', 'name slug')
      .lean();

      return relatedProducts.map(product => Product.fromPersistence(product));
    } catch (error) {
      throw new DatabaseException(`Failed to find related products: ${error.message}`);
    }
  }

  /**
   * Update product inventory
   */
  async updateInventory(id, quantity) {
    try {
      const product = await this.model.findByIdAndUpdate(
        id,
        { stock: Math.max(0, quantity), updatedAt: new Date() },
        { new: true, runValidators: true }
      ).lean();

      return product ? Product.fromPersistence(product) : null;
    } catch (error) {
      throw new DatabaseException(`Failed to update inventory: ${error.message}`);
    }
  }

  /**
   * Check product availability
   */
  async checkAvailability(id, quantity = 1) {
    try {
      const product = await this.model.findById(id).lean();
      if (!product) return false;

      return product.status === ProductStatus.ACTIVE && product.stock >= quantity;
    } catch (error) {
      throw new DatabaseException(`Failed to check availability: ${error.message}`);
    }
  }

  /**
   * Get low stock products
   */
  async findLowStock(threshold = 10, options = {}) {
    try {
      const products = await this.model.findLowStock(threshold)
        .populate('categoryId', 'name slug')
        .populate('sellerId', 'name username')
        .lean();

      return products.map(product => Product.fromPersistence(product));
    } catch (error) {
      throw new DatabaseException(`Failed to find low stock products: ${error.message}`);
    }
  }

  /**
   * Bulk update products
   */
  async bulkUpdate(updates) {
    try {
      const bulkOps = updates.map(update => ({
        updateOne: {
          filter: { _id: update.id },
          update: { ...update.data, updatedAt: new Date() }
        }
      }));

      const result = await this.model.bulkWrite(bulkOps);
      return {
        modifiedCount: result.modifiedCount,
        matchedCount: result.matchedCount
      };
    } catch (error) {
      throw new DatabaseException(`Failed to bulk update products: ${error.message}`);
    }
  }

  // Additional methods from interface

  async findInStock(options = {}) {
    return this.findAll({ ...options, inStock: true });
  }

  async findByPriceRange(minPrice, maxPrice, options = {}) {
    return this.findAll({ ...options, minPrice, maxPrice });
  }

  async findByTag(tag, options = {}) {
    try {
      const query = {
        tags: tag,
        status: ProductStatus.ACTIVE
      };

      return this.findAll({ ...options, customQuery: query });
    } catch (error) {
      throw new DatabaseException(`Failed to find products by tag: ${error.message}`);
    }
  }

  async findSimilar(productId, limit = 5) {
    return this.findRelated(productId, limit);
  }

  async count(filters = {}) {
    try {
      return await this.model.countDocuments(filters);
    } catch (error) {
      throw new DatabaseException(`Failed to count products: ${error.message}`);
    }
  }

  async updateStock(id, stock) {
    return this.updateInventory(id, stock);
  }

  async updatePrice(id, price) {
    return this.update(id, { price });
  }

  async updateFeaturedStatus(id, featured) {
    return this.update(id, { featured });
  }

  async bulkUpdateStatus(ids, status) {
    try {
      const result = await this.model.updateMany(
        { _id: { $in: ids } },
        { status, updatedAt: new Date() }
      );
      return result.modifiedCount;
    } catch (error) {
      throw new DatabaseException(`Failed to bulk update status: ${error.message}`);
    }
  }

  async bulkUpdateStock(updates) {
    try {
      const bulkOps = updates.map(update => ({
        updateOne: {
          filter: { _id: update.id },
          update: { stock: update.stock, updatedAt: new Date() }
        }
      }));

      const result = await this.model.bulkWrite(bulkOps);
      return result.modifiedCount;
    } catch (error) {
      throw new DatabaseException(`Failed to bulk update stock: ${error.message}`);
    }
  }

  async findBestSellers(options = {}) {
    try {
      const { limit = 10 } = options;
      const products = await this.model.findBestSellers(limit)
        .populate('categoryId', 'name slug')
        .populate('sellerId', 'name username')
        .lean();

      return products.map(product => Product.fromPersistence(product));
    } catch (error) {
      throw new DatabaseException(`Failed to find best sellers: ${error.message}`);
    }
  }

  async findNew(days = 7, options = {}) {
    try {
      const { limit = 10 } = options;
      const products = await this.model.findNew(days, limit)
        .populate('categoryId', 'name slug')
        .populate('sellerId', 'name username')
        .lean();

      return products.map(product => Product.fromPersistence(product));
    } catch (error) {
      throw new DatabaseException(`Failed to find new products: ${error.message}`);
    }
  }

  async findDiscounted(options = {}) {
    try {
      return this.findAll({ ...options, customQuery: { discountPercentage: { $gt: 0 } } });
    } catch (error) {
      throw new DatabaseException(`Failed to find discounted products: ${error.message}`);
    }
  }

  async findWithDynamicPricing(options = {}) {
    try {
      return this.findAll({ ...options, customQuery: { 'dynamicPricing.enabled': true } });
    } catch (error) {
      throw new DatabaseException(`Failed to find dynamic pricing products: ${error.message}`);
    }
  }
}

module.exports = ProductRepository;
