<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="59" failures="25" errors="0" time="12.49">
  <testsuite name="OrderService" errors="0" failures="4" skipped="0" timestamp="2025-05-26T22:43:56" time="7.323" tests="24">
    <testcase classname="OrderService › createOrder" name="should create order successfully" time="0.013">
      <failure>BusinessException: Failed to create order: user.isActive is not a function
    at OrderService.createOrder (C:\Users\<USER>\Desktop\robinhood_database-master\backend\src\business\services\OrderService.js:115:13)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\robinhood_database-master\backend\tests\unit\services\OrderService.test.js:77:22)</failure>
    </testcase>
    <testcase classname="OrderService › createOrder" name="should throw ValidationException if user not found" time="0.011">
    </testcase>
    <testcase classname="OrderService › createOrder" name="should throw AuthorizationException if user is inactive" time="0.035">
      <failure>Error: expect(received).rejects.toThrow(expected)

Expected constructor: AuthorizationException
Received constructor: BusinessException

Received message: &quot;Failed to create order: user.isActive is not a function&quot;

      113 |         throw error;
      114 |       }
    &gt; 115 |       throw new BusinessException(`Failed to create order: ${error.message}`);
          |             ^
      116 |     }
      117 |   }
      118 |

      at OrderService.createOrder (src/business/services/OrderService.js:115:13)
      at Object.&lt;anonymous&gt; (tests/unit/services/OrderService.test.js:109:7)
    at Object.toThrow (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\expect\build\index.js:218:22)
    at Object.toThrow (C:\Users\<USER>\Desktop\robinhood_database-master\backend\tests\unit\services\OrderService.test.js:109:73)
    at Promise.then.completed (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="OrderService › createOrder" name="should throw ValidationException if product not found" time="0.016">
      <failure>Error: expect(received).rejects.toThrow(expected)

Expected constructor: ValidationException
Received constructor: BusinessException

Received message: &quot;Failed to create order: user.isActive is not a function&quot;

      113 |         throw error;
      114 |       }
    &gt; 115 |       throw new BusinessException(`Failed to create order: ${error.message}`);
          |             ^
      116 |     }
      117 |   }
      118 |

      at OrderService.createOrder (src/business/services/OrderService.js:115:13)
      at Object.&lt;anonymous&gt; (tests/unit/services/OrderService.test.js:123:7)
    at Object.toThrow (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\expect\build\index.js:218:22)
    at Object.toThrow (C:\Users\<USER>\Desktop\robinhood_database-master\backend\tests\unit\services\OrderService.test.js:123:73)
    at Promise.then.completed (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="OrderService › createOrder" name="should throw ValidationException if insufficient stock" time="0.022">
      <failure>Error: expect(received).rejects.toThrow(expected)

Expected constructor: ValidationException
Received constructor: BusinessException

Received message: &quot;Failed to create order: user.isActive is not a function&quot;

      113 |         throw error;
      114 |       }
    &gt; 115 |       throw new BusinessException(`Failed to create order: ${error.message}`);
          |             ^
      116 |     }
      117 |   }
      118 |

      at OrderService.createOrder (src/business/services/OrderService.js:115:13)
      at Object.&lt;anonymous&gt; (tests/unit/services/OrderService.test.js:143:7)
    at Object.toThrow (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\expect\build\index.js:218:22)
    at Object.toThrow (C:\Users\<USER>\Desktop\robinhood_database-master\backend\tests\unit\services\OrderService.test.js:143:73)
    at Promise.then.completed (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="OrderService › updateOrder" name="should update order successfully by owner" time="0.009">
    </testcase>
    <testcase classname="OrderService › updateOrder" name="should allow admin to update any order" time="0.006">
    </testcase>
    <testcase classname="OrderService › updateOrder" name="should throw AuthorizationException if non-owner tries to update" time="0.006">
    </testcase>
    <testcase classname="OrderService › updateOrder" name="should throw ValidationException if order is not editable" time="0.006">
    </testcase>
    <testcase classname="OrderService › cancelOrder" name="should cancel order successfully by owner" time="0.009">
    </testcase>
    <testcase classname="OrderService › cancelOrder" name="should allow admin to cancel any order" time="0.005">
    </testcase>
    <testcase classname="OrderService › cancelOrder" name="should throw ValidationException if order cannot be cancelled" time="0.014">
    </testcase>
    <testcase classname="OrderService › updateOrderStatus" name="should update order status successfully by admin" time="0.014">
    </testcase>
    <testcase classname="OrderService › updateOrderStatus" name="should allow seller to update orders containing their products" time="0.005">
    </testcase>
    <testcase classname="OrderService › updateOrderStatus" name="should throw AuthorizationException if seller tries to update order without their products" time="0.006">
    </testcase>
    <testcase classname="OrderService › updatePaymentStatus" name="should update payment status successfully by admin" time="0.005">
    </testcase>
    <testcase classname="OrderService › updatePaymentStatus" name="should throw AuthorizationException if non-admin tries to update payment status" time="0.007">
    </testcase>
    <testcase classname="OrderService › getOrderById" name="should return order by ID" time="0.005">
    </testcase>
    <testcase classname="OrderService › getOrderById" name="should throw ValidationException if order not found" time="0.003">
    </testcase>
    <testcase classname="OrderService › getUserOrders" name="should return user orders" time="0.008">
    </testcase>
    <testcase classname="OrderService › getSellerOrders" name="should return seller orders" time="0.003">
    </testcase>
    <testcase classname="OrderService › searchOrders" name="should search orders successfully" time="0.003">
    </testcase>
    <testcase classname="OrderService › getOrderCounts" name="should return order counts by status" time="0.004">
    </testcase>
    <testcase classname="OrderService › getRevenueStatistics" name="should return revenue statistics" time="0.004">
    </testcase>
  </testsuite>
  <testsuite name="UserService" errors="0" failures="19" skipped="0" timestamp="2025-05-26T22:44:03" time="2.586" tests="20">
    <testcase classname="UserService › registerUser" name="should register a new user successfully" time="0.009">
      <failure>TypeError: userService.registerUser is not a function
    at Object.registerUser (C:\Users\<USER>\Desktop\robinhood_database-master\backend\tests\unit\services\UserService.test.js:71:40)
    at Promise.then.completed (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="UserService › registerUser" name="should throw ValidationException if email already exists" time="0.003">
      <failure>TypeError: userService.registerUser is not a function
    at Object.registerUser (C:\Users\<USER>\Desktop\robinhood_database-master\backend\tests\unit\services\UserService.test.js:89:32)
    at Promise.then.completed (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="UserService › registerUser" name="should throw ValidationException if username already exists" time="0.004">
      <failure>TypeError: userService.registerUser is not a function
    at Object.registerUser (C:\Users\<USER>\Desktop\robinhood_database-master\backend\tests\unit\services\UserService.test.js:102:32)
    at Promise.then.completed (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="UserService › authenticateUser" name="should authenticate user with valid credentials" time="0.006">
      <failure>UserNotActiveException: User account is not active
    at AuthenticateUserUseCase.checkUserPermissions (C:\Users\<USER>\Desktop\robinhood_database-master\backend\src\core\usecases\user\AuthenticateUserUseCase.js:117:13)
    at AuthenticateUserUseCase.checkUserPermissions [as execute] (C:\Users\<USER>\Desktop\robinhood_database-master\backend\src\core\usecases\user\AuthenticateUserUseCase.js:38:10)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at UserService.authenticateUser (C:\Users\<USER>\Desktop\robinhood_database-master\backend\src\business\services\UserService.js:47:12)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\robinhood_database-master\backend\tests\unit\services\UserService.test.js:122:22)</failure>
    </testcase>
    <testcase classname="UserService › authenticateUser" name="should throw ValidationException for invalid email" time="0.011">
      <failure>Error: expect(received).rejects.toThrow(expected)

Expected constructor: ValidationException
Received constructor: InvalidCredentialsException

Received message: &quot;Invalid email or password&quot;

      92 |
      93 |     if (!user) {
    &gt; 94 |       throw new InvalidCredentialsException();
         |             ^
      95 |     }
      96 |
      97 |     return user;

      at AuthenticateUserUseCase.findUser (src/core/usecases/user/AuthenticateUserUseCase.js:94:13)
      at AuthenticateUserUseCase.execute (src/core/usecases/user/AuthenticateUserUseCase.js:32:18)
      at UserService.authenticateUser (src/business/services/UserService.js:47:12)
      at Object.&lt;anonymous&gt; (tests/unit/services/UserService.test.js:145:7)
    at Object.toThrow (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\expect\build\index.js:218:22)
    at Object.toThrow (C:\Users\<USER>\Desktop\robinhood_database-master\backend\tests\unit\services\UserService.test.js:145:75)
    at Promise.then.completed (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="UserService › authenticateUser" name="should throw ValidationException for invalid password" time="0.008">
      <failure>Error: expect(received).rejects.toThrow(expected)

Expected constructor: ValidationException
Received constructor: InvalidCredentialsException

Received message: &quot;Invalid email or password&quot;

      105 |     
      106 |     if (!isPasswordValid) {
    &gt; 107 |       throw new InvalidCredentialsException();
          |             ^
      108 |     }
      109 |   }
      110 |

      at AuthenticateUserUseCase.verifyPassword (src/core/usecases/user/AuthenticateUserUseCase.js:107:13)
      at AuthenticateUserUseCase.execute (src/core/usecases/user/AuthenticateUserUseCase.js:35:5)
      at UserService.authenticateUser (src/business/services/UserService.js:47:12)
      at Object.&lt;anonymous&gt; (tests/unit/services/UserService.test.js:159:7)
    at Object.toThrow (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\expect\build\index.js:218:22)
    at Object.toThrow (C:\Users\<USER>\Desktop\robinhood_database-master\backend\tests\unit\services\UserService.test.js:159:75)
    at Promise.then.completed (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="UserService › authenticateUser" name="should throw AuthorizationException for inactive user" time="0.003">
    </testcase>
    <testcase classname="UserService › getUserById" name="should return user by ID" time="0.002">
      <failure>TypeError: user.toJSON is not a function
    at UserService.toJSON [as getUserById] (C:\Users\<USER>\Desktop\robinhood_database-master\backend\src\business\services\UserService.js:58:17)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\robinhood_database-master\backend\tests\unit\services\UserService.test.js:186:22)</failure>
    </testcase>
    <testcase classname="UserService › getUserById" name="should throw ValidationException if user not found" time="0.011">
      <failure>Error: expect(received).rejects.toThrow(expected)

Expected constructor: ValidationException
Received constructor: UserNotFoundException

Received message: &quot;User with id &apos;nonexistent&apos; not found&quot;

      54 |     const user = await this.userRepository.findById(id);
      55 |     if (!user) {
    &gt; 56 |       throw new UserNotFoundException(id);
         |             ^
      57 |     }
      58 |     return user.toJSON();
      59 |   }

      at UserService.getUserById (src/business/services/UserService.js:56:13)
      at Object.&lt;anonymous&gt; (tests/unit/services/UserService.test.js:200:7)
    at Object.toThrow (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\expect\build\index.js:218:22)
    at Object.toThrow (C:\Users\<USER>\Desktop\robinhood_database-master\backend\tests\unit\services\UserService.test.js:200:61)
    at Promise.then.completed (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="UserService › updateUser" name="should update user successfully" time="0.006">
      <failure>TypeError: userService.updateUser is not a function
    at Object.updateUser (C:\Users\<USER>\Desktop\robinhood_database-master\backend\tests\unit\services\UserService.test.js:217:40)
    at Promise.then.completed (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="UserService › updateUser" name="should allow admin to update any user" time="0.003">
      <failure>TypeError: userService.updateUser is not a function
    at Object.updateUser (C:\Users\<USER>\Desktop\robinhood_database-master\backend\tests\unit\services\UserService.test.js:240:40)
    at Promise.then.completed (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="UserService › updateUser" name="should throw AuthorizationException if non-admin tries to update another user" time="0.003">
      <failure>TypeError: userService.updateUser is not a function
    at Object.updateUser (C:\Users\<USER>\Desktop\robinhood_database-master\backend\tests\unit\services\UserService.test.js:259:32)
    at Promise.then.completed (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="UserService › deleteUser" name="should delete user successfully (admin)" time="0.005">
      <failure>TypeError: userService.deleteUser is not a function
    at Object.deleteUser (C:\Users\<USER>\Desktop\robinhood_database-master\backend\tests\unit\services\UserService.test.js:278:40)
    at Promise.then.completed (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="UserService › deleteUser" name="should throw AuthorizationException if non-admin tries to delete user" time="0.005">
      <failure>TypeError: userService.deleteUser is not a function
    at Object.deleteUser (C:\Users\<USER>\Desktop\robinhood_database-master\backend\tests\unit\services\UserService.test.js:298:32)
    at Promise.then.completed (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="UserService › changePassword" name="should change password successfully" time="0.002">
      <failure>TypeError: requestingUser.isAdmin is not a function
    at UserService.isAdmin [as checkUserUpdatePermission] (C:\Users\<USER>\Desktop\robinhood_database-master\backend\src\business\services\UserService.js:243:42)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at UserService.changePassword (C:\Users\<USER>\Desktop\robinhood_database-master\backend\src\business\services\UserService.js:85:5)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\robinhood_database-master\backend\tests\unit\services\UserService.test.js:318:22)</failure>
    </testcase>
    <testcase classname="UserService › changePassword" name="should throw ValidationException for incorrect current password" time="0.013">
      <failure>Error: expect(received).rejects.toThrow(expected)

Expected constructor: ValidationException
Received constructor: TypeError

Received message: &quot;requestingUser.isAdmin is not a function&quot;

      241 |     // Admins can update any user
      242 |     const requestingUser = await this.userRepository.findById(requestingUserId);
    &gt; 243 |     if (requestingUser &amp;&amp; requestingUser.isAdmin()) {
          |                                          ^
      244 |       return true;
      245 |     }
      246 |

      at UserService.isAdmin [as checkUserUpdatePermission] (src/business/services/UserService.js:243:42)
      at UserService.changePassword (src/business/services/UserService.js:85:5)
      at Object.&lt;anonymous&gt; (tests/unit/services/UserService.test.js:338:7)
    at Object.toThrow (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\expect\build\index.js:218:22)
    at Object.toThrow (C:\Users\<USER>\Desktop\robinhood_database-master\backend\tests\unit\services\UserService.test.js:338:94)
    at Promise.then.completed (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="UserService › updateUserPermissions" name="should update user permissions successfully (admin)" time="0.002">
      <failure>TypeError: admin.isAdmin is not a function
    at UserService.isAdmin [as updateUserPermissions] (C:\Users\<USER>\Desktop\robinhood_database-master\backend\src\business\services\UserService.js:176:26)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\robinhood_database-master\backend\tests\unit\services\UserService.test.js:359:22)</failure>
    </testcase>
    <testcase classname="UserService › updateUserPermissions" name="should throw AuthorizationException if non-admin tries to update permissions" time="0.01">
      <failure>Error: expect(received).rejects.toThrow(expected)

Expected constructor: AuthorizationException
Received constructor: TypeError

Received message: &quot;admin.isAdmin is not a function&quot;

      174 |   async updateUserPermissions(userId, permissions, adminId) {
      175 |     const admin = await this.userRepository.findById(adminId);
    &gt; 176 |     if (!admin || !admin.isAdmin()) {
          |                          ^
      177 |       throw new AuthorizationException(&apos;Only admins can update user permissions&apos;);
      178 |     }
      179 |

      at UserService.isAdmin [as updateUserPermissions] (src/business/services/UserService.js:176:26)
      at Object.&lt;anonymous&gt; (tests/unit/services/UserService.test.js:379:7)
    at Object.toThrow (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\expect\build\index.js:218:22)
    at Object.toThrow (C:\Users\<USER>\Desktop\robinhood_database-master\backend\tests\unit\services\UserService.test.js:379:95)
    at Promise.then.completed (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="UserService › getAllUsers" name="should return paginated users" time="0.003">
      <failure>TypeError: userService.getAllUsers is not a function
    at Object.getAllUsers (C:\Users\<USER>\Desktop\robinhood_database-master\backend\tests\unit\services\UserService.test.js:400:42)
    at Promise.then.completed (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\utils.js:298:28)
    at new Promise (&lt;anonymous&gt;)
    at callAsyncCircusFn (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\utils.js:231:10)
    at _callCircusTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:316:40)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at _runTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:252:3)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:126:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:121:9)
    at _runTestsForDescribeBlock (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:121:9)
    at run (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\run.js:71:3)
    at runAndTransformResultsToJestFormat (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapterInit.js:122:21)
    at jestAdapter (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-circus\build\legacy-code-todo-rewrite\jestAdapter.js:79:19)
    at runTestInternal (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-runner\build\runTest.js:367:16)
    at runTest (C:\Users\<USER>\Desktop\robinhood_database-master\backend\node_modules\jest-runner\build\runTest.js:444:34)</failure>
    </testcase>
    <testcase classname="UserService › searchUsers" name="should search users successfully" time="0.005">
      <failure>AuthorizationException: Only admins can search users
    at UserService.searchUsers (C:\Users\<USER>\Desktop\robinhood_database-master\backend\src\business\services\UserService.js:221:13)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)
    at Object.&lt;anonymous&gt; (C:\Users\<USER>\Desktop\robinhood_database-master\backend\tests\unit\services\UserService.test.js:422:24)</failure>
    </testcase>
  </testsuite>
  <testsuite name="ProductService" errors="0" failures="2" skipped="0" timestamp="2025-05-26T22:44:06" time="2.191" tests="15">
    <testcase classname="ProductService › createProduct" name="should create product successfully for seller" time="0.004">
    </testcase>
    <testcase classname="ProductService › createProduct" name="should throw AuthorizationException if user cannot sell" time="0.03">
    </testcase>
    <testcase classname="ProductService › createProduct" name="should throw ValidationException if category not found" time="0.003">
    </testcase>
    <testcase classname="ProductService › updateProduct" name="should update product successfully by owner" time="0.003">
    </testcase>
    <testcase classname="ProductService › updateProduct" name="should allow admin to update any product" time="0.003">
    </testcase>
    <testcase classname="ProductService › updateProduct" name="should throw AuthorizationException if non-owner tries to update" time="0.003">
    </testcase>
    <testcase classname="ProductService › deleteProduct" name="should delete product successfully by owner" time="0.002">
    </testcase>
    <testcase classname="ProductService › deleteProduct" name="should allow admin to delete any product" time="0.002">
    </testcase>
    <testcase classname="ProductService › approveProduct" name="should approve product successfully by admin" time="0.027">
      <failure>Error: expect(jest.fn()).toHaveBeenCalledWith(...expected)

Expected: &quot;456&quot;, &quot;active&quot;

Number of calls: 0
    at Object.toHaveBeenCalledWith (C:\Users\<USER>\Desktop\robinhood_database-master\backend\tests\unit\services\ProductService.test.js:238:50)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
    <testcase classname="ProductService › approveProduct" name="should throw AuthorizationException if non-admin tries to approve" time="0.003">
    </testcase>
    <testcase classname="ProductService › getProductById" name="should return product by ID" time="0.002">
    </testcase>
    <testcase classname="ProductService › getProductById" name="should throw ValidationException if product not found" time="0.003">
    </testcase>
    <testcase classname="ProductService › getAllProducts" name="should return paginated products" time="0.004">
    </testcase>
    <testcase classname="ProductService › searchProducts" name="should search products successfully" time="0.003">
    </testcase>
    <testcase classname="ProductService › incrementViewCount" name="should increment view count successfully" time="0.004">
      <failure>Error: expect(received).toEqual(expected) // deep equality

Expected: {&quot;categoryId&quot;: null, &quot;description&quot;: &quot;Test product description&quot;, &quot;id&quot;: &quot;456&quot;, &quot;images&quot;: [&quot;test-image.jpg&quot;], &quot;name&quot;: &quot;Test Product&quot;, &quot;originalPrice&quot;: 129.99, &quot;price&quot;: 99.99, &quot;sellerId&quot;: null, &quot;sku&quot;: &quot;TEST-001&quot;, &quot;specifications&quot;: {&quot;dimensions&quot;: {&quot;height&quot;: 6, &quot;length&quot;: 10, &quot;width&quot;: 8}, &quot;weight&quot;: 1.5}, &quot;status&quot;: &quot;active&quot;, &quot;stock&quot;: 10, &quot;viewCount&quot;: 6}
Received: undefined
    at Object.toEqual (C:\Users\<USER>\Desktop\robinhood_database-master\backend\tests\unit\services\ProductService.test.js:347:22)
    at processTicksAndRejections (node:internal/process/task_queues:105:5)</failure>
    </testcase>
  </testsuite>
</testsuites>