/**
 * User Mongoose Model
 * Infrastructure layer - Database model
 */

const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const crypto = require('crypto');
const { UserRole, UserStatus, ApprovalStatus } = require('../../../shared/enums');
const { VALIDATION, SECURITY } = require('../../../shared/constants');

const userSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: [true, 'Name is required'],
      trim: true,
      minlength: [VALIDATION.NAME_MIN_LENGTH, `Name must be at least ${VALIDATION.NAME_MIN_LENGTH} characters`],
      maxlength: [VALIDATION.NAME_MAX_LENGTH, `Name cannot exceed ${VALIDATION.NAME_MAX_LENGTH} characters`]
    },
    email: {
      type: String,
      required: [true, 'Email is required'],
      unique: true,
      trim: true,
      lowercase: true,
      match: [
        /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
        'Please enter a valid email address'
      ]
    },
    username: {
      type: String,
      required: [true, 'Username is required'],
      unique: true,
      trim: true,
      minlength: [VALIDATION.USERNAME_MIN_LENGTH, `Username must be at least ${VALIDATION.USERNAME_MIN_LENGTH} characters`],
      maxlength: [VALIDATION.USERNAME_MAX_LENGTH, `Username cannot exceed ${VALIDATION.USERNAME_MAX_LENGTH} characters`]
    },
    password: {
      type: String,
      required: [true, 'Password is required'],
      minlength: [VALIDATION.PASSWORD_MIN_LENGTH, `Password must be at least ${VALIDATION.PASSWORD_MIN_LENGTH} characters`],
      maxlength: [VALIDATION.PASSWORD_MAX_LENGTH, `Password cannot exceed ${VALIDATION.PASSWORD_MAX_LENGTH} characters`],
      select: false // Don't include password in API responses
    },
    role: {
      type: String,
      enum: Object.values(UserRole),
      default: UserRole.USER,
      index: true
    },
    status: {
      type: String,
      enum: Object.values(UserStatus),
      default: UserStatus.ACTIVE,
      index: true
    },
    approvalStatus: {
      type: String,
      enum: Object.values(ApprovalStatus),
      default: ApprovalStatus.PENDING,
      index: true
    },
    isEmailVerified: {
      type: Boolean,
      default: false,
      index: true
    },
    canBid: {
      type: Boolean,
      default: false,
      index: true
    },
    canParticipateInLottery: {
      type: Boolean,
      default: false,
      index: true
    },
    profile: {
      avatar: {
        type: String,
        default: 'https://res.cloudinary.com/robinhoot/image/upload/v1/avatars/default.png'
      },
      phoneNumber: {
        type: String,
        validate: {
          validator: function(v) {
            return !v || /^(\+\d{1,3}[- ]?)?\d{10,12}$/.test(v);
          },
          message: 'Please enter a valid phone number'
        }
      },
      address: {
        street: String,
        city: String,
        state: String,
        postalCode: String,
        country: String
      },
      isPhoneVerified: {
        type: Boolean,
        default: false
      },
      identityVerified: {
        type: Boolean,
        default: false
      },
      identityDocuments: [{
        type: {
          type: String,
          enum: ['national_id', 'passport', 'driving_license', 'utility_bill'],
          required: true
        },
        url: {
          type: String,
          required: true
        },
        status: {
          type: String,
          enum: Object.values(ApprovalStatus),
          default: ApprovalStatus.PENDING
        },
        uploadedAt: {
          type: Date,
          default: Date.now
        }
      }]
    },
    // Admin fields
    approvedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    approvedAt: {
      type: Date
    },
    rejectionReason: {
      type: String,
      trim: true,
      maxlength: [500, 'Rejection reason cannot exceed 500 characters']
    },
    adminNotes: {
      type: String,
      trim: true,
      maxlength: [1000, 'Admin notes cannot exceed 1000 characters']
    },
    // Security fields
    lastLogin: {
      type: Date
    },
    resetPasswordToken: String,
    resetPasswordExpire: Date,
    emailVerificationToken: String,
    emailVerificationExpire: Date
  },
  {
    timestamps: true,
    toJSON: { 
      virtuals: true,
      transform: function(doc, ret) {
        delete ret.password;
        delete ret.resetPasswordToken;
        delete ret.resetPasswordExpire;
        delete ret.emailVerificationToken;
        delete ret.emailVerificationExpire;
        return ret;
      }
    },
    toObject: { virtuals: true }
  }
);

// Indexes for performance
userSchema.index({ email: 1 });
userSchema.index({ username: 1 });
userSchema.index({ role: 1, status: 1 });
userSchema.index({ approvalStatus: 1 });
userSchema.index({ canBid: 1 });
userSchema.index({ canParticipateInLottery: 1 });
userSchema.index({ createdAt: -1 });

// Password hashing middleware
userSchema.pre('save', async function(next) {
  // Only hash password if it has been modified
  if (!this.isModified('password')) {
    return next();
  }

  try {
    // Generate salt and hash password
    const salt = await bcrypt.genSalt(SECURITY.BCRYPT_ROUNDS);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error);
  }
});

// Password verification method
userSchema.methods.matchPassword = async function(enteredPassword) {
  return await bcrypt.compare(enteredPassword, this.password);
};

// Generate password reset token
userSchema.methods.getResetPasswordToken = function() {
  // Generate token
  const resetToken = crypto.randomBytes(32).toString('hex');

  // Hash token and save to user
  this.resetPasswordToken = crypto
    .createHash('sha256')
    .update(resetToken)
    .digest('hex');

  // Set token expiry to 10 minutes
  this.resetPasswordExpire = Date.now() + 10 * 60 * 1000;

  return resetToken;
};

// Generate email verification token
userSchema.methods.getEmailVerificationToken = function() {
  // Generate token
  const verificationToken = crypto.randomBytes(32).toString('hex');

  // Hash token and save to user
  this.emailVerificationToken = crypto
    .createHash('sha256')
    .update(verificationToken)
    .digest('hex');

  // Set token expiry to 24 hours
  this.emailVerificationExpire = Date.now() + 24 * 60 * 60 * 1000;

  return verificationToken;
};

// Virtual populate for orders
userSchema.virtual('orders', {
  ref: 'Order',
  localField: '_id',
  foreignField: 'userId'
});

// Virtual populate for bids
userSchema.virtual('bids', {
  ref: 'Auction',
  localField: '_id',
  foreignField: 'bids.userId'
});

// User approval methods
userSchema.methods.approveUser = function(adminId, reason = '') {
  this.approvalStatus = ApprovalStatus.APPROVED;
  this.approvedBy = adminId;
  this.approvedAt = new Date();
  this.canBid = true;
  this.canParticipateInLottery = true;
  this.rejectionReason = undefined;
  return this.save();
};

userSchema.methods.rejectUser = function(adminId, reason = '') {
  this.approvalStatus = ApprovalStatus.REJECTED;
  this.approvedBy = adminId;
  this.approvedAt = new Date();
  this.canBid = false;
  this.canParticipateInLottery = false;
  this.rejectionReason = reason;
  return this.save();
};

userSchema.methods.suspendUser = function(adminId, reason = '') {
  this.approvalStatus = ApprovalStatus.SUSPENDED;
  this.canBid = false;
  this.canParticipateInLottery = false;
  this.rejectionReason = reason;
  return this.save();
};

// Permission check methods
userSchema.methods.canPlaceBid = function() {
  return this.status === UserStatus.ACTIVE &&
         this.approvalStatus === ApprovalStatus.APPROVED &&
         this.canBid &&
         this.isEmailVerified;
};

userSchema.methods.canJoinLottery = function() {
  return this.status === UserStatus.ACTIVE &&
         this.approvalStatus === ApprovalStatus.APPROVED &&
         this.canParticipateInLottery &&
         this.isEmailVerified;
};

userSchema.methods.isActive = function() {
  return this.status === UserStatus.ACTIVE && 
         this.approvalStatus === ApprovalStatus.APPROVED;
};

userSchema.methods.isAdmin = function() {
  return this.role === UserRole.ADMIN;
};

userSchema.methods.isSeller = function() {
  return this.role === UserRole.SELLER;
};

const User = mongoose.model('User', userSchema);

module.exports = User;
