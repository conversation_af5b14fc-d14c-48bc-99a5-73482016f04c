/**
 * Banner Routes Configuration
 * Defines all banner related API endpoints
 */

const express = require('express');
const ErrorMiddleware = require('../middleware/ErrorMiddleware');

class BannerRoutes {
  constructor(container) {
    this.container = container;
    this.router = express.Router();
    this.setupRoutes();
  }

  setupRoutes() {
    const bannerController = new (require('../controllers/BannerController'))(this.container);
    const authMiddleware = this.container.resolve('authMiddleware');

    // Public routes
    this.router.get('/active', 
      ErrorMiddleware.asyncHandler(bannerController.getActiveBanners.bind(bannerController))
    );

    this.router.get('/', 
      ErrorMiddleware.asyncHandler(bannerController.getActiveBanners.bind(bannerController))
    );

    // Admin routes
    this.router.get('/admin/all', 
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(bannerController.getAllBanners.bind(bannerController))
    );

    this.router.post('/', 
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(bannerController.createBanner.bind(bannerController))
    );

    this.router.put('/:id', 
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(bannerController.updateBanner.bind(bannerController))
    );

    this.router.delete('/:id', 
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(bannerController.deleteBanner.bind(bannerController))
    );
  }

  getRouter() {
    return this.router;
  }
}

module.exports = BannerRoutes;
