/**
 * Product Service
 * Business logic for product operations
 */

const { BusinessException, ValidationException, AuthorizationException } = require('../../core/exceptions');
const { ProductStatus, UserRole } = require('../../shared/enums');
const Product = require('../../core/entities/Product');

class ProductService {
  constructor(productRepository, userRepository, categoryRepository, emailService) {
    this.productRepository = productRepository;
    this.userRepository = userRepository;
    this.categoryRepository = categoryRepository;
    this.emailService = emailService;
  }





  /**
   * Create new product
   */
  async createProduct(productData, createdBy) {
    try {
      // Validate user permissions
      const user = await this.userRepository.findById(createdBy);
      if (!user) {
        throw new ValidationException('User not found');
      }

      if (user.role !== UserRole.ADMIN && user.role !== UserRole.SELLER) {
        throw new AuthorizationException('Only admins and sellers can create products');
      }

      // Validate category exists
      if (productData.categoryId) {
        const category = await this.categoryRepository.findById(productData.categoryId);
        if (!category) {
          throw new ValidationException('Category not found');
        }

        // Check if category is active
        if (category.status !== 'active') {
          throw new ValidationException('Cannot assign product to inactive category');
        }
      }

      // Create product entity
      const product = new Product({
        ...productData,
        sellerId: createdBy,
        status: user.role === UserRole.ADMIN ? ProductStatus.ACTIVE : ProductStatus.PENDING,
        metadata: {
          lastModifiedBy: createdBy
        }
      });

      // Save to repository
      const savedProduct = await this.productRepository.create(product.toPersistence());

      // Send notification for approval if needed
      if (savedProduct.status === ProductStatus.PENDING) {
        this.sendProductApprovalNotification(savedProduct);
      }

      return savedProduct;
    } catch (error) {
      if (error instanceof ValidationException || error instanceof AuthorizationException) {
        throw error;
      }
      throw new BusinessException(`Failed to create product: ${error.message}`);
    }
  }

  /**
   * Get all products
   */
  async getAllProducts(options = {}) {
    try {
      // Set default options
      const defaultOptions = {
        page: 1,
        limit: 20,
        sortBy: 'createdAt',
        sortOrder: 'desc',
        status: 'active'
      };

      const queryOptions = { ...defaultOptions, ...options };

      // Validate price range if provided
      if (queryOptions.minPrice !== undefined) {
        const minPrice = parseFloat(queryOptions.minPrice);
        if (isNaN(minPrice) || minPrice < 0) {
          queryOptions.minPrice = 0;
        } else {
          queryOptions.minPrice = minPrice;
        }
      }

      if (queryOptions.maxPrice !== undefined) {
        const maxPrice = parseFloat(queryOptions.maxPrice);
        if (isNaN(maxPrice) || maxPrice < 0) {
          queryOptions.maxPrice = 999999.99;
        } else {
          queryOptions.maxPrice = maxPrice;
        }
      }

      // Remove price validation that was causing issues
      delete queryOptions.minPrice;
      delete queryOptions.maxPrice;

      return await this.productRepository.findAll(queryOptions);
    } catch (error) {
      throw new BusinessException(`Failed to get products: ${error.message}`);
    }
  }

  /**
   * Get product by ID
   */
  async getProductById(id) {
    try {
      const product = await this.productRepository.findById(id);
      if (!product) {
        throw new ValidationException('Product not found');
      }

      // Increment view count (async, don't wait)
      this.incrementViewCount(id);

      return product;
    } catch (error) {
      if (error instanceof ValidationException) {
        throw error;
      }
      throw new BusinessException(`Failed to get product: ${error.message}`);
    }
  }

  /**
   * Update product
   */
  async updateProduct(id, updateData, updatedBy) {
    try {
      const product = await this.productRepository.findById(id);
      if (!product) {
        throw new ValidationException('Product not found');
      }

      // Check permissions
      const user = await this.userRepository.findById(updatedBy);
      if (!user) {
        throw new ValidationException('User not found');
      }

      if (user.role !== UserRole.ADMIN && product.sellerId !== updatedBy) {
        throw new AuthorizationException('You can only update your own products');
      }

      // Validate category if being updated
      if (updateData.categoryId && updateData.categoryId !== product.categoryId) {
        const category = await this.categoryRepository.findById(updateData.categoryId);
        if (!category) {
          throw new ValidationException('Category not found');
        }

        // Check if category is active
        if (category.status !== 'active') {
          throw new ValidationException('Cannot assign product to inactive category');
        }
      }

      // Update metadata
      updateData.metadata = {
        ...product.metadata,
        lastModifiedBy: updatedBy
      };

      // If seller is updating, set status to pending (except for stock updates)
      if (user.role !== UserRole.ADMIN && !this.isStockOnlyUpdate(updateData)) {
        updateData.status = ProductStatus.PENDING;
      }

      const updatedProduct = await this.productRepository.update(id, updateData);
      return updatedProduct;
    } catch (error) {
      if (error instanceof ValidationException || error instanceof AuthorizationException) {
        throw error;
      }
      throw new BusinessException(`Failed to update product: ${error.message}`);
    }
  }

  /**
   * Delete product
   */
  async deleteProduct(id, deletedBy) {
    try {
      const product = await this.productRepository.findById(id);
      if (!product) {
        throw new ValidationException('Product not found');
      }

      // Check permissions
      const user = await this.userRepository.findById(deletedBy);
      if (!user) {
        throw new ValidationException('User not found');
      }

      if (user.role !== UserRole.ADMIN && product.sellerId !== deletedBy) {
        throw new AuthorizationException('You can only delete your own products');
      }

      const success = await this.productRepository.delete(id);
      if (!success) {
        throw new BusinessException('Failed to delete product');
      }

      return true;
    } catch (error) {
      if (error instanceof ValidationException || error instanceof AuthorizationException) {
        throw error;
      }
      throw new BusinessException(`Failed to delete product: ${error.message}`);
    }
  }

  /**
   * Get products by category
   */
  async getProductsByCategory(categoryId, options = {}) {
    try {
      return await this.productRepository.findByCategory(categoryId, options);
    } catch (error) {
      throw new BusinessException(`Failed to get products by category: ${error.message}`);
    }
  }

  /**
   * Get products by seller
   */
  async getProductsBySeller(sellerId, options = {}) {
    try {
      return await this.productRepository.findBySeller(sellerId, options);
    } catch (error) {
      throw new BusinessException(`Failed to get products by seller: ${error.message}`);
    }
  }

  /**
   * Search products
   */
  async searchProducts(query, options = {}) {
    try {
      return await this.productRepository.search(query, options);
    } catch (error) {
      throw new BusinessException(`Failed to search products: ${error.message}`);
    }
  }

  /**
   * Get featured products
   */
  async getFeaturedProducts(options = {}) {
    try {
      return await this.productRepository.findFeatured(options);
    } catch (error) {
      throw new BusinessException(`Failed to get featured products: ${error.message}`);
    }
  }

  /**
   * Get products by status
   */
  async getProductsByStatus(status, options = {}) {
    try {
      return await this.productRepository.findByStatus(status, options);
    } catch (error) {
      throw new BusinessException(`Failed to get products by status: ${error.message}`);
    }
  }

  /**
   * Approve product (Admin)
   */
  async approveProduct(id, approvedBy, reason = '') {
    try {
      const user = await this.userRepository.findById(approvedBy);
      if (!user || user.role !== UserRole.ADMIN) {
        throw new AuthorizationException('Only admins can approve products');
      }

      const product = await this.productRepository.findById(id);
      if (!product) {
        throw new ValidationException('Product not found');
      }

      if (product.status !== ProductStatus.PENDING) {
        throw new ValidationException('Only pending products can be approved');
      }

      const updatedProduct = await this.productRepository.update(id, {
        status: ProductStatus.ACTIVE,
        metadata: {
          ...product.metadata,
          approvedBy,
          approvedAt: new Date()
        }
      });

      // Send approval notification to seller
      this.sendProductApprovedNotification(updatedProduct, reason);

      return updatedProduct;
    } catch (error) {
      if (error instanceof ValidationException || error instanceof AuthorizationException) {
        throw error;
      }
      throw new BusinessException(`Failed to approve product: ${error.message}`);
    }
  }

  /**
   * Reject product (Admin)
   */
  async rejectProduct(id, rejectedBy, reason) {
    try {
      const user = await this.userRepository.findById(rejectedBy);
      if (!user || user.role !== UserRole.ADMIN) {
        throw new AuthorizationException('Only admins can reject products');
      }

      const product = await this.productRepository.findById(id);
      if (!product) {
        throw new ValidationException('Product not found');
      }

      if (product.status !== ProductStatus.PENDING) {
        throw new ValidationException('Only pending products can be rejected');
      }

      const updatedProduct = await this.productRepository.update(id, {
        status: ProductStatus.REJECTED,
        metadata: {
          ...product.metadata,
          rejectedBy,
          rejectedAt: new Date(),
          rejectionReason: reason
        }
      });

      // Send rejection notification to seller
      this.sendProductRejectedNotification(updatedProduct, reason);

      return updatedProduct;
    } catch (error) {
      if (error instanceof ValidationException || error instanceof AuthorizationException) {
        throw error;
      }
      throw new BusinessException(`Failed to reject product: ${error.message}`);
    }
  }

  /**
   * Update product status
   */
  async updateProductStatus(id, status, updatedBy) {
    try {
      const user = await this.userRepository.findById(updatedBy);
      if (!user || user.role !== UserRole.ADMIN) {
        throw new AuthorizationException('Only admins can update product status');
      }

      const product = await this.productRepository.updateStatus(id, status);
      if (!product) {
        throw new ValidationException('Product not found');
      }

      return product;
    } catch (error) {
      if (error instanceof ValidationException || error instanceof AuthorizationException) {
        throw error;
      }
      throw new BusinessException(`Failed to update product status: ${error.message}`);
    }
  }

  /**
   * Update product inventory
   */
  async updateInventory(id, quantity, updatedBy) {
    try {
      const product = await this.productRepository.findById(id);
      if (!product) {
        throw new ValidationException('Product not found');
      }

      // Check permissions
      const user = await this.userRepository.findById(updatedBy);
      if (!user) {
        throw new ValidationException('User not found');
      }

      if (user.role !== UserRole.ADMIN && product.sellerId !== updatedBy) {
        throw new AuthorizationException('You can only update inventory for your own products');
      }

      const updatedProduct = await this.productRepository.updateInventory(id, quantity);
      return updatedProduct;
    } catch (error) {
      if (error instanceof ValidationException || error instanceof AuthorizationException) {
        throw error;
      }
      throw new BusinessException(`Failed to update inventory: ${error.message}`);
    }
  }

  /**
   * Check product availability
   */
  async checkAvailability(id, quantity = 1) {
    try {
      return await this.productRepository.checkAvailability(id, quantity);
    } catch (error) {
      throw new BusinessException(`Failed to check availability: ${error.message}`);
    }
  }

  /**
   * Get product statistics
   */
  async getProductStatistics(id) {
    try {
      return await this.productRepository.getStatistics(id);
    } catch (error) {
      throw new BusinessException(`Failed to get product statistics: ${error.message}`);
    }
  }

  /**
   * Increment view count
   */
  async incrementViewCount(id) {
    try {
      await this.productRepository.incrementViewCount(id);
    } catch (error) {
      // Don't throw error for view count increment
      console.error(`Failed to increment view count: ${error.message}`);
    }
  }

  /**
   * Get low stock products
   */
  async getLowStockProducts(threshold = 10, options = {}) {
    try {
      return await this.productRepository.findLowStock(threshold, options);
    } catch (error) {
      throw new BusinessException(`Failed to get low stock products: ${error.message}`);
    }
  }

  /**
   * Get product counts by status
   */
  async getProductCounts(sellerId = null) {
    try {
      return await this.productRepository.getCountByStatus(sellerId);
    } catch (error) {
      throw new BusinessException(`Failed to get product counts: ${error.message}`);
    }
  }

  /**
   * Get related products
   */
  async getRelatedProducts(productId, limit = 5) {
    try {
      return await this.productRepository.findRelated(productId, limit);
    } catch (error) {
      throw new BusinessException(`Failed to get related products: ${error.message}`);
    }
  }

  /**
   * Bulk update products
   */
  async bulkUpdateProducts(updates, updatedBy) {
    try {
      const user = await this.userRepository.findById(updatedBy);
      if (!user || user.role !== UserRole.ADMIN) {
        throw new AuthorizationException('Only admins can perform bulk updates');
      }

      return await this.productRepository.bulkUpdate(updates);
    } catch (error) {
      if (error instanceof AuthorizationException) {
        throw error;
      }
      throw new BusinessException(`Failed to bulk update products: ${error.message}`);
    }
  }

  /**
   * Check if update is stock-only
   */
  isStockOnlyUpdate(updateData) {
    const allowedFields = ['stock', 'lowStockThreshold'];
    const updateFields = Object.keys(updateData);
    return updateFields.every(field => allowedFields.includes(field) || field === 'metadata');
  }

  /**
   * Send product approval notification
   */
  async sendProductApprovalNotification(product) {
    try {
      // Notify admins about new product pending approval
      const admins = await this.userRepository.findByRole(UserRole.ADMIN);
      for (const admin of admins) {
        if (admin.email) {
          await this.emailService.sendNotificationEmail(
            admin.email,
            'New Product Pending Approval',
            `A new product "${product.name}" is pending approval.`
          );
        }
      }
    } catch (error) {
      console.error(`Failed to send approval notification: ${error.message}`);
    }
  }

  /**
   * Send product approved notification
   */
  async sendProductApprovedNotification(product, reason) {
    try {
      const seller = await this.userRepository.findById(product.sellerId);
      if (seller && seller.email) {
        await this.emailService.sendNotificationEmail(
          seller.email,
          'Product Approved',
          `Your product "${product.name}" has been approved and is now live. ${reason ? `Reason: ${reason}` : ''}`
        );
      }
    } catch (error) {
      console.error(`Failed to send approved notification: ${error.message}`);
    }
  }

  /**
   * Send product rejected notification
   */
  async sendProductRejectedNotification(product, reason) {
    try {
      const seller = await this.userRepository.findById(product.sellerId);
      if (seller && seller.email) {
        await this.emailService.sendNotificationEmail(
          seller.email,
          'Product Rejected',
          `Your product "${product.name}" has been rejected. Reason: ${reason}`
        );
      }
    } catch (error) {
      console.error(`Failed to send rejected notification: ${error.message}`);
    }
  }
}

module.exports = ProductService;
