/**
 * Migration Script: Product-Category Relationship Fix
 * Bu script mevcut products'ları temizler ve category sistemini düzeltir
 */

const mongoose = require('mongoose');
require('dotenv').config();

// Models
const ProductModel = require('../src/infrastructure/database/models/Product');
const CategoryModel = require('../src/infrastructure/database/models/Category');

async function connectDatabase() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/robinhood_database');
    console.log('✅ Database connected successfully');
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    process.exit(1);
  }
}

async function migrateProductCategories() {
  try {
    console.log('🚀 Starting Product-Category Migration...');

    // 1. Mevcut kategorileri listele
    const categories = await CategoryModel.find({}).lean();
    console.log(`📋 Found ${categories.length} categories:`);
    categories.forEach(cat => {
      console.log(`   - ${cat.name} (${cat._id})`);
    });

    // 2. Mevcut products'ları listele
    const products = await ProductModel.find({}).lean();
    console.log(`📦 Found ${products.length} products`);

    // 3. Products'ları analiz et
    let productsWithValidCategory = 0;
    let productsWithInvalidCategory = 0;
    let productsWithoutCategory = 0;

    for (const product of products) {
      if (!product.categoryId) {
        productsWithoutCategory++;
        console.log(`❌ Product "${product.name}" has no categoryId`);
      } else if (mongoose.Types.ObjectId.isValid(product.categoryId)) {
        const categoryExists = categories.find(cat => cat._id.toString() === product.categoryId.toString());
        if (categoryExists) {
          productsWithValidCategory++;
          console.log(`✅ Product "${product.name}" has valid category: ${categoryExists.name}`);
        } else {
          productsWithInvalidCategory++;
          console.log(`⚠️  Product "${product.name}" has invalid categoryId: ${product.categoryId}`);
        }
      } else {
        productsWithInvalidCategory++;
        console.log(`❌ Product "${product.name}" has invalid categoryId format: ${product.categoryId}`);
      }
    }

    console.log('\n📊 Migration Summary:');
    console.log(`✅ Products with valid category: ${productsWithValidCategory}`);
    console.log(`⚠️  Products with invalid category: ${productsWithInvalidCategory}`);
    console.log(`❌ Products without category: ${productsWithoutCategory}`);

    // 4. Kullanıcıdan onay al
    const readline = require('readline');
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });

    const answer = await new Promise(resolve => {
      rl.question('\n🤔 Do you want to fix invalid categories? (y/n): ', resolve);
    });
    rl.close();

    if (answer.toLowerCase() !== 'y') {
      console.log('❌ Migration cancelled by user');
      return;
    }

    // 5. Invalid categories'i düzelt
    let fixedCount = 0;
    
    for (const product of products) {
      let needsUpdate = false;
      let updateData = {};

      // CategoryId null/undefined ise null yap
      if (!product.categoryId) {
        updateData.categoryId = null;
        needsUpdate = true;
      }
      // CategoryId geçersiz format ise null yap
      else if (!mongoose.Types.ObjectId.isValid(product.categoryId)) {
        updateData.categoryId = null;
        needsUpdate = true;
        console.log(`🔧 Fixing invalid categoryId for "${product.name}": ${product.categoryId} -> null`);
      }
      // CategoryId geçerli format ama category mevcut değil ise null yap
      else {
        const categoryExists = categories.find(cat => cat._id.toString() === product.categoryId.toString());
        if (!categoryExists) {
          updateData.categoryId = null;
          needsUpdate = true;
          console.log(`🔧 Fixing non-existent categoryId for "${product.name}": ${product.categoryId} -> null`);
        }
      }

      if (needsUpdate) {
        await ProductModel.findByIdAndUpdate(product._id, updateData);
        fixedCount++;
      }
    }

    console.log(`\n✅ Migration completed! Fixed ${fixedCount} products`);

    // 6. Final verification
    const updatedProducts = await ProductModel.find({}).populate('categoryId', 'name slug').lean();
    console.log('\n🔍 Final verification:');
    
    let validCount = 0;
    let nullCount = 0;
    
    for (const product of updatedProducts) {
      if (product.categoryId) {
        validCount++;
        console.log(`✅ "${product.name}" -> Category: ${product.categoryId.name}`);
      } else {
        nullCount++;
        console.log(`⚪ "${product.name}" -> No category`);
      }
    }

    console.log(`\n📊 Final Summary:`);
    console.log(`✅ Products with valid category: ${validCount}`);
    console.log(`⚪ Products without category: ${nullCount}`);
    console.log(`📦 Total products: ${updatedProducts.length}`);

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  }
}

async function main() {
  try {
    await connectDatabase();
    await migrateProductCategories();
    console.log('\n🎉 Migration completed successfully!');
  } catch (error) {
    console.error('❌ Migration failed:', error);
  } finally {
    await mongoose.disconnect();
    console.log('👋 Database disconnected');
    process.exit(0);
  }
}

// Script'i çalıştır
if (require.main === module) {
  main();
}

module.exports = { migrateProductCategories };
