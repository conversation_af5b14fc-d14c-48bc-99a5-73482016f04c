const bcrypt = require('bcryptjs');

module.exports = {
  /**
   * @param db {import('mongodb').Db}
   * @param client {import('mongodb').MongoClient}
   * @returns {Promise<void>}
   */
  async up(db, client) {
    console.log('🚀 Starting database initialization...');

    // Drop all existing collections to start fresh
    const collections = await db.listCollections().toArray();
    for (const collection of collections) {
      if (collection.name !== 'changelog') { // Don't drop migration changelog
        console.log(`🗑️  Dropping collection: ${collection.name}`);
        await db.collection(collection.name).drop();
      }
    }

    // Create indexes for better performance
    console.log('📊 Creating database indexes...');

    // Users collection indexes
    await db.collection('users').createIndex({ email: 1 }, { unique: true });
    await db.collection('users').createIndex({ username: 1 }, { unique: true });
    await db.collection('users').createIndex({ approvalStatus: 1 });
    await db.collection('users').createIndex({ canBid: 1 });
    await db.collection('users').createIndex({ canParticipateInLottery: 1 });

    // Categories collection indexes
    await db.collection('categories').createIndex({ slug: 1 }, { unique: true });
    await db.collection('categories').createIndex({ parentId: 1 });
    await db.collection('categories').createIndex({ isActive: 1 });

    // Products collection indexes
    await db.collection('products').createIndex({ sku: 1 }, { unique: true, sparse: true });
    await db.collection('products').createIndex({ barcode: 1 }, { unique: true, sparse: true });
    await db.collection('products').createIndex({ categoryId: 1 });
    await db.collection('products').createIndex({ sellerId: 1 });
    await db.collection('products').createIndex({ isActive: 1 });
    await db.collection('products').createIndex({ isFeatured: 1 });

    // Orders collection indexes
    await db.collection('orders').createIndex({ orderNumber: 1 }, { unique: true });
    await db.collection('orders').createIndex({ userId: 1 });
    await db.collection('orders').createIndex({ status: 1 });

    // Auctions collection indexes
    await db.collection('auctions').createIndex({ productId: 1 });
    await db.collection('auctions').createIndex({ sellerId: 1 });
    await db.collection('auctions').createIndex({ status: 1 });
    await db.collection('auctions').createIndex({ endTime: 1 });

    // Lotteries collection indexes
    await db.collection('lotteries').createIndex({ status: 1 });
    await db.collection('lotteries').createIndex({ endTime: 1 });

    console.log('✅ Database indexes created successfully');

    // Create default admin user
    console.log('👤 Creating default admin user...');
    const hashedPassword = await bcrypt.hash('eEf8kbpE9G8XhiL', 12);

    await db.collection('users').insertOne({
      name: 'Admin User',
      email: '<EMAIL>',
      username: 'admin',
      password: hashedPassword,
      role: 'admin',
      status: 'active',
      approvalStatus: 'approved',
      isEmailVerified: true,
      canBid: true,
      canParticipateInLottery: true,
      canSell: true,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // Create default categories
    console.log('📂 Creating default categories...');
    const categories = [
      {
        name: 'Elektronik',
        slug: 'elektronik',
        description: 'Elektronik ürünler ve aksesuarlar',
        shortDescription: 'Teknoloji ürünleri',
        parentId: null,
        level: 0,
        order: 1,
        isActive: true,
        isFeatured: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Giyim',
        slug: 'giyim',
        description: 'Kadın, erkek ve çocuk giyim ürünleri',
        shortDescription: 'Moda ve giyim',
        parentId: null,
        level: 0,
        order: 2,
        isActive: true,
        isFeatured: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Ev & Yaşam',
        slug: 'ev-yasam',
        description: 'Ev dekorasyonu ve yaşam ürünleri',
        shortDescription: 'Ev eşyaları',
        parentId: null,
        level: 0,
        order: 3,
        isActive: true,
        isFeatured: false,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Spor & Outdoor',
        slug: 'spor-outdoor',
        description: 'Spor malzemeleri ve outdoor ürünler',
        shortDescription: 'Spor ürünleri',
        parentId: null,
        level: 0,
        order: 4,
        isActive: true,
        isFeatured: false,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Kitap & Hobi',
        slug: 'kitap-hobi',
        description: 'Kitaplar ve hobi ürünleri',
        shortDescription: 'Kitap ve hobi',
        parentId: null,
        level: 0,
        order: 5,
        isActive: true,
        isFeatured: false,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    await db.collection('categories').insertMany(categories);

    console.log('✅ Database initialization completed successfully!');
    console.log('📧 Admin user created: <EMAIL>');
    console.log('🔑 Admin password: eEf8kbpE9G8XhiL');
  },

  /**
   * @param db {import('mongodb').Db}
   * @param client {import('mongodb').MongoClient}
   * @returns {Promise<void>}
   */
  async down(db, client) {
    console.log('🔄 Rolling back database initialization...');

    // Drop all collections except changelog
    const collections = await db.listCollections().toArray();
    for (const collection of collections) {
      if (collection.name !== 'changelog') {
        console.log(`🗑️  Dropping collection: ${collection.name}`);
        await db.collection(collection.name).drop();
      }
    }

    console.log('✅ Database rollback completed');
  }
};
