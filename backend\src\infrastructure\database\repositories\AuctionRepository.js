/**
 * Auction Repository Implementation
 * Implements IAuctionRepository interface
 */

const IAuctionRepository = require('../../../core/interfaces/IAuctionRepository');
const AuctionModel = require('../models/Auction');
const Auction = require('../../../core/entities/Auction');
const { PAGINATION } = require('../../../shared/constants');
const { DatabaseException } = require('../../../core/exceptions');

class AuctionRepository extends IAuctionRepository {
  constructor() {
    super();
    this.model = AuctionModel;
  }

  /**
   * Create a new auction
   */
  async create(auctionData) {
    try {
      const auction = new this.model(auctionData);
      const savedAuction = await auction.save();
      return Auction.fromPersistence(savedAuction.toObject());
    } catch (error) {
      throw new DatabaseException(`Failed to create auction: ${error.message}`);
    }
  }

  /**
   * Find all auctions
   */
  async findAll(options = {}) {
    try {
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        status,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = options;

      const query = {};
      if (status) query.status = status;

      const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };
      const skip = (page - 1) * limit;

      const [auctions, total] = await Promise.all([
        this.model.find(query)
          .populate('sellerId', 'name username')
          .populate('productId', 'name images')
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .lean(),
        this.model.countDocuments(query)
      ]);

      return {
        auctions: auctions.map(auction => Auction.fromPersistence(auction)),
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw new DatabaseException(`Failed to find auctions: ${error.message}`);
    }
  }

  /**
   * Find auction by ID
   */
  async findById(id) {
    try {
      const auction = await this.model.findById(id)
        .populate('sellerId', 'name username email')
        .populate('productId')
        .populate('winnerId', 'name username')
        .lean();

      return auction ? Auction.fromPersistence(auction) : null;
    } catch (error) {
      throw new DatabaseException(`Failed to find auction: ${error.message}`);
    }
  }

  /**
   * Find auctions by seller ID
   */
  async findBySellerId(sellerId, options = {}) {
    try {
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        status,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = options;

      const query = { sellerId };
      if (status) query.status = status;

      const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };
      const skip = (page - 1) * limit;

      const [auctions, total] = await Promise.all([
        this.model.find(query)
          .populate('productId', 'name images')
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .lean(),
        this.model.countDocuments(query)
      ]);

      return {
        auctions: auctions.map(auction => Auction.fromPersistence(auction)),
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw new DatabaseException(`Failed to find auctions by seller: ${error.message}`);
    }
  }

  /**
   * Find active auctions
   */
  async findActive(options = {}) {
    try {
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        sortBy = 'endTime',
        sortOrder = 'asc'
      } = options;

      const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };
      const skip = (page - 1) * limit;

      const [auctions, total] = await Promise.all([
        this.model.findActive()
          .populate('sellerId', 'name username')
          .populate('productId', 'name images category')
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .lean(),
        this.model.findActive().countDocuments()
      ]);

      return {
        auctions: auctions.map(auction => Auction.fromPersistence(auction)),
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw new DatabaseException(`Failed to find active auctions: ${error.message}`);
    }
  }

  /**
   * Find auctions by status
   */
  async findByStatus(status, options = {}) {
    try {
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = options;

      const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };
      const skip = (page - 1) * limit;

      const [auctions, total] = await Promise.all([
        this.model.find({ status })
          .populate('sellerId', 'name username')
          .populate('productId', 'name images')
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .lean(),
        this.model.countDocuments({ status })
      ]);

      return {
        auctions: auctions.map(auction => Auction.fromPersistence(auction)),
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw new DatabaseException(`Failed to find auctions by status: ${error.message}`);
    }
  }

  /**
   * Search auctions
   */
  async search(query, options = {}) {
    try {
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        status,
        minPrice,
        maxPrice,
        sortBy = 'relevance',
        sortOrder = 'desc'
      } = options;

      const searchQuery = {
        $text: { $search: query }
      };

      if (status) searchQuery.status = status;
      if (minPrice !== undefined) searchQuery.currentPrice = { $gte: minPrice };
      if (maxPrice !== undefined) {
        searchQuery.currentPrice = searchQuery.currentPrice || {};
        searchQuery.currentPrice.$lte = maxPrice;
      }

      let sort = {};
      if (sortBy === 'relevance') {
        sort = { score: { $meta: 'textScore' } };
      } else {
        sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };
      }

      const skip = (page - 1) * limit;

      const [auctions, total] = await Promise.all([
        this.model.find(searchQuery, { score: { $meta: 'textScore' } })
          .populate('sellerId', 'name username')
          .populate('productId', 'name images')
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .lean(),
        this.model.countDocuments(searchQuery)
      ]);

      return {
        auctions: auctions.map(auction => Auction.fromPersistence(auction)),
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw new DatabaseException(`Failed to search auctions: ${error.message}`);
    }
  }

  /**
   * Update auction
   */
  async update(id, updateData) {
    try {
      const auction = await this.model.findByIdAndUpdate(
        id,
        { ...updateData, updatedAt: new Date() },
        { new: true, runValidators: true }
      ).lean();

      return auction ? Auction.fromPersistence(auction) : null;
    } catch (error) {
      throw new DatabaseException(`Failed to update auction: ${error.message}`);
    }
  }

  /**
   * Delete auction
   */
  async delete(id) {
    try {
      const result = await this.model.findByIdAndDelete(id);
      return !!result;
    } catch (error) {
      throw new DatabaseException(`Failed to delete auction: ${error.message}`);
    }
  }

  /**
   * Add bid to auction
   */
  async addBid(auctionId, bidData) {
    try {
      const auction = await this.model.findByIdAndUpdate(
        auctionId,
        {
          $push: { bids: bidData },
          $set: {
            currentPrice: bidData.amount,
            updatedAt: new Date()
          }
        },
        { new: true, runValidators: true }
      ).lean();

      return auction ? Auction.fromPersistence(auction) : null;
    } catch (error) {
      throw new DatabaseException(`Failed to add bid: ${error.message}`);
    }
  }

  /**
   * Get auction bids
   */
  async getBids(auctionId, options = {}) {
    try {
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        sortBy = 'timestamp',
        sortOrder = 'desc'
      } = options;

      const auction = await this.model.findById(auctionId)
        .select('bids')
        .lean();

      if (!auction) return [];

      const sort = sortOrder === 'desc' ? -1 : 1;
      const sortedBids = auction.bids.sort((a, b) => {
        if (sortBy === 'timestamp') {
          return sort * (new Date(b.timestamp) - new Date(a.timestamp));
        }
        if (sortBy === 'amount') {
          return sort * (b.amount - a.amount);
        }
        return 0;
      });

      const skip = (page - 1) * limit;
      const paginatedBids = sortedBids.slice(skip, skip + limit);

      return {
        bids: paginatedBids,
        pagination: {
          page,
          limit,
          total: auction.bids.length,
          pages: Math.ceil(auction.bids.length / limit)
        }
      };
    } catch (error) {
      throw new DatabaseException(`Failed to get auction bids: ${error.message}`);
    }
  }

  /**
   * Get user's bids
   */
  async getUserBids(userId, options = {}) {
    try {
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT
      } = options;

      const skip = (page - 1) * limit;

      const auctions = await this.model.aggregate([
        { $match: { 'bids.userId': userId } },
        { $unwind: '$bids' },
        { $match: { 'bids.userId': userId } },
        {
          $lookup: {
            from: 'products',
            localField: 'productId',
            foreignField: '_id',
            as: 'product'
          }
        },
        { $unwind: '$product' },
        {
          $project: {
            auctionId: '$_id',
            title: 1,
            status: 1,
            endTime: 1,
            currentPrice: 1,
            bid: '$bids',
            product: { name: 1, images: 1 }
          }
        },
        { $sort: { 'bid.timestamp': -1 } },
        { $skip: skip },
        { $limit: limit }
      ]);

      const total = await this.model.aggregate([
        { $match: { 'bids.userId': userId } },
        { $unwind: '$bids' },
        { $match: { 'bids.userId': userId } },
        { $count: 'total' }
      ]);

      return {
        bids: auctions,
        pagination: {
          page,
          limit,
          total: total[0]?.total || 0,
          pages: Math.ceil((total[0]?.total || 0) / limit)
        }
      };
    } catch (error) {
      throw new DatabaseException(`Failed to get user bids: ${error.message}`);
    }
  }

  /**
   * Get ending soon auctions
   */
  async findEndingSoon(hours = 24, options = {}) {
    try {
      const auctions = await this.model.findEndingSoon(hours)
        .populate('sellerId', 'name username')
        .populate('productId', 'name images')
        .sort({ endTime: 1 })
        .lean();

      return auctions.map(auction => Auction.fromPersistence(auction));
    } catch (error) {
      throw new DatabaseException(`Failed to find ending soon auctions: ${error.message}`);
    }
  }

  /**
   * Get expired auctions
   */
  async findExpired(options = {}) {
    try {
      const auctions = await this.model.findExpired()
        .populate('sellerId', 'name username')
        .populate('productId', 'name images')
        .lean();

      return auctions.map(auction => Auction.fromPersistence(auction));
    } catch (error) {
      throw new DatabaseException(`Failed to find expired auctions: ${error.message}`);
    }
  }

  /**
   * Update auction status
   */
  async updateStatus(id, status) {
    try {
      const auction = await this.model.findByIdAndUpdate(
        id,
        { status, updatedAt: new Date() },
        { new: true, runValidators: true }
      ).lean();

      return auction ? Auction.fromPersistence(auction) : null;
    } catch (error) {
      throw new DatabaseException(`Failed to update auction status: ${error.message}`);
    }
  }

  /**
   * Increment view count
   */
  async incrementViewCount(id) {
    try {
      const auction = await this.model.findByIdAndUpdate(
        id,
        { $inc: { viewCount: 1 } },
        { new: true }
      ).lean();

      return auction ? Auction.fromPersistence(auction) : null;
    } catch (error) {
      throw new DatabaseException(`Failed to increment view count: ${error.message}`);
    }
  }

  /**
   * Get auction statistics
   */
  async getStatistics(id) {
    try {
      const auction = await this.model.findById(id).lean();
      if (!auction) return null;

      return {
        bidCount: auction.bids.length,
        uniqueBidders: new Set(auction.bids.map(bid => bid.userId.toString())).size,
        averageBid: auction.bids.length > 0
          ? auction.bids.reduce((sum, bid) => sum + bid.amount, 0) / auction.bids.length
          : 0,
        highestBid: auction.bids.length > 0
          ? Math.max(...auction.bids.map(bid => bid.amount))
          : 0,
        viewCount: auction.viewCount,
        watcherCount: auction.watcherCount
      };
    } catch (error) {
      throw new DatabaseException(`Failed to get auction statistics: ${error.message}`);
    }
  }

  /**
   * Get auctions count by status
   */
  async getCountByStatus(sellerId = null) {
    try {
      const matchStage = sellerId ? { sellerId } : {};

      const counts = await this.model.aggregate([
        { $match: matchStage },
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 }
          }
        }
      ]);

      const result = {};
      counts.forEach(item => {
        result[item._id] = item.count;
      });

      return result;
    } catch (error) {
      throw new DatabaseException(`Failed to get auction counts: ${error.message}`);
    }
  }
}

module.exports = AuctionRepository;
