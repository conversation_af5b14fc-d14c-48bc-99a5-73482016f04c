const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:5000/api';
let adminToken = '';
let adminUserId = '';
let testCategoryId = '';

async function testProductCRUD() {
  console.log('🚀 Testing Product CRUD Operations...\n');

  try {
    // 1. Login to get admin token
    console.log('1️⃣ Logging in as admin...');
    const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'eEf8kbpE9G8XhiL'
      })
    });

    const loginData = await loginResponse.json();
    adminToken = loginData.data.token;
    adminUserId = loginData.data.user.id;
    console.log('✅ Admin login successful\n');

    // 2. Get categories to use for product
    console.log('2️⃣ Getting categories for product...');
    const categoriesResponse = await fetch(`${BASE_URL}/categories`);
    const categoriesData = await categoriesResponse.json();
    const categories = categoriesData.data.categories;

    testCategoryId = categories.find(cat => cat.slug === 'elektronik')?.id || categories[0]?.id;
    console.log(`📂 Using category ID: ${testCategoryId} (${categories.find(cat => cat.id === testCategoryId)?.name})\n`);

    // 3. List existing products
    console.log('3️⃣ Listing existing products...');
    const listResponse = await fetch(`${BASE_URL}/products`);
    const listData = await listResponse.json();
    const existingProducts = listData.data.products;

    console.log(`📦 Found ${existingProducts.length} existing products:`);
    existingProducts.forEach((product, index) => {
      console.log(`  ${index + 1}. ${product.name} - ID: ${product.id} - Price: ${product.price}₺`);
    });
    console.log('');

    // 4. Clean up any existing test product
    console.log('4️⃣ Cleaning up existing test products...');
    const existingTestProduct = existingProducts.find(p => p.name.includes('Test CRUD Product'));
    if (existingTestProduct && existingTestProduct.id) {
      try {
        await fetch(`${BASE_URL}/products/${existingTestProduct.id}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${adminToken}`,
            'Content-Type': 'application/json'
          }
        });
        console.log('🧹 Cleaned up existing test product');
      } catch (error) {
        console.log('🧹 No existing test product to clean up');
      }
    }

    // 5. CREATE - Add new product
    console.log('5️⃣ Testing CREATE - Adding new product...');
    const newProduct = {
      name: 'Test CRUD Product',
      description: 'CRUD test için oluşturulan ürün. Bu ürün test amaçlı olarak oluşturulmuştur.',
      shortDescription: 'CRUD test ürünü',
      price: 1500,
      originalPrice: 2000,
      discountPercentage: 25,
      categoryId: testCategoryId,
      sellerId: adminUserId,
      images: [
        {
          url: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=500',
          alt: 'Test Product Image',
          isPrimary: true
        }
      ],
      stock: 100,
      lowStockThreshold: 10,
      specifications: {
        'Marka': 'Test Brand',
        'Model': 'CRUD-2024',
        'Renk': 'Siyah',
        'Garanti': '2 Yıl'
      },
      tags: ['test', 'crud', 'demo'],
      featured: true,
      status: 'active'
    };

    const createResponse = await fetch(`${BASE_URL}/products`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${adminToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(newProduct)
    });

    const createData = await createResponse.json();
    if (createResponse.ok) {
      const createdProduct = createData.data.product;
      console.log(`✅ CREATE successful - Product created with ID: ${createdProduct.id}`);
      console.log(`   Name: ${createdProduct.name}`);
      console.log(`   Price: ${createdProduct.price}₺`);
      console.log(`   Stock: ${createdProduct.stock}`);
      console.log(`   Status: ${createdProduct.status}`);
      console.log('');

      // 6. READ - Get product by ID
      console.log('6️⃣ Testing READ - Getting product by ID...');
      const readResponse = await fetch(`${BASE_URL}/products/${createdProduct.id}`);
      const readData = await readResponse.json();

      if (readResponse.ok) {
        const fetchedProduct = readData.data.product;
        console.log(`✅ READ successful - Product fetched:`);
        console.log(`   ID: ${fetchedProduct.id}`);
        console.log(`   Name: ${fetchedProduct.name}`);
        console.log(`   Description: ${fetchedProduct.description}`);
        console.log(`   Price: ${fetchedProduct.price}₺`);
        console.log(`   Stock: ${fetchedProduct.stock}`);
        console.log(`   Featured: ${fetchedProduct.featured}`);
        console.log('');

        // 7. UPDATE - Edit product
        console.log('7️⃣ Testing UPDATE - Editing product...');
        const updateData = {
          name: 'Test CRUD Product (Güncellenmiş)',
          description: 'CRUD test için oluşturulan ve güncellenmiş ürün',
          shortDescription: 'Güncellenmiş CRUD test ürünü',
          price: 1200,
          originalPrice: 1800,
          discountPercentage: 33,
          stock: 75,
          featured: false,
          status: 'active'
        };

        const updateResponse = await fetch(`${BASE_URL}/products/${createdProduct.id}`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${adminToken}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(updateData)
        });

        const updatedData = await updateResponse.json();
        if (updateResponse.ok) {
          const updatedProduct = updatedData.data.product;
          console.log(`✅ UPDATE successful - Product updated:`);
          console.log(`   Name: ${updatedProduct.name}`);
          console.log(`   Price: ${updatedProduct.price}₺ (was ${createdProduct.price}₺)`);
          console.log(`   Stock: ${updatedProduct.stock} (was ${createdProduct.stock})`);
          console.log(`   Featured: ${updatedProduct.featured} (was ${createdProduct.featured})`);
          console.log('');

          // 8. Verify update by reading again
          console.log('8️⃣ Verifying update by reading again...');
          const verifyResponse = await fetch(`${BASE_URL}/products/${createdProduct.id}`);
          const verifyData = await verifyResponse.json();

          if (verifyResponse.ok) {
            const verifiedProduct = verifyData.data.product;
            console.log(`✅ VERIFY successful - Changes confirmed:`);
            console.log(`   Name: ${verifiedProduct.name}`);
            console.log(`   Price: ${verifiedProduct.price}₺`);
            console.log(`   Stock: ${verifiedProduct.stock}`);
            console.log(`   Featured: ${verifiedProduct.featured}`);
            console.log('');
          } else {
            console.log(`❌ VERIFY failed:`, verifyData.message);
          }

          // 9. DELETE - Remove product
          console.log('9️⃣ Testing DELETE - Removing product...');
          const deleteResponse = await fetch(`${BASE_URL}/products/${createdProduct.id}`, {
            method: 'DELETE',
            headers: {
              'Authorization': `Bearer ${adminToken}`,
              'Content-Type': 'application/json'
            }
          });

          const deleteData = await deleteResponse.json();
          if (deleteResponse.ok) {
            console.log(`✅ DELETE successful - Product removed`);
            console.log('');

            // 10. Verify deletion by trying to read
            console.log('🔟 Verifying deletion by trying to read...');
            const verifyDeleteResponse = await fetch(`${BASE_URL}/products/${createdProduct.id}`);
            const verifyDeleteData = await verifyDeleteResponse.json();

            if (verifyDeleteResponse.status === 404 ||
                (verifyDeleteData && verifyDeleteData.message === 'Product not found')) {
              console.log(`✅ DELETE VERIFY successful - Product not found (as expected)`);
            } else {
              console.log(`❌ DELETE VERIFY failed - Product still exists:`, verifyDeleteData);
            }
          } else {
            console.log(`❌ DELETE failed:`, deleteData.message);
          }

        } else {
          console.log(`❌ UPDATE failed:`, updatedData.message);
        }

      } else {
        console.log(`❌ READ failed:`, readData.message);
      }

    } else {
      console.log(`❌ CREATE failed:`, createData.message);
      console.log('Response:', JSON.stringify(createData, null, 2));
    }

    console.log('\n🎉 Product CRUD testing completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

testProductCRUD();
