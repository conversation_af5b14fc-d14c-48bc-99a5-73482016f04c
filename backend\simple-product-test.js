const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:5000/api';
let adminToken = '';

async function simpleProductTest() {
  console.log('🚀 Simple Product Test...\n');

  try {
    // 1. Login to get admin token
    console.log('1️⃣ Logging in as admin...');
    const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'eEf8kbpE9G8XhiL'
      })
    });

    const loginData = await loginResponse.json();
    adminToken = loginData.data.token;
    console.log('✅ Admin login successful\n');

    // 2. Get categories
    console.log('2️⃣ Getting categories...');
    const categoriesResponse = await fetch(`${BASE_URL}/categories`);
    const categoriesData = await categoriesResponse.json();
    const categories = categoriesData.data.categories;
    
    const testCategoryId = categories.find(cat => cat.slug === 'elektronik')?.id || categories[0]?.id;
    console.log(`📂 Using category ID: ${testCategoryId}\n`);

    // 3. Create simple product
    console.log('3️⃣ Creating simple product...');
    const newProduct = {
      name: 'Simple Test Product',
      description: 'Simple test için oluşturulan ürün',
      price: 1000,
      categoryId: testCategoryId,
      stock: 50,
      status: 'active'
    };

    console.log('Product data:', JSON.stringify(newProduct, null, 2));

    const createResponse = await fetch(`${BASE_URL}/products`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${adminToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(newProduct)
    });

    const createData = await createResponse.json();
    console.log('Create response status:', createResponse.status);
    console.log('Create response:', JSON.stringify(createData, null, 2));

    if (createResponse.ok) {
      console.log('✅ Product created successfully!');
      console.log('Product ID:', createData.data.product.id);
    } else {
      console.log('❌ Product creation failed');
    }

    console.log('\n🎉 Simple test completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

simpleProductTest();
