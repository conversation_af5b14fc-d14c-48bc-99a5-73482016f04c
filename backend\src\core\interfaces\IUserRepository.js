/**
 * User Repository Interface
 * Kullanıcı veri er<PERSON><PERSON><PERSON> katman<PERSON> için interface
 */

class IUserRepository {
  /**
   * Kullanıcı oluştur
   * @param {User} user - Kullanıcı entity'si
   * @returns {Promise<User>} - <PERSON><PERSON>ş<PERSON><PERSON><PERSON> kullanıcı
   */
  async create(user) {
    throw new Error('Method not implemented');
  }

  /**
   * ID ile kullanıcı bul
   * @param {string} id - Kullanıcı ID'si
   * @returns {Promise<User|null>} - Bulunan kullanıcı veya null
   */
  async findById(id) {
    throw new Error('Method not implemented');
  }

  /**
   * Email ile kullanıcı bul
   * @param {string} email - Email adresi
   * @returns {Promise<User|null>} - Bulunan kullanıcı veya null
   */
  async findByEmail(email) {
    throw new Error('Method not implemented');
  }

  /**
   * Username ile kullanıcı bul
   * @param {string} username - <PERSON>llanıcı adı
   * @returns {Promise<User|null>} - Bulunan kullanıcı veya null
   */
  async findByUsername(username) {
    throw new Error('Method not implemented');
  }

  /**
   * Kullanıcı güncelle
   * @param {string} id - Kullanıcı ID'si
   * @param {User} user - Güncellenmiş kullanıcı entity'si
   * @returns {Promise<User>} - Güncellenmiş kullanıcı
   */
  async update(id, user) {
    throw new Error('Method not implemented');
  }

  /**
   * Kullanıcı sil
   * @param {string} id - Kullanıcı ID'si
   * @returns {Promise<boolean>} - Silme işlemi başarılı mı
   */
  async delete(id) {
    throw new Error('Method not implemented');
  }

  /**
   * Tüm kullanıcıları listele (sayfalama ile)
   * @param {Object} options - Sayfalama ve filtreleme seçenekleri
   * @returns {Promise<{users: User[], total: number, page: number, limit: number}>}
   */
  async findAll(options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Role göre kullanıcıları bul
   * @param {string} role - Kullanıcı rolü
   * @param {Object} options - Sayfalama seçenekleri
   * @returns {Promise<{users: User[], total: number}>}
   */
  async findByRole(role, options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Status göre kullanıcıları bul
   * @param {string} status - Kullanıcı durumu
   * @param {Object} options - Sayfalama seçenekleri
   * @returns {Promise<{users: User[], total: number}>}
   */
  async findByStatus(status, options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Onay durumuna göre kullanıcıları bul
   * @param {string} approvalStatus - Onay durumu
   * @param {Object} options - Sayfalama seçenekleri
   * @returns {Promise<{users: User[], total: number}>}
   */
  async findByApprovalStatus(approvalStatus, options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Email doğrulanmamış kullanıcıları bul
   * @param {Object} options - Sayfalama seçenekleri
   * @returns {Promise<{users: User[], total: number}>}
   */
  async findUnverifiedUsers(options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Kullanıcı arama (isim, email, username)
   * @param {string} searchTerm - Arama terimi
   * @param {Object} options - Sayfalama seçenekleri
   * @returns {Promise<{users: User[], total: number}>}
   */
  async search(searchTerm, options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Kullanıcı sayısını al
   * @param {Object} filters - Filtreleme kriterleri
   * @returns {Promise<number>} - Kullanıcı sayısı
   */
  async count(filters = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Email veya username'in benzersiz olup olmadığını kontrol et
   * @param {string} email - Email adresi
   * @param {string} username - Kullanıcı adı
   * @param {string} excludeId - Hariç tutulacak kullanıcı ID'si (güncelleme için)
   * @returns {Promise<{emailExists: boolean, usernameExists: boolean}>}
   */
  async checkUniqueness(email, username, excludeId = null) {
    throw new Error('Method not implemented');
  }

  /**
   * Kullanıcının şifresini güncelle
   * @param {string} id - Kullanıcı ID'si
   * @param {string} hashedPassword - Hash'lenmiş şifre
   * @returns {Promise<boolean>} - Güncelleme başarılı mı
   */
  async updatePassword(id, hashedPassword) {
    throw new Error('Method not implemented');
  }

  /**
   * Email doğrulama durumunu güncelle
   * @param {string} id - Kullanıcı ID'si
   * @param {boolean} isVerified - Doğrulama durumu
   * @returns {Promise<boolean>} - Güncelleme başarılı mı
   */
  async updateEmailVerification(id, isVerified) {
    throw new Error('Method not implemented');
  }

  /**
   * Kullanıcı izinlerini güncelle
   * @param {string} id - Kullanıcı ID'si
   * @param {Object} permissions - İzin bilgileri
   * @returns {Promise<boolean>} - Güncelleme başarılı mı
   */
  async updatePermissions(id, permissions) {
    throw new Error('Method not implemented');
  }

  /**
   * Kullanıcı profilini güncelle
   * @param {string} id - Kullanıcı ID'si
   * @param {Object} profileData - Profil bilgileri
   * @returns {Promise<boolean>} - Güncelleme başarılı mı
   */
  async updateProfile(id, profileData) {
    throw new Error('Method not implemented');
  }

  /**
   * Kullanıcı durumunu toplu güncelle
   * @param {string[]} ids - Kullanıcı ID'leri
   * @param {string} status - Yeni durum
   * @returns {Promise<number>} - Güncellenen kullanıcı sayısı
   */
  async bulkUpdateStatus(ids, status) {
    throw new Error('Method not implemented');
  }

  /**
   * Kullanıcı onay durumunu toplu güncelle
   * @param {string[]} ids - Kullanıcı ID'leri
   * @param {string} approvalStatus - Yeni onay durumu
   * @returns {Promise<number>} - Güncellenen kullanıcı sayısı
   */
  async bulkUpdateApprovalStatus(ids, approvalStatus) {
    throw new Error('Method not implemented');
  }
}

module.exports = IUserRepository;
