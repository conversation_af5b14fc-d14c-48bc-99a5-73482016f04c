/**
 * Main Routes Configuration
 * Configures all application routes
 */

const express = require('express');
const AuthRoutes = require('./AuthRoutes');
const UserRoutes = require('./UserRoutes');
const AuctionRoutes = require('./AuctionRoutes');
const ProductRoutes = require('./ProductRoutes');
const LotteryRoutes = require('./LotteryRoutes');
const CategoryRoutes = require('./CategoryRoutes');
const OrderRoutes = require('./OrderRoutes');
const SiteSettingsRoutes = require('./SiteSettingsRoutes');
const BannerRoutes = require('./BannerRoutes');
const GiveawayRoutes = require('./GiveawayRoutes');
const ExchangeRoutes = require('./ExchangeRoutes');
const { API } = require('../../shared/constants');

class RouteConfiguration {
  constructor(container) {
    this.container = container;
    this.router = express.Router();
    this.setupRoutes();
  }

  setupRoutes() {
    // API versioning
    const apiRouter = express.Router();

    // Health check endpoint
    apiRouter.get('/health', (req, res) => {
      res.json({
        success: true,
        message: 'API is healthy',
        version: API.VERSION,
        timestamp: new Date().toISOString(),
        uptime: process.uptime()
      });
    });

    // Auth routes
    const authRoutes = new AuthRoutes(this.container);
    apiRouter.use('/auth', (req, res, next) => {
      console.log('🔥 /auth route hit:', req.method, req.path);
      next();
    }, authRoutes.getRouter());

    // User routes
    const userRoutes = new UserRoutes(this.container);
    apiRouter.use('/users', userRoutes.getRouter());

    // Auction routes
    const auctionRoutes = new AuctionRoutes(this.container);
    apiRouter.use('/auctions', auctionRoutes.getRouter());

    // Product routes
    const productRoutes = new ProductRoutes(this.container);
    apiRouter.use('/products', productRoutes.getRouter());

    // Lottery routes
    const lotteryRoutes = new LotteryRoutes(this.container);
    apiRouter.use('/lotteries', lotteryRoutes.getRouter());

    // Category routes
    const categoryRoutes = new CategoryRoutes(this.container);
    apiRouter.use('/categories', categoryRoutes.getRouter());

    // Order routes
    const orderRoutes = new OrderRoutes(this.container);
    apiRouter.use('/orders', orderRoutes.getRouter());

    // Site Settings routes
    const siteSettingsRoutes = new SiteSettingsRoutes(this.container);
    apiRouter.use('/site-settings', siteSettingsRoutes.getRouter());

    // Banner routes
    const bannerRoutes = new BannerRoutes(this.container);
    apiRouter.use('/banners', bannerRoutes.getRouter());

    // Giveaway routes
    const giveawayRoutes = new GiveawayRoutes(this.container);
    apiRouter.use('/giveaways', giveawayRoutes.getRouter());

    // Exchange routes
    const exchangeRoutes = new ExchangeRoutes(this.container);
    apiRouter.use('/exchange', exchangeRoutes.getRouter());

    // Mount API routes with version prefix
    this.router.use(`${API.BASE_PATH}/${API.VERSION}`, apiRouter);

    // Legacy routes support (without version)
    this.router.use(API.BASE_PATH, apiRouter);
  }

  getRouter() {
    return this.router;
  }

  /**
   * Setup additional route configurations
   */
  setupAdditionalRoutes() {
    // CORS preflight
    this.router.options('*', (req, res) => {
      res.header('Access-Control-Allow-Origin', '*');
      res.header('Access-Control-Allow-Methods', 'GET,PUT,POST,DELETE,OPTIONS');
      res.header('Access-Control-Allow-Headers', 'Content-Type, Authorization');
      res.sendStatus(200);
    });

    // API documentation route
    this.router.get('/docs', (req, res) => {
      res.json({
        success: true,
        message: 'API Documentation',
        version: API.VERSION,
        endpoints: this.getEndpointsList()
      });
    });
  }

  /**
   * Get list of available endpoints
   */
  getEndpointsList() {
    return {
      users: {
        'POST /api/v1/users/register': 'User registration',
        'POST /api/v1/users/login': 'User login',
        'POST /api/v1/users/logout': 'User logout',
        'GET /api/v1/users/profile': 'Get user profile (auth required)',
        'PUT /api/v1/users/profile': 'Update user profile (auth required)',
        'POST /api/v1/users/change-password': 'Change password (auth required)',
        'GET /api/v1/users': 'List users (admin only)',
        'GET /api/v1/users/:id': 'Get user by ID',
        'POST /api/v1/users/:id/approve': 'Approve user (admin only)',
        'POST /api/v1/users/:id/reject': 'Reject user (admin only)',
        'PUT /api/v1/users/:id/permissions': 'Update user permissions (admin only)'
      },
      auctions: {
        'GET /api/v1/auctions': 'Get all auctions',
        'GET /api/v1/auctions/active': 'Get active auctions',
        'GET /api/v1/auctions/ending-soon': 'Get ending soon auctions',
        'GET /api/v1/auctions/:id': 'Get auction by ID',
        'POST /api/v1/auctions': 'Create auction (seller/admin)',
        'PUT /api/v1/auctions/:id': 'Update auction (owner/admin)',
        'DELETE /api/v1/auctions/:id': 'Delete auction (owner/admin)',
        'POST /api/v1/auctions/:id/bid': 'Place bid (auth + permission)',
        'GET /api/v1/auctions/:id/bids': 'Get auction bids',
        'GET /api/v1/auctions/user/my-auctions': 'Get user auctions (seller)',
        'GET /api/v1/auctions/user/my-bids': 'Get user bids',
        'PUT /api/v1/auctions/:id/status': 'Update auction status (admin)',
        'GET /api/v1/auctions/admin/counts': 'Get auction counts (admin)'
      },
      products: {
        'GET /api/v1/products': 'Get all products',
        'GET /api/v1/products/:id': 'Get product by ID',
        'GET /api/v1/products/category/:categoryId': 'Get products by category',
        'GET /api/v1/products/featured/list': 'Get featured products',
        'GET /api/v1/products/:id/related': 'Get related products',
        'GET /api/v1/products/search/query': 'Search products',
        'POST /api/v1/products': 'Create product (seller/admin)',
        'PUT /api/v1/products/:id': 'Update product (owner/admin)',
        'DELETE /api/v1/products/:id': 'Delete product (owner/admin)',
        'PUT /api/v1/products/:id/inventory': 'Update inventory (owner/admin)',
        'GET /api/v1/products/user/my-products': 'Get user products (seller)',
        'POST /api/v1/products/:id/approve': 'Approve product (admin)',
        'POST /api/v1/products/:id/reject': 'Reject product (admin)',
        'PUT /api/v1/products/:id/status': 'Update product status (admin)',
        'GET /api/v1/products/admin/counts': 'Get product counts (admin)'
      },
      lotteries: {
        'GET /api/v1/lotteries': 'Get all lotteries',
        'GET /api/v1/lotteries/active': 'Get active lotteries',
        'GET /api/v1/lotteries/ending-soon': 'Get ending soon lotteries',
        'GET /api/v1/lotteries/:id': 'Get lottery by ID',
        'GET /api/v1/lotteries/:id/tickets': 'Get lottery tickets',
        'POST /api/v1/lotteries/:id/purchase': 'Purchase ticket (lottery permission)',
        'GET /api/v1/lotteries/user/my-tickets': 'Get user tickets',
        'POST /api/v1/lotteries': 'Create lottery (admin)',
        'PUT /api/v1/lotteries/:id': 'Update lottery (admin)',
        'DELETE /api/v1/lotteries/:id': 'Delete lottery (admin)',
        'POST /api/v1/lotteries/:id/start': 'Start lottery (admin)',
        'POST /api/v1/lotteries/:id/end': 'End lottery (admin)',
        'POST /api/v1/lotteries/:id/cancel': 'Cancel lottery (admin)',
        'GET /api/v1/lotteries/admin/counts': 'Get lottery counts (admin)'
      },
      categories: {
        'GET /api/v1/categories': 'Get all categories',
        'GET /api/v1/categories/:id': 'Get category by ID',
        'GET /api/v1/categories/slug/:slug': 'Get category by slug',
        'GET /api/v1/categories/active/list': 'Get active categories',
        'GET /api/v1/categories/parent/:parentId': 'Get categories by parent',
        'GET /api/v1/categories/root/list': 'Get root categories',
        'GET /api/v1/categories/tree/structure': 'Get category tree',
        'GET /api/v1/categories/:id/hierarchy': 'Get category hierarchy',
        'GET /api/v1/categories/featured/list': 'Get featured categories',
        'GET /api/v1/categories/with-counts/list': 'Get categories with product counts',
        'GET /api/v1/categories/search/query': 'Search categories',
        'POST /api/v1/categories': 'Create category (admin)',
        'PUT /api/v1/categories/:id': 'Update category (admin)',
        'DELETE /api/v1/categories/:id': 'Delete category (admin)',
        'PUT /api/v1/categories/:id/status': 'Update category status (admin)',
        'POST /api/v1/categories/reorder': 'Reorder categories (admin)',
        'PUT /api/v1/categories/:id/move': 'Move category to new parent (admin)',
        'POST /api/v1/categories/bulk-update': 'Bulk update categories (admin)',
        'POST /api/v1/categories/update-counts': 'Update all product counts (admin)'
      },
      orders: {
        'GET /api/v1/orders/:id': 'Get order by ID (owner/admin)',
        'GET /api/v1/orders/number/:orderNumber': 'Get order by order number (owner/admin)',
        'POST /api/v1/orders': 'Create order (authenticated)',
        'PUT /api/v1/orders/:id': 'Update order (owner/admin, if editable)',
        'POST /api/v1/orders/:id/cancel': 'Cancel order (owner/admin)',
        'GET /api/v1/orders/user/my-orders': 'Get user orders',
        'GET /api/v1/orders/:id/statistics': 'Get order statistics (owner/admin)',
        'GET /api/v1/orders/seller/my-orders': 'Get seller orders (seller/admin)',
        'PUT /api/v1/orders/:id/status': 'Update order status (seller/admin)',
        'POST /api/v1/orders/:id/tracking': 'Add tracking info (seller/admin)',
        'GET /api/v1/orders/admin/all': 'Get all orders (admin)',
        'GET /api/v1/orders/admin/status/:status': 'Get orders by status (admin)',
        'PUT /api/v1/orders/:id/payment-status': 'Update payment status (admin)',
        'GET /api/v1/orders/admin/counts': 'Get order counts (admin)',
        'GET /api/v1/orders/admin/revenue': 'Get revenue statistics (admin)',
        'GET /api/v1/orders/admin/search': 'Search orders (admin)'
      },
      system: {
        'GET /api/health': 'Health check',
        'GET /docs': 'API documentation'
      }
    };
  }
}

module.exports = RouteConfiguration;
