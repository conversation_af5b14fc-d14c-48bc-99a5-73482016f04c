/**
 * Order Routes Configuration
 * Defines all order-related API endpoints
 */

const express = require('express');
const ErrorMiddleware = require('../middleware/ErrorMiddleware');

class OrderRoutes {
  constructor(container) {
    this.container = container;
    this.router = express.Router();
    this.setupRoutes();
  }

  setupRoutes() {
    const orderController = this.container.resolve('orderController');
    const authMiddleware = this.container.resolve('authMiddleware');

    // Public routes (authentication required for all order operations)

    // Get order by ID (user can view their own orders, admin can view all)
    this.router.get('/:id',
      authMiddleware.authenticate(),
      ErrorMiddleware.asyncHandler(orderController.getOrderById.bind(orderController))
    );

    // Get order by order number (user can view their own orders, admin can view all)
    this.router.get('/number/:orderNumber',
      authMiddleware.authenticate(),
      ErrorMiddleware.asyncHandler(orderController.getOrderByOrderNumber.bind(orderController))
    );

    // Create new order (authenticated users only)
    this.router.post('/',
      authMiddleware.authenticate(),
      ErrorMiddleware.asyncHandler(orderController.createOrder.bind(orderController))
    );

    // Update order (owner or admin only, if editable)
    this.router.put('/:id',
      authMiddleware.authenticate(),
      ErrorMiddleware.asyncHandler(orderController.updateOrder.bind(orderController))
    );

    // Cancel order (owner or admin only)
    this.router.post('/:id/cancel',
      authMiddleware.authenticate(),
      ErrorMiddleware.asyncHandler(orderController.cancelOrder.bind(orderController))
    );

    // Get user's own orders
    this.router.get('/user/my-orders',
      authMiddleware.authenticate(),
      ErrorMiddleware.asyncHandler(orderController.getUserOrders.bind(orderController))
    );

    // Get order statistics (owner or admin only)
    this.router.get('/:id/statistics',
      authMiddleware.authenticate(),
      ErrorMiddleware.asyncHandler(orderController.getOrderStatistics.bind(orderController))
    );

    // Seller routes (seller or admin only)

    // Get seller's orders
    this.router.get('/seller/my-orders',
      authMiddleware.authenticate(),
      authMiddleware.requireSeller(),
      ErrorMiddleware.asyncHandler(orderController.getSellerOrders.bind(orderController))
    );

    // Update order status (seller for their items or admin)
    this.router.put('/:id/status',
      authMiddleware.authenticate(),
      authMiddleware.requireRoles(['admin', 'seller']),
      ErrorMiddleware.asyncHandler(orderController.updateOrderStatus.bind(orderController))
    );

    // Add tracking information (seller for their items or admin)
    this.router.post('/:id/tracking',
      authMiddleware.authenticate(),
      authMiddleware.requireRoles(['admin', 'seller']),
      ErrorMiddleware.asyncHandler(orderController.addTrackingInfo.bind(orderController))
    );

    // Admin routes (admin only)

    // Get all orders
    this.router.get('/admin/all',
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(orderController.getAllOrders.bind(orderController))
    );

    // Get orders by status
    this.router.get('/admin/status/:status',
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(orderController.getOrdersByStatus.bind(orderController))
    );

    // Update payment status
    this.router.put('/:id/payment-status',
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(orderController.updatePaymentStatus.bind(orderController))
    );

    // Get order counts by status
    this.router.get('/admin/counts',
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(orderController.getOrderCounts.bind(orderController))
    );

    // Get revenue statistics
    this.router.get('/admin/revenue',
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(orderController.getRevenueStatistics.bind(orderController))
    );

    // Search orders
    this.router.get('/admin/search',
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(orderController.searchOrders.bind(orderController))
    );
  }

  getRouter() {
    return this.router;
  }
}

module.exports = OrderRoutes;
