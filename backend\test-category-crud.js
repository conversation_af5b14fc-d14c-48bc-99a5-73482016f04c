const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:5000/api';
let adminToken = '';

async function testCategoryCRUD() {
  console.log('🚀 Testing Category CRUD Operations...\n');

  try {
    // 1. Login to get admin token
    console.log('1️⃣ Logging in as admin...');
    const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'eEf8kbpE9G8XhiL'
      })
    });

    const loginData = await loginResponse.json();
    adminToken = loginData.data.token;
    console.log('✅ Admin login successful\n');

    // 2. List existing categories
    console.log('2️⃣ Listing existing categories...');
    const listResponse = await fetch(`${BASE_URL}/categories`);
    const listData = await listResponse.json();
    const existingCategories = listData.data.categories;

    console.log(`📂 Found ${existingCategories.length} existing categories:`);
    existingCategories.forEach((cat, index) => {
      console.log(`  ${index + 1}. ${cat.name} (${cat.slug}) - ID: ${cat.id}`);
    });
    console.log('');

    // 3. Clean up any existing test category first
    console.log('3️⃣ Cleaning up existing test categories...');
    const existingTestCategory = existingCategories.find(cat => cat.slug === 'test-crud-kategori-new');
    if (existingTestCategory && existingTestCategory.id) {
      try {
        await fetch(`${BASE_URL}/categories/${existingTestCategory.id}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${adminToken}`,
            'Content-Type': 'application/json'
          }
        });
        console.log('🧹 Cleaned up existing test category');
      } catch (error) {
        console.log('🧹 No existing test category to clean up');
      }
    }

    // 4. CREATE - Add new category
    console.log('4️⃣ Testing CREATE - Adding new category...');
    const newCategory = {
      name: 'Test CRUD Kategori New',
      slug: 'test-crud-kategori-new',
      description: 'CRUD test için oluşturulan kategori',
      shortDescription: 'CRUD test kategorisi',
      isActive: true,
      isFeatured: false
    };

    const createResponse = await fetch(`${BASE_URL}/categories`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${adminToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(newCategory)
    });

    const createData = await createResponse.json();
    if (createResponse.ok) {
      const createdCategory = createData.data.category;
      console.log(`✅ CREATE successful - Category created with ID: ${createdCategory.id}`);
      console.log(`   Name: ${createdCategory.name}`);
      console.log(`   Slug: ${createdCategory.slug}`);
      console.log(`   Active: ${createdCategory.isActive}`);
      console.log('');

      // 5. READ - Get category by ID
      console.log('5️⃣ Testing READ - Getting category by ID...');
      const readResponse = await fetch(`${BASE_URL}/categories/${createdCategory.id}`);
      const readData = await readResponse.json();

      if (readResponse.ok) {
        const fetchedCategory = readData.data.category;
        console.log(`✅ READ successful - Category fetched:`);
        console.log(`   ID: ${fetchedCategory.id}`);
        console.log(`   Name: ${fetchedCategory.name}`);
        console.log(`   Description: ${fetchedCategory.description}`);
        console.log(`   Active: ${fetchedCategory.isActive}`);
        console.log('');

        // 6. UPDATE - Edit category
        console.log('6️⃣ Testing UPDATE - Editing category...');
        const updateData = {
          name: 'Test CRUD Kategori (Güncellenmiş)',
          description: 'CRUD test için oluşturulan ve güncellenmiş kategori',
          shortDescription: 'Güncellenmiş CRUD test kategorisi',
          isActive: false,
          isFeatured: true
        };

        const updateResponse = await fetch(`${BASE_URL}/categories/${createdCategory.id}`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${adminToken}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(updateData)
        });

        const updatedData = await updateResponse.json();
        if (updateResponse.ok) {
          const updatedCategory = updatedData.data.category;
          console.log(`✅ UPDATE successful - Category updated:`);
          console.log(`   Name: ${updatedCategory.name}`);
          console.log(`   Description: ${updatedCategory.description}`);
          console.log(`   Active: ${updatedCategory.isActive}`);
          console.log(`   Featured: ${updatedCategory.isFeatured}`);
          console.log('');

          // 7. Verify update by reading again
          console.log('7️⃣ Verifying update by reading again...');
          const verifyResponse = await fetch(`${BASE_URL}/categories/${createdCategory.id}`);
          const verifyData = await verifyResponse.json();

          if (verifyResponse.ok) {
            const verifiedCategory = verifyData.data.category;
            console.log(`✅ VERIFY successful - Changes confirmed:`);
            console.log(`   Name: ${verifiedCategory.name}`);
            console.log(`   Active: ${verifiedCategory.isActive}`);
            console.log(`   Featured: ${verifiedCategory.isFeatured}`);
            console.log('');
          } else {
            console.log(`❌ VERIFY failed:`, verifyData.message);
          }

          // 7. DELETE - Remove category
          console.log('7️⃣ Testing DELETE - Removing category...');
          const deleteResponse = await fetch(`${BASE_URL}/categories/${createdCategory.id}`, {
            method: 'DELETE',
            headers: {
              'Authorization': `Bearer ${adminToken}`,
              'Content-Type': 'application/json'
            }
          });

          const deleteData = await deleteResponse.json();
          if (deleteResponse.ok) {
            console.log(`✅ DELETE successful - Category removed`);
            console.log('');

            // 8. Verify deletion by trying to read
            console.log('8️⃣ Verifying deletion by trying to read...');
            const verifyDeleteResponse = await fetch(`${BASE_URL}/categories/${createdCategory.id}`);

            if (verifyDeleteResponse.status === 404) {
              console.log(`✅ DELETE VERIFY successful - Category not found (as expected)`);
            } else {
              const verifyDeleteData = await verifyDeleteResponse.json();
              console.log(`❌ DELETE VERIFY failed - Category still exists:`, verifyDeleteData);
            }
          } else {
            console.log(`❌ DELETE failed:`, deleteData.message);
          }

        } else {
          console.log(`❌ UPDATE failed:`, updatedData.message);
        }

      } else {
        console.log(`❌ READ failed:`, readData.message);
      }

    } else {
      console.log(`❌ CREATE failed:`, createData.message);
    }

    // 9. Test error cases
    console.log('\n9️⃣ Testing error cases...');

    // Test duplicate slug
    console.log('Testing duplicate slug...');
    const duplicateCategory = {
      name: 'Duplicate Test',
      slug: 'elektronik', // This slug already exists
      description: 'Test duplicate slug',
      isActive: true
    };

    const duplicateResponse = await fetch(`${BASE_URL}/categories`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${adminToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(duplicateCategory)
    });

    const duplicateData = await duplicateResponse.json();
    if (!duplicateResponse.ok) {
      console.log(`✅ Duplicate slug validation working - Error: ${duplicateData.message}`);
    } else {
      console.log(`❌ Duplicate slug validation failed - Should have been rejected`);
    }

    // Test invalid category ID
    console.log('Testing invalid category ID...');
    const invalidResponse = await fetch(`${BASE_URL}/categories/invalid-id`);
    if (invalidResponse.status === 404 || invalidResponse.status === 400) {
      console.log(`✅ Invalid ID validation working - Status: ${invalidResponse.status}`);
    } else {
      console.log(`❌ Invalid ID validation failed - Status: ${invalidResponse.status}`);
    }

    // 10. Final category list
    console.log('\n🔟 Final category list...');
    const finalListResponse = await fetch(`${BASE_URL}/categories`);
    const finalListData = await finalListResponse.json();
    const finalCategories = finalListData.data.categories;

    console.log(`📂 Final count: ${finalCategories.length} categories`);
    finalCategories.forEach((cat, index) => {
      console.log(`  ${index + 1}. ${cat.name} (${cat.slug}) - Active: ${cat.isActive}`);
    });

    console.log('\n🎉 Category CRUD testing completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

testCategoryCRUD();
