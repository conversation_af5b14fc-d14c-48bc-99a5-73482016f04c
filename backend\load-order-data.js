const mongoose = require('mongoose');
require('dotenv').config();

async function loadOrderData() {
  try {
    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb+srv://emrevaryemez22:<EMAIL>/robinhoot?retryWrites=true&w=majority');
    console.log('✅ Connected to database');

    // Get database
    const db = mongoose.connection.db;

    // Get existing users and products
    const users = await db.collection('users').find({}).toArray();
    const products = await db.collection('products').find({}).toArray();

    console.log(`📊 Found ${users.length} users and ${products.length} products`);

    // Get admin and regular users
    const adminUser = users.find(user => user.role === 'admin');
    const regularUsers = users.filter(user => user.role === 'user');

    if (!adminUser) {
      console.error('❌ Admin user not found');
      return;
    }

    if (regularUsers.length === 0) {
      console.error('❌ No regular users found');
      return;
    }

    if (products.length === 0) {
      console.error('❌ No products found');
      return;
    }

    console.log(`👤 Admin: ${adminUser.name} (${adminUser.email})`);
    console.log(`👥 Regular users: ${regularUsers.length}`);
    console.log(`📦 Products: ${products.length}`);

    // Create sample orders
    const sampleOrders = [
      {
        orderNumber: `ORD-${Date.now()}-A001`,
        userId: regularUsers[0]._id,
        items: [
          {
            productId: products[0]._id,
            sellerId: products[0].sellerId,
            name: products[0].name,
            price: products[0].price,
            quantity: 2,
            total: products[0].price * 2
          },
          {
            productId: products[1]._id,
            sellerId: products[1].sellerId,
            name: products[1].name,
            price: products[1].price,
            quantity: 1,
            total: products[1].price * 1
          }
        ],
        totalAmount: (products[0].price * 2) + (products[1].price * 1) + 50,
        shippingCost: 50,
        taxAmount: 0,
        discountAmount: 0,
        currency: 'TRY',
        status: 'pending',
        paymentStatus: 'pending',
        shippingStatus: 'pending',
        shippingAddress: {
          firstName: 'Ahmet',
          lastName: 'Yılmaz',
          company: '',
          address1: 'Atatürk Caddesi No: 123',
          address2: 'Daire 5',
          city: 'İstanbul',
          state: 'İstanbul',
          postalCode: '34000',
          country: 'Turkey',
          phone: '+90 ************'
        },
        paymentMethod: {
          type: 'credit_card',
          provider: 'stripe',
          last4: '4242',
          brand: 'visa'
        },
        notes: 'Lütfen kapıcıya teslim edin',
        source: 'web',
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        orderNumber: `ORD-${Date.now()}-B002`,
        userId: regularUsers.length > 1 ? regularUsers[1]._id : regularUsers[0]._id,
        items: [
          {
            productId: products[2] ? products[2]._id : products[0]._id,
            sellerId: products[2] ? products[2].sellerId : products[0].sellerId,
            name: products[2] ? products[2].name : products[0].name,
            price: products[2] ? products[2].price : products[0].price,
            quantity: 1,
            total: products[2] ? products[2].price : products[0].price
          }
        ],
        totalAmount: (products[2] ? products[2].price : products[0].price) + 30 - 100,
        shippingCost: 30,
        taxAmount: 0,
        discountAmount: 100,
        currency: 'TRY',
        status: 'confirmed',
        paymentStatus: 'completed',
        shippingStatus: 'preparing',
        shippingAddress: {
          firstName: 'Fatma',
          lastName: 'Demir',
          company: 'ABC Şirketi',
          address1: 'Cumhuriyet Bulvarı No: 456',
          address2: '',
          city: 'Ankara',
          state: 'Ankara',
          postalCode: '06000',
          country: 'Turkey',
          phone: '+90 ************'
        },
        paymentMethod: {
          type: 'credit_card',
          provider: 'iyzico',
          last4: '1234',
          brand: 'mastercard'
        },
        notes: 'Hızlı teslimat istiyorum',
        couponCode: 'WELCOME100',
        source: 'mobile',
        createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
        updatedAt: new Date()
      },
      {
        orderNumber: `ORD-${Date.now()}-C003`,
        userId: adminUser._id,
        items: [
          {
            productId: products[3] ? products[3]._id : products[0]._id,
            sellerId: products[3] ? products[3].sellerId : products[0].sellerId,
            name: products[3] ? products[3].name : products[0].name,
            price: products[3] ? products[3].price : products[0].price,
            quantity: 3,
            total: (products[3] ? products[3].price : products[0].price) * 3
          }
        ],
        totalAmount: (products[3] ? products[3].price : products[0].price) * 3,
        shippingCost: 0, // Free shipping
        taxAmount: 0,
        discountAmount: 0,
        currency: 'TRY',
        status: 'shipped',
        paymentStatus: 'completed',
        shippingStatus: 'shipped',
        shippingAddress: {
          firstName: 'Admin',
          lastName: 'User',
          company: 'RobinHoot',
          address1: 'Admin Caddesi No: 1',
          address2: 'Admin Ofisi',
          city: 'İzmir',
          state: 'İzmir',
          postalCode: '35000',
          country: 'Turkey',
          phone: '+90 ************'
        },
        paymentMethod: {
          type: 'bank_transfer',
          provider: 'manual',
          reference: 'TRF123456789'
        },
        notes: 'Admin test siparişi',
        trackingNumber: 'TRK123456789',
        carrier: 'Aras Kargo',
        shippedAt: new Date(Date.now() - 12 * 60 * 60 * 1000), // 12 hours ago
        estimatedDelivery: new Date(Date.now() + 24 * 60 * 60 * 1000), // 1 day from now
        source: 'admin',
        createdAt: new Date(Date.now() - 48 * 60 * 60 * 1000), // 2 days ago
        updatedAt: new Date()
      }
    ];

    console.log('\n📦 Creating sample orders...');

    for (let i = 0; i < sampleOrders.length; i++) {
      const orderData = sampleOrders[i];
      console.log(`\n${i + 1}. Creating order: ${orderData.orderNumber}`);
      console.log(`   User: ${orderData.userId}`);
      console.log(`   Items: ${orderData.items.length}`);
      console.log(`   Total: ${orderData.total} ${orderData.currency}`);
      console.log(`   Status: ${orderData.status}`);

      try {
        const result = await db.collection('orders').insertOne(orderData);
        console.log(`   ✅ Created with ID: ${result.insertedId}`);
      } catch (error) {
        console.log(`   ❌ Failed to create: ${error.message}`);
      }
    }

    // Verify created orders
    console.log('\n🔍 Verifying created orders...');
    const orders = await db.collection('orders').find({}).toArray();
    console.log(`📊 Total orders in database: ${orders.length}`);

    orders.forEach((order, index) => {
      console.log(`  ${index + 1}. ${order.orderNumber} - ${order.status} - ${order.totalAmount} ${order.currency}`);
    });

    await mongoose.disconnect();
    console.log('\n✅ Disconnected from database');
    console.log('🎉 Order data loading completed!');

  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

loadOrderData();
