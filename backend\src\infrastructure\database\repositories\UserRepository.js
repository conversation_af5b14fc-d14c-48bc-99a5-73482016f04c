/**
 * User Repository Implementation
 * Infrastructure layer - Database operations for User entity
 */

const IUserRepository = require('../../../core/interfaces/IUserRepository');
const User = require('../models/User');
const UserEntity = require('../../../core/entities/User');
const { UserNotFoundException, DatabaseException, ConflictException } = require('../../../core/exceptions');
const { PAGINATION } = require('../../../shared/constants');

class UserRepository extends IUserRepository {
  /**
   * Kullanıcı oluştur
   */
  async create(userEntity) {
    try {
      const userData = userEntity.toPersistence();
      const user = new User(userData);
      const savedUser = await user.save();
      return UserEntity.fromPersistence(savedUser.toObject());
    } catch (error) {
      if (error.code === 11000) {
        const field = Object.keys(error.keyPattern)[0];
        throw new ConflictException(`User with this ${field} already exists`);
      }
      throw new DatabaseException(error.message);
    }
  }

  /**
   * ID ile kullanıcı bul
   */
  async findById(id) {
    try {
      const user = await User.findById(id).select('+password');
      return user ? UserEntity.fromPersistence(user.toObject()) : null;
    } catch (error) {
      throw new DatabaseException(error.message);
    }
  }

  /**
   * Email ile kullanıcı bul
   */
  async findByEmail(email) {
    try {
      const user = await User.findOne({ email }).select('+password');
      return user ? UserEntity.fromPersistence(user.toObject()) : null;
    } catch (error) {
      throw new DatabaseException(error.message);
    }
  }

  /**
   * Username ile kullanıcı bul
   */
  async findByUsername(username) {
    try {
      const user = await User.findOne({ username }).select('+password');
      return user ? UserEntity.fromPersistence(user.toObject()) : null;
    } catch (error) {
      throw new DatabaseException(error.message);
    }
  }

  /**
   * Kullanıcı güncelle
   */
  async update(id, userEntity) {
    try {
      const userData = userEntity.toPersistence();
      const user = await User.findByIdAndUpdate(
        id,
        { $set: userData },
        { new: true, runValidators: true }
      );
      
      if (!user) {
        throw new UserNotFoundException(id);
      }
      
      return UserEntity.fromPersistence(user.toObject());
    } catch (error) {
      if (error instanceof UserNotFoundException) {
        throw error;
      }
      throw new DatabaseException(error.message);
    }
  }

  /**
   * Kullanıcı sil
   */
  async delete(id) {
    try {
      const result = await User.findByIdAndDelete(id);
      return !!result;
    } catch (error) {
      throw new DatabaseException(error.message);
    }
  }

  /**
   * Tüm kullanıcıları listele
   */
  async findAll(options = {}) {
    try {
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        sort = { createdAt: -1 },
        filters = {}
      } = options;

      const skip = (page - 1) * limit;
      
      const [users, total] = await Promise.all([
        User.find(filters)
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .lean(),
        User.countDocuments(filters)
      ]);

      return {
        users: users.map(user => UserEntity.fromPersistence(user)),
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      };
    } catch (error) {
      throw new DatabaseException(error.message);
    }
  }

  /**
   * Role göre kullanıcıları bul
   */
  async findByRole(role, options = {}) {
    try {
      const filters = { role, ...options.filters };
      return await this.findAll({ ...options, filters });
    } catch (error) {
      throw new DatabaseException(error.message);
    }
  }

  /**
   * Status göre kullanıcıları bul
   */
  async findByStatus(status, options = {}) {
    try {
      const filters = { status, ...options.filters };
      return await this.findAll({ ...options, filters });
    } catch (error) {
      throw new DatabaseException(error.message);
    }
  }

  /**
   * Onay durumuna göre kullanıcıları bul
   */
  async findByApprovalStatus(approvalStatus, options = {}) {
    try {
      const filters = { approvalStatus, ...options.filters };
      return await this.findAll({ ...options, filters });
    } catch (error) {
      throw new DatabaseException(error.message);
    }
  }

  /**
   * Email doğrulanmamış kullanıcıları bul
   */
  async findUnverifiedUsers(options = {}) {
    try {
      const filters = { isEmailVerified: false, ...options.filters };
      return await this.findAll({ ...options, filters });
    } catch (error) {
      throw new DatabaseException(error.message);
    }
  }

  /**
   * Kullanıcı arama
   */
  async search(searchTerm, options = {}) {
    try {
      const searchRegex = new RegExp(searchTerm, 'i');
      const filters = {
        $or: [
          { name: searchRegex },
          { email: searchRegex },
          { username: searchRegex }
        ],
        ...options.filters
      };
      return await this.findAll({ ...options, filters });
    } catch (error) {
      throw new DatabaseException(error.message);
    }
  }

  /**
   * Kullanıcı sayısını al
   */
  async count(filters = {}) {
    try {
      return await User.countDocuments(filters);
    } catch (error) {
      throw new DatabaseException(error.message);
    }
  }

  /**
   * Email veya username benzersizlik kontrolü
   */
  async checkUniqueness(email, username, excludeId = null) {
    try {
      const emailQuery = { email };
      const usernameQuery = { username };
      
      if (excludeId) {
        emailQuery._id = { $ne: excludeId };
        usernameQuery._id = { $ne: excludeId };
      }

      const [emailExists, usernameExists] = await Promise.all([
        User.exists(emailQuery),
        User.exists(usernameQuery)
      ]);

      return {
        emailExists: !!emailExists,
        usernameExists: !!usernameExists
      };
    } catch (error) {
      throw new DatabaseException(error.message);
    }
  }

  /**
   * Şifre güncelle
   */
  async updatePassword(id, hashedPassword) {
    try {
      const result = await User.findByIdAndUpdate(
        id,
        { password: hashedPassword },
        { new: true }
      );
      return !!result;
    } catch (error) {
      throw new DatabaseException(error.message);
    }
  }

  /**
   * Email doğrulama durumunu güncelle
   */
  async updateEmailVerification(id, isVerified) {
    try {
      const result = await User.findByIdAndUpdate(
        id,
        { isEmailVerified: isVerified },
        { new: true }
      );
      return !!result;
    } catch (error) {
      throw new DatabaseException(error.message);
    }
  }

  /**
   * Kullanıcı izinlerini güncelle
   */
  async updatePermissions(id, permissions) {
    try {
      const result = await User.findByIdAndUpdate(
        id,
        { $set: permissions },
        { new: true }
      );
      return !!result;
    } catch (error) {
      throw new DatabaseException(error.message);
    }
  }

  /**
   * Kullanıcı profilini güncelle
   */
  async updateProfile(id, profileData) {
    try {
      const result = await User.findByIdAndUpdate(
        id,
        { $set: { profile: profileData } },
        { new: true }
      );
      return !!result;
    } catch (error) {
      throw new DatabaseException(error.message);
    }
  }

  /**
   * Kullanıcı durumunu toplu güncelle
   */
  async bulkUpdateStatus(ids, status) {
    try {
      const result = await User.updateMany(
        { _id: { $in: ids } },
        { $set: { status } }
      );
      return result.modifiedCount;
    } catch (error) {
      throw new DatabaseException(error.message);
    }
  }

  /**
   * Kullanıcı onay durumunu toplu güncelle
   */
  async bulkUpdateApprovalStatus(ids, approvalStatus) {
    try {
      const result = await User.updateMany(
        { _id: { $in: ids } },
        { $set: { approvalStatus } }
      );
      return result.modifiedCount;
    } catch (error) {
      throw new DatabaseException(error.message);
    }
  }
}

module.exports = UserRepository;
