/**
 * Login DTO
 * Data Transfer Object for user authentication
 */

const { ValidationException } = require('../../../core/exceptions');

class LoginDto {
  constructor(data) {
    this.emailOrUsername = data.emailOrUsername?.toLowerCase().trim();
    this.password = data.password;

    this.validate();
  }

  validate() {
    const errors = [];

    // Email or username validation
    if (!this.emailOrUsername) {
      errors.push('Email or username is required');
    } else if (this.emailOrUsername.length < 3) {
      errors.push('Email or username must be at least 3 characters');
    }

    // Password validation
    if (!this.password) {
      errors.push('Password is required');
    } else if (this.password.length < 6) {
      errors.push('Password must be at least 6 characters');
    }

    if (errors.length > 0) {
      throw new ValidationException('Validation failed', { errors });
    }
  }
}

module.exports = LoginDto;
