const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

async function createAdminUser() {
  try {
    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb+srv://emrevaryemez22:<EMAIL>/robinhoot?retryWrites=true&w=majority');
    console.log('✅ Connected to database');

    // Hash password
    const hashedPassword = await bcrypt.hash('eEf8kbpE9G8XhiL', 12);
    console.log('🔐 Password hashed');

    // Get database
    const db = mongoose.connection.db;

    // Create admin user
    const adminUser = {
      name: 'Admin User',
      email: '<EMAIL>',
      username: 'admin',
      password: hashedPassword,
      role: 'admin',
      status: 'active',
      approvalStatus: 'approved',
      isEmailVerified: true,
      canBid: true,
      canParticipateInLottery: true,
      canSell: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Insert user
    const result = await db.collection('users').insertOne(adminUser);
    console.log('👤 Admin user created with ID:', result.insertedId);

    // Verify user was created
    const createdUser = await db.collection('users').findOne({ email: '<EMAIL>' });
    if (createdUser) {
      console.log('✅ User verification successful');
      console.log('Email:', createdUser.email);
      console.log('Role:', createdUser.role);
      console.log('Status:', createdUser.status);
      console.log('ApprovalStatus:', createdUser.approvalStatus);
    }

    // Test password
    const isPasswordValid = await bcrypt.compare('eEf8kbpE9G8XhiL', createdUser.password);
    console.log('🔐 Password test:', isPasswordValid);

    await mongoose.disconnect();
    console.log('✅ Disconnected from database');

  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

createAdminUser();
