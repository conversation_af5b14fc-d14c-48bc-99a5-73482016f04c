/**
 * Category Repository Interface
 * Defines contract for category data operations
 */

class ICategoryRepository {
  /**
   * Create a new category
   * @param {Object} categoryData - Category data
   * @returns {Promise<Object>} Created category
   */
  async create(categoryData) {
    throw new Error('Method not implemented');
  }

  /**
   * Find category by ID
   * @param {string} id - Category ID
   * @returns {Promise<Object|null>} Category or null
   */
  async findById(id) {
    throw new Error('Method not implemented');
  }

  /**
   * Find category by slug
   * @param {string} slug - Category slug
   * @returns {Promise<Object|null>} Category or null
   */
  async findBySlug(slug) {
    throw new Error('Method not implemented');
  }

  /**
   * Find all categories
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Paginated categories
   */
  async findAll(options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Find active categories
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Active categories
   */
  async findActive(options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Find categories by parent
   * @param {string} parentId - Parent category ID
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Child categories
   */
  async findByParent(parentId, options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Find root categories (no parent)
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Root categories
   */
  async findRootCategories(options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Search categories
   * @param {string} query - Search query
   * @param {Object} options - Query options
   * @returns {Promise<Object>} Search results
   */
  async search(query, options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Update category
   * @param {string} id - Category ID
   * @param {Object} updateData - Update data
   * @returns {Promise<Object>} Updated category
   */
  async update(id, updateData) {
    throw new Error('Method not implemented');
  }

  /**
   * Delete category
   * @param {string} id - Category ID
   * @returns {Promise<boolean>} Success status
   */
  async delete(id) {
    throw new Error('Method not implemented');
  }

  /**
   * Update category status
   * @param {string} id - Category ID
   * @param {boolean} isActive - Active status
   * @returns {Promise<Object>} Updated category
   */
  async updateStatus(id, isActive) {
    throw new Error('Method not implemented');
  }

  /**
   * Get category hierarchy
   * @param {string} id - Category ID
   * @returns {Promise<Array>} Category hierarchy path
   */
  async getHierarchy(id) {
    throw new Error('Method not implemented');
  }

  /**
   * Get category tree
   * @param {string} rootId - Root category ID (optional)
   * @returns {Promise<Array>} Category tree structure
   */
  async getCategoryTree(rootId = null) {
    throw new Error('Method not implemented');
  }

  /**
   * Get categories with product counts
   * @param {Object} options - Query options
   * @returns {Promise<Array>} Categories with product counts
   */
  async findWithProductCounts(options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Check if category has children
   * @param {string} id - Category ID
   * @returns {Promise<boolean>} Has children status
   */
  async hasChildren(id) {
    throw new Error('Method not implemented');
  }

  /**
   * Check if category has products
   * @param {string} id - Category ID
   * @returns {Promise<boolean>} Has products status
   */
  async hasProducts(id) {
    throw new Error('Method not implemented');
  }

  /**
   * Get category statistics
   * @param {string} id - Category ID
   * @returns {Promise<Object>} Category statistics
   */
  async getStatistics(id) {
    throw new Error('Method not implemented');
  }

  /**
   * Reorder categories
   * @param {Array} orderData - Array of {id, order} objects
   * @returns {Promise<boolean>} Success status
   */
  async reorder(orderData) {
    throw new Error('Method not implemented');
  }

  /**
   * Move category to new parent
   * @param {string} id - Category ID
   * @param {string} newParentId - New parent ID (null for root)
   * @returns {Promise<Object>} Updated category
   */
  async moveToParent(id, newParentId) {
    throw new Error('Method not implemented');
  }

  /**
   * Get featured categories
   * @param {number} limit - Number of categories to return
   * @returns {Promise<Array>} Featured categories
   */
  async findFeatured(limit = 10) {
    throw new Error('Method not implemented');
  }

  /**
   * Bulk update categories
   * @param {Array} updates - Array of update objects
   * @returns {Promise<Object>} Update results
   */
  async bulkUpdate(updates) {
    throw new Error('Method not implemented');
  }

  /**
   * Get category path (breadcrumb)
   * @param {string} id - Category ID
   * @returns {Promise<Array>} Category path from root to current
   */
  async getCategoryPath(id) {
    throw new Error('Method not implemented');
  }

  /**
   * Check if slug is unique
   * @param {string} slug - Category slug
   * @param {string} excludeId - Category ID to exclude from check
   * @returns {Promise<boolean>} Is unique status
   */
  async isSlugUnique(slug, excludeId = null) {
    throw new Error('Method not implemented');
  }
}

module.exports = ICategoryRepository;
