/**
 * Giveaway Controller
 * Handles giveaway/lottery management
 */

const { HttpStatus } = require('../../shared/enums');

class GiveawayController {
  constructor(container) {
    this.container = container;
    this.lotteryService = container.resolve('lotteryService');
  }

  /**
   * Get active giveaways
   */
  async getActiveGiveaways(req, res) {
    try {
      // Use lottery service to get active lotteries
      const result = await this.lotteryService.getActiveLotteries();

      // Transform lottery data to giveaway format
      const giveaways = {
        success: true,
        data: result.lotteries.map(lottery => ({
          id: lottery.id,
          title: lottery.title,
          description: lottery.description,
          image: lottery.image || 'https://via.placeholder.com/400x300/4F46E5/FFFFFF?text=Çekiliş',
          prize: lottery.prize,
          ticketPrice: lottery.ticketPrice,
          maxTickets: lottery.maxTickets,
          soldTickets: lottery.soldTickets,
          endDate: lottery.endDate,
          status: lottery.status,
          isActive: lottery.status === 'active',
          createdAt: lottery.createdAt
        })),
        pagination: result.pagination,
        timestamp: new Date().toISOString()
      };

      res.status(HttpStatus.OK).json(giveaways);
    } catch (error) {
      console.error('Get active giveaways error:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: 'Failed to get active giveaways',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Get all giveaways (admin)
   */
  async getAllGiveaways(req, res) {
    try {
      const options = {
        page: parseInt(req.query.page) || 1,
        limit: parseInt(req.query.limit) || 10,
        sortBy: req.query.sortBy || 'createdAt',
        sortOrder: req.query.sortOrder || 'desc'
      };

      const result = await this.lotteryService.getAllLotteries(options);

      // Transform lottery data to giveaway format
      const giveaways = {
        success: true,
        data: result.lotteries.map(lottery => ({
          id: lottery.id,
          title: lottery.title,
          description: lottery.description,
          image: lottery.image || 'https://via.placeholder.com/400x300/4F46E5/FFFFFF?text=Çekiliş',
          prize: lottery.prize,
          ticketPrice: lottery.ticketPrice,
          maxTickets: lottery.maxTickets,
          soldTickets: lottery.soldTickets,
          startDate: lottery.startDate,
          endDate: lottery.endDate,
          status: lottery.status,
          isActive: lottery.status === 'active',
          createdAt: lottery.createdAt,
          updatedAt: lottery.updatedAt
        })),
        pagination: result.pagination,
        timestamp: new Date().toISOString()
      };

      res.status(HttpStatus.OK).json(giveaways);
    } catch (error) {
      console.error('Get all giveaways error:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: 'Failed to get giveaways',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Get giveaway by ID
   */
  async getGiveawayById(req, res) {
    try {
      const { id } = req.params;
      const lottery = await this.lotteryService.getLotteryById(id);

      const giveaway = {
        success: true,
        data: {
          id: lottery.id,
          title: lottery.title,
          description: lottery.description,
          image: lottery.image || 'https://via.placeholder.com/400x300/4F46E5/FFFFFF?text=Çekiliş',
          prize: lottery.prize,
          ticketPrice: lottery.ticketPrice,
          maxTickets: lottery.maxTickets,
          soldTickets: lottery.soldTickets,
          startDate: lottery.startDate,
          endDate: lottery.endDate,
          status: lottery.status,
          isActive: lottery.status === 'active',
          numbers: lottery.numbers,
          tickets: lottery.tickets,
          winner: lottery.winner,
          createdAt: lottery.createdAt,
          updatedAt: lottery.updatedAt
        },
        timestamp: new Date().toISOString()
      };

      res.status(HttpStatus.OK).json(giveaway);
    } catch (error) {
      console.error('Get giveaway by ID error:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: 'Failed to get giveaway',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Create new giveaway
   */
  async createGiveaway(req, res) {
    try {
      const giveawayData = req.body;
      const createdBy = req.user?.id;

      // Transform giveaway data to lottery format
      const lotteryData = {
        title: giveawayData.title,
        description: giveawayData.description,
        image: giveawayData.image,
        prize: giveawayData.prize,
        ticketPrice: giveawayData.ticketPrice,
        maxTickets: giveawayData.maxTickets,
        startDate: giveawayData.startDate,
        endDate: giveawayData.endDate
      };

      const lottery = await this.lotteryService.createLottery(lotteryData, createdBy);

      const giveaway = {
        success: true,
        data: {
          id: lottery.id,
          title: lottery.title,
          description: lottery.description,
          image: lottery.image,
          prize: lottery.prize,
          ticketPrice: lottery.ticketPrice,
          maxTickets: lottery.maxTickets,
          startDate: lottery.startDate,
          endDate: lottery.endDate,
          status: lottery.status,
          createdAt: lottery.createdAt
        },
        message: 'Giveaway created successfully',
        timestamp: new Date().toISOString()
      };

      res.status(HttpStatus.CREATED).json(giveaway);
    } catch (error) {
      console.error('Create giveaway error:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: 'Failed to create giveaway',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Update giveaway
   */
  async updateGiveaway(req, res) {
    try {
      const { id } = req.params;
      const updateData = req.body;
      const updatedBy = req.user?.id;

      const lottery = await this.lotteryService.updateLottery(id, updateData, updatedBy);

      const giveaway = {
        success: true,
        data: {
          id: lottery.id,
          title: lottery.title,
          description: lottery.description,
          image: lottery.image,
          prize: lottery.prize,
          ticketPrice: lottery.ticketPrice,
          maxTickets: lottery.maxTickets,
          startDate: lottery.startDate,
          endDate: lottery.endDate,
          status: lottery.status,
          updatedAt: lottery.updatedAt
        },
        message: 'Giveaway updated successfully',
        timestamp: new Date().toISOString()
      };

      res.status(HttpStatus.OK).json(giveaway);
    } catch (error) {
      console.error('Update giveaway error:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: 'Failed to update giveaway',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Delete giveaway
   */
  async deleteGiveaway(req, res) {
    try {
      const { id } = req.params;
      const deletedBy = req.user?.id;

      await this.lotteryService.deleteLottery(id, deletedBy);

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'Giveaway deleted successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Delete giveaway error:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: 'Failed to delete giveaway',
        timestamp: new Date().toISOString()
      });
    }
  }
}

module.exports = GiveawayController;
