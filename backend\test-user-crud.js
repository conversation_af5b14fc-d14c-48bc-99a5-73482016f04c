const fetch = require('node-fetch');

const BASE_URL = 'http://localhost:5000/api';

async function testUserCRUD() {
  try {
    console.log('🧪 Testing User CRUD Operations...\n');

    // 1. LOGIN - Get admin token
    console.log('1️⃣ Logging in as admin...');
    const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'eEf8kbpE9G8XhiL'
      })
    });

    const loginData = await loginResponse.json();
    if (!loginResponse.ok) {
      throw new Error(`Login failed: ${loginData.message}`);
    }

    const adminToken = loginData.data.token;
    const adminUserId = loginData.data.user.id;
    console.log('✅ Admin login successful\n');

    // 2. LIST - Get existing users
    console.log('2️⃣ Listing existing users...');
    const listResponse = await fetch(`${BASE_URL}/users`, {
      headers: { 'Authorization': `Bearer ${adminToken}` }
    });
    const listData = await listResponse.json();

    console.log('List response status:', listResponse.status);
    console.log('List response data:', JSON.stringify(listData, null, 2));

    if (listResponse.ok && listData.data && listData.data.users) {
      console.log(`📋 Found ${listData.data.users.length} existing users:`);
      listData.data.users.forEach((user, index) => {
        console.log(`  ${index + 1}. ${user.name} (${user.email}) - Role: ${user.role} - Status: ${user.status}`);
      });
    }
    console.log('');

    // 3. Cleanup existing test users
    console.log('3️⃣ Cleaning up existing test users...');
    if (listResponse.ok && listData.data && listData.data.users) {
      for (const user of listData.data.users) {
        if (user.email && user.email.includes('test-crud')) {
          try {
            await fetch(`${BASE_URL}/users/${user.id}`, {
              method: 'DELETE',
              headers: {
                'Authorization': `Bearer ${adminToken}`,
                'Content-Type': 'application/json'
              }
            });
            console.log('🧹 Cleaned up existing test user:', user.email);
          } catch (error) {
            console.log('🧹 Could not clean up test user:', user.email);
          }
        }
      }
    }

    // 4. CREATE - Add new user
    console.log('4️⃣ Testing CREATE - Adding new user...');
    const newUser = {
      name: 'Test CRUD User',
      email: '<EMAIL>',
      username: 'testcruduser',
      password: 'TestPassword123!',
      role: 'user',
      status: 'active',
      profile: {
        phone: '+90 ************',
        address: 'Test Address 123, Test City',
        dateOfBirth: '1990-01-01'
      }
    };

    const createResponse = await fetch(`${BASE_URL}/users`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${adminToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(newUser)
    });

    const createData = await createResponse.json();
    if (createResponse.ok) {
      const createdUser = createData.data.user;
      console.log(`✅ CREATE successful - User created with ID: ${createdUser.id}`);
      console.log(`   Name: ${createdUser.name}`);
      console.log(`   Email: ${createdUser.email}`);
      console.log(`   Username: ${createdUser.username}`);
      console.log(`   Role: ${createdUser.role}`);
      console.log(`   Status: ${createdUser.status}`);
      console.log('');

      // 5. READ - Get user by ID
      console.log('5️⃣ Testing READ - Getting user by ID...');
      const readResponse = await fetch(`${BASE_URL}/users/${createdUser.id}`, {
        headers: { 'Authorization': `Bearer ${adminToken}` }
      });
      const readData = await readResponse.json();

      if (readResponse.ok) {
        const fetchedUser = readData.data.user;
        console.log(`✅ READ successful - User fetched:`);
        console.log(`   ID: ${fetchedUser.id}`);
        console.log(`   Name: ${fetchedUser.name}`);
        console.log(`   Email: ${fetchedUser.email}`);
        console.log(`   Username: ${fetchedUser.username}`);
        console.log(`   Role: ${fetchedUser.role}`);
        console.log(`   Status: ${fetchedUser.status}`);
        console.log(`   Created: ${fetchedUser.createdAt}`);
        console.log('');

        // 6. UPDATE - Edit user
        console.log('6️⃣ Testing UPDATE - Editing user...');
        const updateData = {
          name: 'Updated CRUD User',
          role: 'seller',
          status: 'active',
          profile: {
            phone: '+90 ************',
            address: 'Updated Address 456, Updated City',
            dateOfBirth: '1990-01-01'
          }
        };

        const updateResponse = await fetch(`${BASE_URL}/users/${createdUser.id}`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${adminToken}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(updateData)
        });

        const updatedData = await updateResponse.json();
        if (updateResponse.ok) {
          const updatedUser = updatedData.data.user;
          console.log(`✅ UPDATE successful - User updated:`);
          console.log(`   Name: ${updatedUser.name} (was ${fetchedUser.name})`);
          console.log(`   Role: ${updatedUser.role} (was ${fetchedUser.role})`);
          console.log(`   Status: ${updatedUser.status}`);
          console.log(`   Phone: ${updatedUser.profile?.phone || 'N/A'}`);
          console.log('');

          // 7. Verify update by reading again
          console.log('7️⃣ Verifying update by reading again...');
          const verifyResponse = await fetch(`${BASE_URL}/users/${createdUser.id}`, {
            headers: { 'Authorization': `Bearer ${adminToken}` }
          });
          const verifyData = await verifyResponse.json();

          if (verifyResponse.ok) {
            const verifiedUser = verifyData.data.user;
            console.log(`✅ VERIFY successful - Changes confirmed:`);
            console.log(`   Name: ${verifiedUser.name}`);
            console.log(`   Role: ${verifiedUser.role}`);
            console.log(`   Phone: ${verifiedUser.profile?.phone || 'N/A'}`);
            console.log('');
          } else {
            console.log(`❌ VERIFY failed:`, verifyData.message);
          }

          // 8. DELETE - Remove user
          console.log('8️⃣ Testing DELETE - Removing user...');
          const deleteResponse = await fetch(`${BASE_URL}/users/${createdUser.id}`, {
            method: 'DELETE',
            headers: {
              'Authorization': `Bearer ${adminToken}`,
              'Content-Type': 'application/json'
            }
          });

          const deleteData = await deleteResponse.json();
          if (deleteResponse.ok) {
            console.log(`✅ DELETE successful - User removed`);
            console.log('');

            // 9. Verify deletion by trying to read
            console.log('9️⃣ Verifying deletion by trying to read...');
            const verifyDeleteResponse = await fetch(`${BASE_URL}/users/${createdUser.id}`, {
              headers: { 'Authorization': `Bearer ${adminToken}` }
            });
            const verifyDeleteData = await verifyDeleteResponse.json();

            if (verifyDeleteResponse.status === 404 ||
                (verifyDeleteData && verifyDeleteData.message === 'User not found')) {
              console.log(`✅ DELETE VERIFY successful - User not found (as expected)`);
            } else {
              console.log(`❌ DELETE VERIFY failed - User still exists:`, verifyDeleteData);
            }
          } else {
            console.log(`❌ DELETE failed:`, deleteData.message);
          }

        } else {
          console.log(`❌ UPDATE failed:`, updatedData.message);
        }

      } else {
        console.log(`❌ READ failed:`, readData.message);
      }

    } else {
      console.log(`❌ CREATE failed:`, createData.message);
      console.log('Response:', JSON.stringify(createData, null, 2));
    }

    console.log('\n🎉 User CRUD testing completed!');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

testUserCRUD();
