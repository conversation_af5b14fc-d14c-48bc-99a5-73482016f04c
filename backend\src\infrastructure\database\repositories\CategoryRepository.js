/**
 * Category Repository Implementation
 * Implements ICategoryRepository interface
 */

const ICategoryRepository = require('../../../core/interfaces/ICategoryRepository');
const CategoryModel = require('../models/Category');
const Category = require('../../../core/entities/Category');
const { PAGINATION } = require('../../../shared/constants');
const { DatabaseException } = require('../../../core/exceptions');

class CategoryRepository extends ICategoryRepository {
  constructor() {
    super();
    this.model = CategoryModel;
  }

  /**
   * Create a new category
   */
  async create(categoryData) {
    try {
      const category = new this.model(categoryData);
      const savedCategory = await category.save();
      return Category.fromPersistence(savedCategory.toObject());
    } catch (error) {
      throw new DatabaseException(`Failed to create category: ${error.message}`);
    }
  }

  /**
   * Find category by ID
   */
  async findById(id) {
    try {
      const category = await this.model.findById(id)
        .populate('parent', 'name slug')
        .lean();

      return category ? Category.fromPersistence(category) : null;
    } catch (error) {
      throw new DatabaseException(`Failed to find category: ${error.message}`);
    }
  }

  /**
   * Find category by slug
   */
  async findBySlug(slug) {
    try {
      const category = await this.model.findOne({ slug })
        .populate('parent', 'name slug')
        .lean();

      return category ? Category.fromPersistence(category) : null;
    } catch (error) {
      throw new DatabaseException(`Failed to find category by slug: ${error.message}`);
    }
  }

  /**
   * Find all categories
   */
  async findAll(options = {}) {
    try {
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        isActive,
        parentId,
        level,
        sortBy = 'order',
        sortOrder = 'asc'
      } = options;

      const query = {};
      if (isActive !== undefined) query.isActive = isActive;
      if (parentId !== undefined) query.parentId = parentId;
      if (level !== undefined) query.level = level;

      const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };
      const skip = (page - 1) * limit;

      const [categories, total] = await Promise.all([
        this.model.find(query)
          .populate('parent', 'name slug')
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .lean(),
        this.model.countDocuments(query)
      ]);

      console.log('🔥 CategoryRepository.findAll - Query:', query);
      console.log('🔥 CategoryRepository.findAll - Found categories count:', categories.length);
      console.log('🔥 CategoryRepository.findAll - Categories:', categories.map(cat => ({ _id: cat._id, name: cat.name, isActive: cat.isActive })));

      return {
        categories: categories.map(category => Category.fromPersistence(category)),
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw new DatabaseException(`Failed to find categories: ${error.message}`);
    }
  }

  /**
   * Find active categories
   */
  async findActive(options = {}) {
    try {
      const { limit, sortBy = 'order', sortOrder = 'asc' } = options;

      let query = this.model.findActive();

      if (sortBy) {
        const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };
        query = query.sort(sort);
      }

      if (limit) {
        query = query.limit(limit);
      }

      const categories = await query.lean();
      return categories.map(category => Category.fromPersistence(category));
    } catch (error) {
      throw new DatabaseException(`Failed to find active categories: ${error.message}`);
    }
  }

  /**
   * Find categories by parent
   */
  async findByParent(parentId, options = {}) {
    try {
      const { sortBy = 'order', sortOrder = 'asc' } = options;
      const sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };

      const categories = await this.model.find({
        parentId,
        isActive: true
      })
      .sort(sort)
      .lean();

      return categories.map(category => Category.fromPersistence(category));
    } catch (error) {
      throw new DatabaseException(`Failed to find categories by parent: ${error.message}`);
    }
  }

  /**
   * Find root categories
   */
  async findRootCategories(options = {}) {
    try {
      const categories = await this.model.findRootCategories(options).lean();
      return categories.map(category => Category.fromPersistence(category));
    } catch (error) {
      throw new DatabaseException(`Failed to find root categories: ${error.message}`);
    }
  }

  /**
   * Search categories
   */
  async search(query, options = {}) {
    try {
      const {
        page = PAGINATION.DEFAULT_PAGE,
        limit = PAGINATION.DEFAULT_LIMIT,
        isActive = true,
        sortBy = 'relevance',
        sortOrder = 'desc'
      } = options;

      const searchQuery = {
        $text: { $search: query },
        isActive
      };

      let sort = {};
      if (sortBy === 'relevance') {
        sort = { score: { $meta: 'textScore' } };
      } else {
        sort = { [sortBy]: sortOrder === 'desc' ? -1 : 1 };
      }

      const skip = (page - 1) * limit;

      const [categories, total] = await Promise.all([
        this.model.find(searchQuery, { score: { $meta: 'textScore' } })
          .sort(sort)
          .skip(skip)
          .limit(limit)
          .lean(),
        this.model.countDocuments(searchQuery)
      ]);

      return {
        categories: categories.map(category => Category.fromPersistence(category)),
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      throw new DatabaseException(`Failed to search categories: ${error.message}`);
    }
  }

  /**
   * Update category
   */
  async update(id, updateData) {
    try {
      const category = await this.model.findByIdAndUpdate(
        id,
        { ...updateData, updatedAt: new Date() },
        { new: true, runValidators: true }
      ).lean();

      return category ? Category.fromPersistence(category) : null;
    } catch (error) {
      throw new DatabaseException(`Failed to update category: ${error.message}`);
    }
  }

  /**
   * Delete category
   */
  async delete(id) {
    try {
      const category = await this.model.findById(id);
      if (!category) {
        return false;
      }

      const canDelete = await category.canBeDeleted();
      if (!canDelete.canDelete) {
        throw new DatabaseException(canDelete.reason);
      }

      await this.model.findByIdAndDelete(id);
      return true;
    } catch (error) {
      throw new DatabaseException(`Failed to delete category: ${error.message}`);
    }
  }

  /**
   * Update category status
   */
  async updateStatus(id, isActive) {
    try {
      const category = await this.model.findByIdAndUpdate(
        id,
        { isActive, updatedAt: new Date() },
        { new: true, runValidators: true }
      ).lean();

      return category ? Category.fromPersistence(category) : null;
    } catch (error) {
      throw new DatabaseException(`Failed to update category status: ${error.message}`);
    }
  }

  /**
   * Get category hierarchy
   */
  async getHierarchy(id) {
    try {
      const category = await this.model.findById(id);
      if (!category) {
        return [];
      }

      const path = await category.getFullPath();
      return path.map(cat => Category.fromPersistence(cat));
    } catch (error) {
      throw new DatabaseException(`Failed to get category hierarchy: ${error.message}`);
    }
  }

  /**
   * Get category tree
   */
  async getCategoryTree(rootId = null) {
    try {
      const tree = await this.model.buildTree(rootId);
      return tree.map(category => Category.fromPersistence(category));
    } catch (error) {
      throw new DatabaseException(`Failed to get category tree: ${error.message}`);
    }
  }

  /**
   * Get categories with product counts
   */
  async findWithProductCounts(options = {}) {
    try {
      const categories = await this.model.findWithProductCounts();
      return categories.map(category => Category.fromPersistence(category));
    } catch (error) {
      throw new DatabaseException(`Failed to find categories with product counts: ${error.message}`);
    }
  }

  /**
   * Check if category has children
   */
  async hasChildren(id) {
    try {
      const count = await this.model.countDocuments({ parentId: id });
      return count > 0;
    } catch (error) {
      throw new DatabaseException(`Failed to check if category has children: ${error.message}`);
    }
  }

  /**
   * Check if category has products
   */
  async hasProducts(id) {
    try {
      const ProductModel = require('../models/Product');
      const count = await ProductModel.countDocuments({ categoryId: id });
      return count > 0;
    } catch (error) {
      throw new DatabaseException(`Failed to check if category has products: ${error.message}`);
    }
  }

  /**
   * Get category statistics
   */
  async getStatistics(id) {
    try {
      const category = await this.model.findById(id);
      if (!category) {
        return null;
      }

      const ProductModel = require('../models/Product');

      const [
        productCount,
        activeProductCount,
        childrenCount,
        totalDescendants
      ] = await Promise.all([
        ProductModel.countDocuments({ categoryId: id }),
        ProductModel.countDocuments({ categoryId: id, status: 'active' }),
        this.model.countDocuments({ parentId: id }),
        this.getDescendantCount(id)
      ]);

      return {
        productCount,
        activeProductCount,
        childrenCount,
        totalDescendants,
        level: category.level,
        isActive: category.isActive,
        isFeatured: category.isFeatured
      };
    } catch (error) {
      throw new DatabaseException(`Failed to get category statistics: ${error.message}`);
    }
  }

  /**
   * Reorder categories
   */
  async reorder(orderData) {
    try {
      const result = await this.model.reorderCategories(orderData);
      return result.modifiedCount > 0;
    } catch (error) {
      throw new DatabaseException(`Failed to reorder categories: ${error.message}`);
    }
  }

  /**
   * Move category to new parent
   */
  async moveToParent(id, newParentId) {
    try {
      // Validate that we're not creating a circular reference
      if (newParentId) {
        const isDescendant = await this.isDescendantOf(newParentId, id);
        if (isDescendant) {
          throw new DatabaseException('Cannot move category to its own descendant');
        }
      }

      const category = await this.model.findByIdAndUpdate(
        id,
        { parentId: newParentId },
        { new: true, runValidators: true }
      ).lean();

      return category ? Category.fromPersistence(category) : null;
    } catch (error) {
      throw new DatabaseException(`Failed to move category: ${error.message}`);
    }
  }

  /**
   * Get featured categories
   */
  async findFeatured(limit = 10) {
    try {
      const categories = await this.model.findFeatured(limit).lean();
      return categories.map(category => Category.fromPersistence(category));
    } catch (error) {
      throw new DatabaseException(`Failed to find featured categories: ${error.message}`);
    }
  }

  /**
   * Bulk update categories
   */
  async bulkUpdate(updates) {
    try {
      const bulkOps = updates.map(update => ({
        updateOne: {
          filter: { _id: update.id },
          update: { ...update.data, updatedAt: new Date() }
        }
      }));

      const result = await this.model.bulkWrite(bulkOps);
      return {
        modifiedCount: result.modifiedCount,
        matchedCount: result.matchedCount
      };
    } catch (error) {
      throw new DatabaseException(`Failed to bulk update categories: ${error.message}`);
    }
  }

  /**
   * Get category path
   */
  async getCategoryPath(id) {
    try {
      return await this.getHierarchy(id);
    } catch (error) {
      throw new DatabaseException(`Failed to get category path: ${error.message}`);
    }
  }

  /**
   * Check if slug is unique
   */
  async isSlugUnique(slug, excludeId = null) {
    try {
      const query = { slug };
      if (excludeId) {
        query._id = { $ne: excludeId };
      }

      const count = await this.model.countDocuments(query);
      return count === 0;
    } catch (error) {
      throw new DatabaseException(`Failed to check slug uniqueness: ${error.message}`);
    }
  }

  // Helper methods

  /**
   * Get descendant count
   */
  async getDescendantCount(id) {
    try {
      const category = await this.model.findById(id);
      if (!category) {
        return 0;
      }

      const descendants = await category.getChildren(true);
      return descendants.length;
    } catch (error) {
      return 0;
    }
  }

  /**
   * Check if category is descendant of another
   */
  async isDescendantOf(categoryId, ancestorId) {
    try {
      let current = await this.model.findById(categoryId);

      while (current && current.parentId) {
        if (current.parentId.toString() === ancestorId.toString()) {
          return true;
        }
        current = await this.model.findById(current.parentId);
      }

      return false;
    } catch (error) {
      return false;
    }
  }

  /**
   * Update product counts for all categories
   */
  async updateAllProductCounts() {
    try {
      await this.model.updateAllProductCounts();
      return true;
    } catch (error) {
      throw new DatabaseException(`Failed to update product counts: ${error.message}`);
    }
  }
}

module.exports = CategoryRepository;
