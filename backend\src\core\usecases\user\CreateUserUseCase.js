/**
 * Create User Use Case
 * Core business logic for user creation
 */

const User = require('../../entities/User');
const { 
  UserAlreadyExistsException, 
  ValidationException 
} = require('../../exceptions');
const { UserRole, ApprovalStatus } = require('../../../shared/enums');

class CreateUserUseCase {
  constructor(userRepository, passwordService, emailService) {
    this.userRepository = userRepository;
    this.passwordService = passwordService;
    this.emailService = emailService;
  }

  /**
   * <PERSON>ni kullanıcı oluştur
   * @param {Object} userData - Kullanıcı verileri
   * @returns {Promise<User>} - <PERSON>luş<PERSON><PERSON><PERSON> kullanıcı
   */
  async execute(userData) {
    // Input validation
    this.validateInput(userData);

    // Check if user already exists
    await this.checkUserUniqueness(userData.email, userData.username);

    // Hash password
    const hashedPassword = await this.passwordService.hash(userData.password);

    // Create user entity
    const userEntity = new User({
      name: userData.name,
      email: userData.email.toLowerCase(),
      username: userData.username.toLowerCase(),
      password: hashedPassword,
      role: userData.role || UserRole.USER,
      approvalStatus: this.getInitialApprovalStatus(userData.role)
    });

    // Save user to database
    const savedUser = await this.userRepository.create(userEntity);

    // Send welcome email
    await this.sendWelcomeEmail(savedUser);

    // Return user without password
    return savedUser.toJSON();
  }

  /**
   * Input validation
   */
  validateInput(userData) {
    const requiredFields = ['name', 'email', 'username', 'password'];
    const missingFields = requiredFields.filter(field => !userData[field]);

    if (missingFields.length > 0) {
      throw new ValidationException(
        `Missing required fields: ${missingFields.join(', ')}`,
        { missingFields }
      );
    }

    // Email format validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(userData.email)) {
      throw new ValidationException('Invalid email format');
    }

    // Username validation
    if (userData.username.length < 3) {
      throw new ValidationException('Username must be at least 3 characters');
    }

    // Password validation
    if (userData.password.length < 6) {
      throw new ValidationException('Password must be at least 6 characters');
    }

    // Role validation
    if (userData.role && !Object.values(UserRole).includes(userData.role)) {
      throw new ValidationException('Invalid user role');
    }
  }

  /**
   * Check if user already exists
   */
  async checkUserUniqueness(email, username) {
    const { emailExists, usernameExists } = await this.userRepository.checkUniqueness(
      email.toLowerCase(),
      username.toLowerCase()
    );

    if (emailExists) {
      throw new UserAlreadyExistsException('email', email);
    }

    if (usernameExists) {
      throw new UserAlreadyExistsException('username', username);
    }
  }

  /**
   * Get initial approval status based on role
   */
  getInitialApprovalStatus(role) {
    // Admin users need manual approval
    if (role === UserRole.ADMIN || role === UserRole.SELLER) {
      return ApprovalStatus.PENDING;
    }
    
    // Regular users are auto-approved
    return ApprovalStatus.APPROVED;
  }

  /**
   * Send welcome email
   */
  async sendWelcomeEmail(user) {
    try {
      await this.emailService.sendWelcomeEmail(user.email, {
        name: user.name,
        username: user.username,
        needsApproval: user.approvalStatus === ApprovalStatus.PENDING
      });
    } catch (error) {
      // Log error but don't fail user creation
      console.error('Failed to send welcome email:', error);
    }
  }
}

module.exports = CreateUserUseCase;
