const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

async function createTestUsers() {
  try {
    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb+srv://emrevaryemez22:<EMAIL>/robinhoot?retryWrites=true&w=majority');
    console.log('✅ Connected to database');

    // Get database
    const db = mongoose.connection.db;

    // Check existing users
    const existingUsers = await db.collection('users').find({}).toArray();
    console.log(`📊 Found ${existingUsers.length} existing users`);

    // Create test users
    const testUsers = [
      {
        name: 'Ah<PERSON> Yılmaz',
        email: '<EMAIL>',
        username: 'ahmet',
        password: await bcrypt.hash('123456', 12),
        role: 'user',
        status: 'active',
        approvalStatus: 'approved',
        isEmailVerified: true,
        canBid: true,
        canParticipateInLottery: true,
        profile: {
          avatar: 'https://res.cloudinary.com/robinhoot/image/upload/v1/avatars/default.png',
          isPhoneVerified: false,
          identityVerified: false,
          identityDocuments: []
        },
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Fatma Demir',
        email: '<EMAIL>',
        username: 'fatma',
        password: await bcrypt.hash('123456', 12),
        role: 'user',
        status: 'active',
        approvalStatus: 'approved',
        isEmailVerified: true,
        canBid: true,
        canParticipateInLottery: true,
        profile: {
          avatar: 'https://res.cloudinary.com/robinhoot/image/upload/v1/avatars/default.png',
          isPhoneVerified: false,
          identityVerified: false,
          identityDocuments: []
        },
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Mehmet Kaya',
        email: '<EMAIL>',
        username: 'mehmet',
        password: await bcrypt.hash('123456', 12),
        role: 'seller',
        status: 'active',
        approvalStatus: 'approved',
        isEmailVerified: true,
        canBid: true,
        canParticipateInLottery: true,
        profile: {
          avatar: 'https://res.cloudinary.com/robinhoot/image/upload/v1/avatars/default.png',
          isPhoneVerified: false,
          identityVerified: false,
          identityDocuments: []
        },
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    console.log('\n👥 Creating test users...');

    for (let i = 0; i < testUsers.length; i++) {
      const userData = testUsers[i];
      
      // Check if user already exists
      const existingUser = await db.collection('users').findOne({ 
        $or: [
          { email: userData.email },
          { username: userData.username }
        ]
      });

      if (existingUser) {
        console.log(`${i + 1}. User ${userData.name} (${userData.email}) already exists - skipping`);
        continue;
      }

      console.log(`${i + 1}. Creating user: ${userData.name} (${userData.email}) - Role: ${userData.role}`);

      try {
        const result = await db.collection('users').insertOne(userData);
        console.log(`   ✅ Created with ID: ${result.insertedId}`);
      } catch (error) {
        console.log(`   ❌ Failed to create: ${error.message}`);
      }
    }

    // Verify created users
    console.log('\n🔍 Verifying users...');
    const allUsers = await db.collection('users').find({}).toArray();
    console.log(`📊 Total users in database: ${allUsers.length}`);

    allUsers.forEach((user, index) => {
      console.log(`  ${index + 1}. ${user.name} (${user.email}) - Role: ${user.role} - Status: ${user.status}`);
    });

    await mongoose.disconnect();
    console.log('\n✅ Disconnected from database');
    console.log('🎉 Test users creation completed!');

  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

createTestUsers();
