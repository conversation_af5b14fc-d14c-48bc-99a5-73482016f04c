const mongoose = require('mongoose');
require('dotenv').config();

async function debugUser() {
  try {
    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb+srv://emrevaryemez22:<EMAIL>/robinhoot?retryWrites=true&w=majority');
    console.log('✅ Connected to database');

    // Get user collection directly
    const db = mongoose.connection.db;
    const users = await db.collection('users').find({}).toArray();
    
    console.log('📊 Total users in database:', users.length);
    
    if (users.length > 0) {
      console.log('👤 First user:');
      console.log(JSON.stringify(users[0], null, 2));
      
      // Try to find admin user
      const adminUser = await db.collection('users').findOne({ email: 'nazohay<PERSON><EMAIL>' });
      if (adminUser) {
        console.log('\n🔑 Admin user found:');
        console.log('Email:', adminUser.email);
        console.log('Role:', adminUser.role);
        console.log('Status:', adminUser.status);
        console.log('ApprovalStatus:', adminUser.approvalStatus);
        console.log('Has password:', !!adminUser.password);
        console.log('Password length:', adminUser.password ? adminUser.password.length : 0);
      } else {
        console.log('❌ Admin user not found');
      }
    }

    // Test password verification
    const bcrypt = require('bcryptjs');
    const adminUser = await db.collection('users').findOne({ email: '<EMAIL>' });
    if (adminUser && adminUser.password) {
      const isPasswordValid = await bcrypt.compare('eEf8kbpE9G8XhiL', adminUser.password);
      console.log('\n🔐 Password verification:', isPasswordValid);
    }

    await mongoose.disconnect();
    console.log('✅ Disconnected from database');

  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

debugUser();
