/**
 * Product Repository Interface
 * Ürün veri eri<PERSON><PERSON> katman<PERSON> için interface
 */

class IProductRepository {
  /**
   * <PERSON>rün oluş<PERSON>
   * @param {Product} product - Ürün entity'si
   * @returns {Promise<Product>} - Oluşturulan ürün
   */
  async create(product) {
    throw new Error('Method not implemented');
  }

  /**
   * ID ile ürün bul
   * @param {string} id - Ürün ID'si
   * @returns {Promise<Product|null>} - Bulunan ürün veya null
   */
  async findById(id) {
    throw new Error('Method not implemented');
  }

  /**
   * Ürün güncelle
   * @param {string} id - Ürün ID'si
   * @param {Product} product - Güncellenmiş ürün entity'si
   * @returns {Promise<Product>} - Güncellenmiş ürün
   */
  async update(id, product) {
    throw new Error('Method not implemented');
  }

  /**
   * Ürün sil
   * @param {string} id - Ürün ID'si
   * @returns {Promise<boolean>} - <PERSON><PERSON><PERSON> iş<PERSON>i başarılı mı
   */
  async delete(id) {
    throw new Error('Method not implemented');
  }

  /**
   * Tüm ürünleri listele (sayfalama ile)
   * @param {Object} options - Sayfalama ve filtreleme seçenekleri
   * @returns {Promise<{products: Product[], total: number, page: number, limit: number}>}
   */
  async findAll(options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Kategori göre ürünleri bul
   * @param {string} categoryId - Kategori ID'si
   * @param {Object} options - Sayfalama seçenekleri
   * @returns {Promise<{products: Product[], total: number}>}
   */
  async findByCategory(categoryId, options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Satıcı göre ürünleri bul
   * @param {string} sellerId - Satıcı ID'si
   * @param {Object} options - Sayfalama seçenekleri
   * @returns {Promise<{products: Product[], total: number}>}
   */
  async findBySeller(sellerId, options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Durum göre ürünleri bul
   * @param {string} status - Ürün durumu
   * @param {Object} options - Sayfalama seçenekleri
   * @returns {Promise<{products: Product[], total: number}>}
   */
  async findByStatus(status, options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Öne çıkan ürünleri bul
   * @param {Object} options - Sayfalama seçenekleri
   * @returns {Promise<{products: Product[], total: number}>}
   */
  async findFeatured(options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Stokta olan ürünleri bul
   * @param {Object} options - Sayfalama seçenekleri
   * @returns {Promise<{products: Product[], total: number}>}
   */
  async findInStock(options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Fiyat aralığına göre ürünleri bul
   * @param {number} minPrice - Minimum fiyat
   * @param {number} maxPrice - Maximum fiyat
   * @param {Object} options - Sayfalama seçenekleri
   * @returns {Promise<{products: Product[], total: number}>}
   */
  async findByPriceRange(minPrice, maxPrice, options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Ürün arama (isim, açıklama, tag)
   * @param {string} searchTerm - Arama terimi
   * @param {Object} options - Sayfalama seçenekleri
   * @returns {Promise<{products: Product[], total: number}>}
   */
  async search(searchTerm, options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Tag'e göre ürünleri bul
   * @param {string} tag - Tag
   * @param {Object} options - Sayfalama seçenekleri
   * @returns {Promise<{products: Product[], total: number}>}
   */
  async findByTag(tag, options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Benzer ürünleri bul
   * @param {string} productId - Ürün ID'si
   * @param {number} limit - Maksimum sonuç sayısı
   * @returns {Promise<Product[]>} - Benzer ürünler
   */
  async findSimilar(productId, limit = 5) {
    throw new Error('Method not implemented');
  }

  /**
   * Ürün sayısını al
   * @param {Object} filters - Filtreleme kriterleri
   * @returns {Promise<number>} - Ürün sayısı
   */
  async count(filters = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Ürün stokunu güncelle
   * @param {string} id - Ürün ID'si
   * @param {number} stock - Yeni stok miktarı
   * @returns {Promise<boolean>} - Güncelleme başarılı mı
   */
  async updateStock(id, stock) {
    throw new Error('Method not implemented');
  }

  /**
   * Ürün fiyatını güncelle
   * @param {string} id - Ürün ID'si
   * @param {number} price - Yeni fiyat
   * @returns {Promise<boolean>} - Güncelleme başarılı mı
   */
  async updatePrice(id, price) {
    throw new Error('Method not implemented');
  }

  /**
   * Ürün durumunu güncelle
   * @param {string} id - Ürün ID'si
   * @param {string} status - Yeni durum
   * @returns {Promise<boolean>} - Güncelleme başarılı mı
   */
  async updateStatus(id, status) {
    throw new Error('Method not implemented');
  }

  /**
   * Öne çıkan durumunu güncelle
   * @param {string} id - Ürün ID'si
   * @param {boolean} featured - Öne çıkan mı
   * @returns {Promise<boolean>} - Güncelleme başarılı mı
   */
  async updateFeaturedStatus(id, featured) {
    throw new Error('Method not implemented');
  }

  /**
   * Ürün durumunu toplu güncelle
   * @param {string[]} ids - Ürün ID'leri
   * @param {string} status - Yeni durum
   * @returns {Promise<number>} - Güncellenen ürün sayısı
   */
  async bulkUpdateStatus(ids, status) {
    throw new Error('Method not implemented');
  }

  /**
   * Ürün stokunu toplu güncelle
   * @param {Array<{id: string, stock: number}>} updates - Güncelleme listesi
   * @returns {Promise<number>} - Güncellenen ürün sayısı
   */
  async bulkUpdateStock(updates) {
    throw new Error('Method not implemented');
  }

  /**
   * Düşük stoklu ürünleri bul
   * @param {number} threshold - Stok eşiği
   * @param {Object} options - Sayfalama seçenekleri
   * @returns {Promise<{products: Product[], total: number}>}
   */
  async findLowStock(threshold = 10, options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * En çok satan ürünleri bul
   * @param {Object} options - Sayfalama seçenekleri
   * @returns {Promise<{products: Product[], total: number}>}
   */
  async findBestSellers(options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Yeni ürünleri bul
   * @param {number} days - Kaç gün öncesine kadar
   * @param {Object} options - Sayfalama seçenekleri
   * @returns {Promise<{products: Product[], total: number}>}
   */
  async findNew(days = 7, options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * İndirimli ürünleri bul
   * @param {Object} options - Sayfalama seçenekleri
   * @returns {Promise<{products: Product[], total: number}>}
   */
  async findDiscounted(options = {}) {
    throw new Error('Method not implemented');
  }

  /**
   * Dinamik fiyatlandırmalı ürünleri bul
   * @param {Object} options - Sayfalama seçenekleri
   * @returns {Promise<{products: Product[], total: number}>}
   */
  async findWithDynamicPricing(options = {}) {
    throw new Error('Method not implemented');
  }
}

module.exports = IProductRepository;
