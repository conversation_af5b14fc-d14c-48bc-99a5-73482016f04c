/**
 * Email Service
 * Infrastructure service for email operations
 */

const { EMAIL } = require('../../shared/constants');

class EmailService {
  constructor() {
    this.fromAddress = EMAIL.FROM_ADDRESS;
    this.templates = EMAIL.TEMPLATES;
  }

  /**
   * Send welcome email
   * @param {string} email - Recipient email
   * @param {Object} data - Email data
   */
  async sendWelcomeEmail(email, data) {
    try {
      const { name, username, needsApproval } = data;
      
      const subject = 'Welcome to RobinHoot!';
      const message = needsApproval 
        ? `Welcome ${name}! Your account has been created and is pending approval.`
        : `Welcome ${name}! Your account has been created successfully.`;

      // In production, integrate with actual email service (SendGrid, AWS SES, etc.)
      console.log(`Sending welcome email to ${email}:`, {
        subject,
        message,
        username,
        needsApproval
      });

      return { success: true, messageId: this.generateMessageId() };
    } catch (error) {
      console.error('Failed to send welcome email:', error);
      throw new Error(`Email sending failed: ${error.message}`);
    }
  }

  /**
   * Send approval email
   * @param {string} email - Recipient email
   * @param {Object} data - Email data
   */
  async sendApprovalEmail(email, data) {
    try {
      const { name, approved, reason } = data;
      
      const subject = approved ? 'Account Approved!' : 'Account Status Update';
      const message = approved
        ? `Congratulations ${name}! Your account has been approved. You can now participate in auctions and lotteries.`
        : `Hello ${name}, your account status has been updated. ${reason ? `Reason: ${reason}` : ''}`;

      console.log(`Sending approval email to ${email}:`, {
        subject,
        message,
        approved,
        reason
      });

      return { success: true, messageId: this.generateMessageId() };
    } catch (error) {
      console.error('Failed to send approval email:', error);
      throw new Error(`Email sending failed: ${error.message}`);
    }
  }

  /**
   * Send password reset email
   * @param {string} email - Recipient email
   * @param {Object} data - Email data
   */
  async sendPasswordResetEmail(email, data) {
    try {
      const { name, resetToken, resetUrl } = data;
      
      const subject = 'Password Reset Request';
      const message = `Hello ${name}, you requested a password reset. Click the link to reset your password: ${resetUrl}`;

      console.log(`Sending password reset email to ${email}:`, {
        subject,
        message,
        resetToken: resetToken.substring(0, 10) + '...' // Log partial token for security
      });

      return { success: true, messageId: this.generateMessageId() };
    } catch (error) {
      console.error('Failed to send password reset email:', error);
      throw new Error(`Email sending failed: ${error.message}`);
    }
  }

  /**
   * Send email verification email
   * @param {string} email - Recipient email
   * @param {Object} data - Email data
   */
  async sendEmailVerificationEmail(email, data) {
    try {
      const { name, verificationToken, verificationUrl } = data;
      
      const subject = 'Verify Your Email Address';
      const message = `Hello ${name}, please verify your email address by clicking: ${verificationUrl}`;

      console.log(`Sending email verification to ${email}:`, {
        subject,
        message,
        verificationToken: verificationToken.substring(0, 10) + '...'
      });

      return { success: true, messageId: this.generateMessageId() };
    } catch (error) {
      console.error('Failed to send verification email:', error);
      throw new Error(`Email sending failed: ${error.message}`);
    }
  }

  /**
   * Send order confirmation email
   * @param {string} email - Recipient email
   * @param {Object} data - Email data
   */
  async sendOrderConfirmationEmail(email, data) {
    try {
      const { name, orderNumber, totalAmount, items } = data;
      
      const subject = `Order Confirmation - ${orderNumber}`;
      const message = `Hello ${name}, your order ${orderNumber} has been confirmed. Total: $${totalAmount}`;

      console.log(`Sending order confirmation to ${email}:`, {
        subject,
        message,
        orderNumber,
        totalAmount,
        itemCount: items?.length
      });

      return { success: true, messageId: this.generateMessageId() };
    } catch (error) {
      console.error('Failed to send order confirmation email:', error);
      throw new Error(`Email sending failed: ${error.message}`);
    }
  }

  /**
   * Send auction won email
   * @param {string} email - Recipient email
   * @param {Object} data - Email data
   */
  async sendAuctionWonEmail(email, data) {
    try {
      const { name, auctionTitle, winningBid, productName } = data;
      
      const subject = 'Congratulations! You Won an Auction';
      const message = `Hello ${name}, you won the auction "${auctionTitle}" for ${productName} with a bid of $${winningBid}`;

      console.log(`Sending auction won email to ${email}:`, {
        subject,
        message,
        auctionTitle,
        winningBid
      });

      return { success: true, messageId: this.generateMessageId() };
    } catch (error) {
      console.error('Failed to send auction won email:', error);
      throw new Error(`Email sending failed: ${error.message}`);
    }
  }

  /**
   * Send lottery won email
   * @param {string} email - Recipient email
   * @param {Object} data - Email data
   */
  async sendLotteryWonEmail(email, data) {
    try {
      const { name, lotteryTitle, winningNumber, prize } = data;
      
      const subject = 'Congratulations! You Won the Lottery';
      const message = `Hello ${name}, you won the lottery "${lotteryTitle}" with number ${winningNumber}! Prize: $${prize}`;

      console.log(`Sending lottery won email to ${email}:`, {
        subject,
        message,
        lotteryTitle,
        winningNumber,
        prize
      });

      return { success: true, messageId: this.generateMessageId() };
    } catch (error) {
      console.error('Failed to send lottery won email:', error);
      throw new Error(`Email sending failed: ${error.message}`);
    }
  }

  /**
   * Send generic notification email
   * @param {string} email - Recipient email
   * @param {string} subject - Email subject
   * @param {string} message - Email message
   * @param {Object} options - Additional options
   */
  async sendNotificationEmail(email, subject, message, options = {}) {
    try {
      console.log(`Sending notification email to ${email}:`, {
        subject,
        message,
        options
      });

      return { success: true, messageId: this.generateMessageId() };
    } catch (error) {
      console.error('Failed to send notification email:', error);
      throw new Error(`Email sending failed: ${error.message}`);
    }
  }

  /**
   * Validate email address
   * @param {string} email - Email to validate
   * @returns {boolean} - Is valid email
   */
  validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Generate unique message ID
   * @returns {string} - Message ID
   */
  generateMessageId() {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get email template
   * @param {string} templateName - Template name
   * @returns {Object} - Template configuration
   */
  getTemplate(templateName) {
    const templates = {
      [this.templates.WELCOME]: {
        subject: 'Welcome to RobinHoot!',
        template: 'welcome'
      },
      [this.templates.ORDER_CONFIRMATION]: {
        subject: 'Order Confirmation',
        template: 'order_confirmation'
      },
      [this.templates.PASSWORD_RESET]: {
        subject: 'Password Reset Request',
        template: 'password_reset'
      },
      [this.templates.AUCTION_WON]: {
        subject: 'Auction Won!',
        template: 'auction_won'
      },
      [this.templates.LOTTERY_WON]: {
        subject: 'Lottery Won!',
        template: 'lottery_won'
      }
    };

    return templates[templateName] || null;
  }

  /**
   * Batch send emails
   * @param {Array} emails - Array of email objects
   * @returns {Array} - Results array
   */
  async sendBatchEmails(emails) {
    const results = [];
    
    for (const emailData of emails) {
      try {
        const result = await this.sendNotificationEmail(
          emailData.email,
          emailData.subject,
          emailData.message,
          emailData.options
        );
        results.push({ ...result, email: emailData.email });
      } catch (error) {
        results.push({ 
          success: false, 
          error: error.message, 
          email: emailData.email 
        });
      }
    }

    return results;
  }
}

module.exports = EmailService;
