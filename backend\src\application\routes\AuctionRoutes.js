/**
 * Auction Routes Configuration
 * Defines all auction-related API endpoints
 */

const express = require('express');
const ErrorMiddleware = require('../middleware/ErrorMiddleware');

class AuctionRoutes {
  constructor(container) {
    this.container = container;
    this.router = express.Router();
    this.setupRoutes();
  }

  setupRoutes() {
    const auctionController = this.container.resolve('auctionController');
    const authMiddleware = this.container.resolve('authMiddleware');

    // Public routes (no authentication required)

    // Get all auctions with filtering and pagination
    this.router.get('/',
      ErrorMiddleware.asyncHandler(auctionController.getAllAuctions.bind(auctionController))
    );

    // Get active auctions
    this.router.get('/active',
      ErrorMiddleware.asyncHandler(auctionController.getActiveAuctions.bind(auctionController))
    );

    // Get ending soon auctions
    this.router.get('/ending-soon',
      ErrorMiddleware.asyncHandler(auctionController.getEndingSoonAuctions.bind(auctionController))
    );

    // Get auction by ID
    this.router.get('/:id',
      ErrorMiddleware.asyncHandler(auctionController.getAuctionById.bind(auctionController))
    );

    // Increment view count (can be public)
    this.router.post('/:id/view',
      ErrorMiddleware.asyncHandler(auctionController.incrementViewCount.bind(auctionController))
    );

    // Get auction bids (public for transparency)
    this.router.get('/:id/bids',
      ErrorMiddleware.asyncHandler(auctionController.getAuctionBids.bind(auctionController))
    );

    // Get auction statistics (public)
    this.router.get('/:id/statistics',
      ErrorMiddleware.asyncHandler(auctionController.getAuctionStatistics.bind(auctionController))
    );

    // Protected routes (authentication required)

    // Create new auction (sellers and admins only)
    this.router.post('/',
      authMiddleware.authenticate(),
      authMiddleware.requireSeller(),
      ErrorMiddleware.asyncHandler(auctionController.createAuction.bind(auctionController))
    );

    // Update auction (owner or admin only)
    this.router.put('/:id',
      authMiddleware.authenticate(),
      ErrorMiddleware.asyncHandler(auctionController.updateAuction.bind(auctionController))
    );

    // Delete auction (owner or admin only)
    this.router.delete('/:id',
      authMiddleware.authenticate(),
      ErrorMiddleware.asyncHandler(auctionController.deleteAuction.bind(auctionController))
    );

    // Place bid (requires bid permission)
    this.router.post('/:id/bid',
      authMiddleware.authenticate(),
      authMiddleware.requireBidPermission(),
      ErrorMiddleware.asyncHandler(auctionController.placeBid.bind(auctionController))
    );

    // Get user's own auctions (seller dashboard)
    this.router.get('/user/my-auctions',
      authMiddleware.authenticate(),
      authMiddleware.requireSeller(),
      ErrorMiddleware.asyncHandler(auctionController.getUserAuctions.bind(auctionController))
    );

    // Get user's bids
    this.router.get('/user/my-bids',
      authMiddleware.authenticate(),
      ErrorMiddleware.asyncHandler(auctionController.getUserBids.bind(auctionController))
    );

    // Admin routes (admin only)

    // Update auction status
    this.router.put('/:id/status',
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(auctionController.updateAuctionStatus.bind(auctionController))
    );

    // Get auction counts by status
    this.router.get('/admin/counts',
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(auctionController.getAuctionCounts.bind(auctionController))
    );

    // System operation: Update auction statuses
    this.router.post('/admin/update-statuses',
      authMiddleware.authenticate(),
      authMiddleware.requireAdmin(),
      ErrorMiddleware.asyncHandler(auctionController.updateAuctionStatuses.bind(auctionController))
    );

    // Advanced search (can be public or protected based on requirements)
    this.router.get('/search/:query',
      ErrorMiddleware.asyncHandler(auctionController.getAllAuctions.bind(auctionController))
    );
  }

  getRouter() {
    return this.router;
  }
}

module.exports = AuctionRoutes;
