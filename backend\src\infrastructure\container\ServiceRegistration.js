/**
 * Service Registration
 * Registers all application services in the DI container
 */

const DIContainer = require('./DIContainer');

// Infrastructure Services
const PasswordService = require('../services/PasswordService');
const TokenService = require('../services/TokenService');
const EmailService = require('../services/EmailService');

// Database Models
const UserModel = require('../database/models/User');

// Repositories
const UserRepository = require('../database/repositories/UserRepository');
const AuctionRepository = require('../database/repositories/AuctionRepository');
const LotteryRepository = require('../database/repositories/LotteryRepository');
const ProductRepository = require('../database/repositories/ProductRepository');
const CategoryRepository = require('../database/repositories/CategoryRepository');
const OrderRepository = require('../database/repositories/OrderRepository');

// Business Services
const UserService = require('../../business/services/UserService');
const AuctionService = require('../../business/services/AuctionService');
const ProductService = require('../../business/services/ProductService');
const LotteryService = require('../../business/services/LotteryService');
const CategoryService = require('../../business/services/CategoryService');
const OrderService = require('../../business/services/OrderService');

// Controllers
const UserController = require('../../application/controllers/UserController');
const AuctionController = require('../../application/controllers/AuctionController');
const ProductController = require('../../application/controllers/ProductController');
const LotteryController = require('../../application/controllers/LotteryController');
const CategoryController = require('../../application/controllers/CategoryController');
const OrderController = require('../../application/controllers/OrderController');

// Middleware
const AuthMiddleware = require('../../application/middleware/AuthMiddleware');
const ErrorMiddleware = require('../../application/middleware/ErrorMiddleware');

class ServiceRegistration {
  /**
   * Register all services
   * @param {DIContainer} container - DI Container instance
   */
  static registerServices(container) {
    // Infrastructure Services (Singletons)
    container.registerSingleton('passwordService', () => new PasswordService());
    container.registerSingleton('tokenService', () => new TokenService());
    container.registerSingleton('emailService', () => new EmailService());

    // Database Models (Instances)
    container.registerInstance('userModel', UserModel);

    // Repositories (Singletons)
    container.registerSingleton('userRepository', () => new UserRepository(), []);
    container.registerSingleton('auctionRepository', () => new AuctionRepository(), []);
    container.registerSingleton('lotteryRepository', () => new LotteryRepository(), []);
    container.registerSingleton('productRepository', () => new ProductRepository(), []);
    container.registerSingleton('categoryRepository', () => new CategoryRepository(), []);
    container.registerSingleton('orderRepository', () => new OrderRepository(), []);

    // Business Services (Singletons)
    container.registerSingleton('userService',
      (userRepository, passwordService, tokenService, emailService) =>
        new UserService(userRepository, passwordService, tokenService, emailService),
      ['userRepository', 'passwordService', 'tokenService', 'emailService']
    );

    container.registerSingleton('auctionService',
      (auctionRepository, userRepository, productRepository, emailService) =>
        new AuctionService(auctionRepository, userRepository, productRepository, emailService),
      ['auctionRepository', 'userRepository', 'productRepository', 'emailService']
    );

    container.registerSingleton('productService',
      (productRepository, userRepository, categoryRepository, emailService) =>
        new ProductService(productRepository, userRepository, categoryRepository, emailService),
      ['productRepository', 'userRepository', 'categoryRepository', 'emailService']
    );

    container.registerSingleton('lotteryService',
      (lotteryRepository, userRepository, emailService) =>
        new LotteryService(lotteryRepository, userRepository, emailService),
      ['lotteryRepository', 'userRepository', 'emailService']
    );

    container.registerSingleton('categoryService',
      (categoryRepository, userRepository, productRepository) =>
        new CategoryService(categoryRepository, userRepository, productRepository),
      ['categoryRepository', 'userRepository', 'productRepository']
    );

    container.registerSingleton('orderService',
      (orderRepository, userRepository, productRepository, emailService) =>
        new OrderService(orderRepository, userRepository, productRepository, emailService),
      ['orderRepository', 'userRepository', 'productRepository', 'emailService']
    );

    // Controllers (Transient - new instance per request)
    container.registerTransient('userController',
      (userService) => new UserController(userService),
      ['userService']
    );

    container.registerTransient('auctionController',
      (auctionService) => new AuctionController(auctionService),
      ['auctionService']
    );

    container.registerTransient('productController',
      (productService) => new ProductController(productService),
      ['productService']
    );

    container.registerTransient('lotteryController',
      (lotteryService) => new LotteryController(lotteryService),
      ['lotteryService']
    );

    container.registerTransient('categoryController',
      (categoryService) => new CategoryController(categoryService),
      ['categoryService']
    );

    container.registerTransient('orderController',
      (orderService) => new OrderController(orderService),
      ['orderService']
    );

    // Middleware (Singletons)
    container.registerSingleton('authMiddleware',
      (tokenService, userRepository) => new AuthMiddleware(tokenService, userRepository),
      ['tokenService', 'userRepository']
    );

    container.registerInstance('errorMiddleware', ErrorMiddleware);

    return container;
  }

  /**
   * Register database services
   * @param {DIContainer} container - DI Container instance
   */
  static registerDatabaseServices(container) {
    // Database connection will be registered here
    // container.registerSingleton('database', () => new DatabaseConnection());

    return container;
  }

  /**
   * Register external services
   * @param {DIContainer} container - DI Container instance
   */
  static registerExternalServices(container) {
    // External API services will be registered here
    // container.registerSingleton('paymentService', () => new PaymentService());
    // container.registerSingleton('notificationService', () => new NotificationService());

    return container;
  }

  /**
   * Register all application services
   * @returns {DIContainer} Configured container
   */
  static bootstrap() {
    const container = new DIContainer();

    // Register services in order
    this.registerServices(container);
    this.registerDatabaseServices(container);
    this.registerExternalServices(container);

    return container;
  }

  /**
   * Register services for testing
   * @returns {DIContainer} Test container with mocked services
   */
  static bootstrapForTesting() {
    const container = new DIContainer();

    // Register mock services for testing
    container.registerSingleton('passwordService', () => ({
      hash: jest.fn(),
      compare: jest.fn(),
      validateStrength: jest.fn()
    }));

    container.registerSingleton('tokenService', () => ({
      generateAccessToken: jest.fn(),
      verifyAccessToken: jest.fn(),
      generateRefreshToken: jest.fn()
    }));

    container.registerSingleton('emailService', () => ({
      sendWelcomeEmail: jest.fn(),
      sendPasswordResetEmail: jest.fn()
    }));

    container.registerSingleton('userRepository', () => ({
      create: jest.fn(),
      findById: jest.fn(),
      findByEmail: jest.fn(),
      update: jest.fn()
    }));

    // Register real business services with mocked dependencies
    container.registerSingleton('userService',
      (userRepository, passwordService, tokenService, emailService) =>
        new UserService(userRepository, passwordService, tokenService, emailService),
      ['userRepository', 'passwordService', 'tokenService', 'emailService']
    );

    return container;
  }
}

module.exports = ServiceRegistration;
