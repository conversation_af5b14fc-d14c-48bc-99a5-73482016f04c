/**
 * Text formatting utilities for consistent product display
 */

/**
 * Ürün/başl<PERSON>k ismini büyük harfe çevirir (tüm ürün türleri için)
 * @param {string} text - Formatlanacak metin
 * @returns {string} Büyük harflerle formatlanmış metin
 */
const formatTitle = (text) => {
  if (!text) return '';
  return text.toString().toUpperCase().trim();
};

/**
 * Açıklamayı ilk harf bü<PERSON>ük, gerisi küçük olacak şekilde formatlar
 * @param {string} text - Formatlanacak metin
 * @returns {string} <PERSON><PERSON> harf bü<PERSON>k, gerisi küçük formatlanmış metin
 */
const formatDescription = (text) => {
  if (!text) return '';
  const trimmed = text.toString().trim();
  if (trimmed.length === 0) return '';
  
  return trimmed.charAt(0).toUpperCase() + trimmed.slice(1).toLowerCase();
};

module.exports = {
  formatTitle,
  formatDescription
};
