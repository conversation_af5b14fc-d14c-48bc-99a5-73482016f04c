/**
 * Order Service Unit Tests
 */

const OrderService = require('../../../src/business/services/OrderService');
const { BusinessException, ValidationException, AuthorizationException } = require('../../../src/core/exceptions');
const { OrderStatus, PaymentStatus, UserRole } = require('../../../src/shared/enums');

describe('OrderService', () => {
  let orderService;
  let mockOrderRepository;
  let mockUserRepository;
  let mockProductRepository;
  let mockEmailService;

  beforeEach(() => {
    // Mock dependencies
    mockOrderRepository = {
      findById: jest.fn(),
      findByOrderNumber: jest.fn(),
      findAll: jest.fn(),
      findByUser: jest.fn(),
      findBySeller: jest.fn(),
      findByStatus: jest.fn(),
      search: jest.fn(),
      create: jest.fn(),
      update: jest.fn(),
      cancel: jest.fn(),
      updateStatus: jest.fn(),
      updatePaymentStatus: jest.fn(),
      addTrackingInfo: jest.fn(),
      getStatistics: jest.fn(),
      getCountByStatus: jest.fn(),
      getRevenueStatistics: jest.fn()
    };

    mockUserRepository = {
      findById: jest.fn()
    };

    mockProductRepository = {
      findById: jest.fn(),
      updateInventory: jest.fn()
    };

    mockEmailService = {
      sendNotificationEmail: jest.fn()
    };

    orderService = new OrderService(
      mockOrderRepository,
      mockUserRepository,
      mockProductRepository,
      mockEmailService
    );
  });

  describe('createOrder', () => {
    it('should create order successfully', async () => {
      // Arrange
      const orderData = testUtils.createTestOrder();
      const userId = '123';
      const user = testUtils.createTestUser({ id: userId, isActive: true });
      const product = testUtils.createTestProduct({ 
        id: orderData.items[0].productId,
        stock: 10,
        status: 'active'
      });
      const savedOrder = { ...orderData, id: '456', userId };

      mockUserRepository.findById.mockResolvedValue(user);
      mockProductRepository.findById.mockResolvedValue(product);
      mockOrderRepository.create.mockResolvedValue(savedOrder);
      mockProductRepository.updateInventory.mockResolvedValue(true);

      // Act
      const result = await orderService.createOrder(orderData, userId);

      // Assert
      expect(mockUserRepository.findById).toHaveBeenCalledWith(userId);
      expect(mockProductRepository.findById).toHaveBeenCalled();
      expect(mockOrderRepository.create).toHaveBeenCalled();
      expect(mockProductRepository.updateInventory).toHaveBeenCalled();
      expect(mockEmailService.sendNotificationEmail).toHaveBeenCalled();
      expect(result).toEqual(savedOrder);
    });

    it('should throw ValidationException if user not found', async () => {
      // Arrange
      const orderData = testUtils.createTestOrder();
      const userId = 'nonexistent';

      mockUserRepository.findById.mockResolvedValue(null);

      // Act & Assert
      await expect(orderService.createOrder(orderData, userId)).rejects.toThrow(ValidationException);
      expect(mockOrderRepository.create).not.toHaveBeenCalled();
    });

    it('should throw AuthorizationException if user is inactive', async () => {
      // Arrange
      const orderData = testUtils.createTestOrder();
      const userId = '123';
      const user = testUtils.createTestUser({ id: userId, isActive: false });

      mockUserRepository.findById.mockResolvedValue(user);

      // Act & Assert
      await expect(orderService.createOrder(orderData, userId)).rejects.toThrow(AuthorizationException);
      expect(mockOrderRepository.create).not.toHaveBeenCalled();
    });

    it('should throw ValidationException if product not found', async () => {
      // Arrange
      const orderData = testUtils.createTestOrder();
      const userId = '123';
      const user = testUtils.createTestUser({ id: userId, isActive: true });

      mockUserRepository.findById.mockResolvedValue(user);
      mockProductRepository.findById.mockResolvedValue(null);

      // Act & Assert
      await expect(orderService.createOrder(orderData, userId)).rejects.toThrow(ValidationException);
      expect(mockOrderRepository.create).not.toHaveBeenCalled();
    });

    it('should throw ValidationException if insufficient stock', async () => {
      // Arrange
      const orderData = testUtils.createTestOrder();
      orderData.items[0].quantity = 15; // More than available stock
      const userId = '123';
      const user = testUtils.createTestUser({ id: userId, isActive: true });
      const product = testUtils.createTestProduct({ 
        id: orderData.items[0].productId,
        stock: 10, // Less than requested quantity
        status: 'active'
      });

      mockUserRepository.findById.mockResolvedValue(user);
      mockProductRepository.findById.mockResolvedValue(product);

      // Act & Assert
      await expect(orderService.createOrder(orderData, userId)).rejects.toThrow(ValidationException);
      expect(mockOrderRepository.create).not.toHaveBeenCalled();
    });
  });

  describe('updateOrder', () => {
    it('should update order successfully by owner', async () => {
      // Arrange
      const orderId = '456';
      const updateData = { notes: 'Updated notes' };
      const userId = '123';
      const order = testUtils.createTestOrder({ id: orderId, userId, status: OrderStatus.PENDING });
      const user = testUtils.createTestUser({ id: userId });
      const updatedOrder = { ...order, ...updateData };

      mockOrderRepository.findById.mockResolvedValue(order);
      mockUserRepository.findById.mockResolvedValue(user);
      mockOrderRepository.update.mockResolvedValue(updatedOrder);

      // Act
      const result = await orderService.updateOrder(orderId, updateData, userId);

      // Assert
      expect(mockOrderRepository.findById).toHaveBeenCalledWith(orderId);
      expect(mockOrderRepository.update).toHaveBeenCalledWith(orderId, expect.objectContaining(updateData));
      expect(result).toEqual(updatedOrder);
    });

    it('should allow admin to update any order', async () => {
      // Arrange
      const orderId = '456';
      const updateData = { notes: 'Updated notes' };
      const adminId = '789';
      const userId = '123';
      const order = testUtils.createTestOrder({ id: orderId, userId, status: OrderStatus.PENDING });
      const admin = testUtils.createTestAdmin({ id: adminId });
      const updatedOrder = { ...order, ...updateData };

      mockOrderRepository.findById.mockResolvedValue(order);
      mockUserRepository.findById.mockResolvedValue(admin);
      mockOrderRepository.update.mockResolvedValue(updatedOrder);

      // Act
      const result = await orderService.updateOrder(orderId, updateData, adminId);

      // Assert
      expect(result).toEqual(updatedOrder);
    });

    it('should throw AuthorizationException if non-owner tries to update', async () => {
      // Arrange
      const orderId = '456';
      const updateData = { notes: 'Updated notes' };
      const otherUserId = '789';
      const userId = '123';
      const order = testUtils.createTestOrder({ id: orderId, userId });
      const otherUser = testUtils.createTestUser({ id: otherUserId, role: UserRole.USER });

      mockOrderRepository.findById.mockResolvedValue(order);
      mockUserRepository.findById.mockResolvedValue(otherUser);

      // Act & Assert
      await expect(orderService.updateOrder(orderId, updateData, otherUserId)).rejects.toThrow(AuthorizationException);
      expect(mockOrderRepository.update).not.toHaveBeenCalled();
    });

    it('should throw ValidationException if order is not editable', async () => {
      // Arrange
      const orderId = '456';
      const updateData = { notes: 'Updated notes' };
      const userId = '123';
      const order = testUtils.createTestOrder({ 
        id: orderId, 
        userId, 
        status: OrderStatus.DELIVERED // Not editable
      });
      const user = testUtils.createTestUser({ id: userId });

      mockOrderRepository.findById.mockResolvedValue(order);
      mockUserRepository.findById.mockResolvedValue(user);

      // Act & Assert
      await expect(orderService.updateOrder(orderId, updateData, userId)).rejects.toThrow(ValidationException);
      expect(mockOrderRepository.update).not.toHaveBeenCalled();
    });
  });

  describe('cancelOrder', () => {
    it('should cancel order successfully by owner', async () => {
      // Arrange
      const orderId = '456';
      const reason = 'Changed mind';
      const userId = '123';
      const order = testUtils.createTestOrder({ 
        id: orderId, 
        userId, 
        status: OrderStatus.PENDING,
        items: [{ productId: 'prod1', quantity: 2 }]
      });
      const user = testUtils.createTestUser({ id: userId });
      const cancelledOrder = { ...order, status: OrderStatus.CANCELLED };

      mockOrderRepository.findById.mockResolvedValue(order);
      mockUserRepository.findById.mockResolvedValue(user);
      mockOrderRepository.cancel.mockResolvedValue(cancelledOrder);
      mockProductRepository.findById.mockResolvedValue(testUtils.createTestProduct());
      mockProductRepository.updateInventory.mockResolvedValue(true);

      // Act
      const result = await orderService.cancelOrder(orderId, reason, userId);

      // Assert
      expect(mockOrderRepository.cancel).toHaveBeenCalledWith(orderId, reason);
      expect(mockProductRepository.updateInventory).toHaveBeenCalled(); // Restore inventory
      expect(mockEmailService.sendNotificationEmail).toHaveBeenCalled();
      expect(result).toEqual(cancelledOrder);
    });

    it('should allow admin to cancel any order', async () => {
      // Arrange
      const orderId = '456';
      const reason = 'Admin cancellation';
      const adminId = '789';
      const userId = '123';
      const order = testUtils.createTestOrder({ 
        id: orderId, 
        userId, 
        status: OrderStatus.PENDING,
        items: []
      });
      const admin = testUtils.createTestAdmin({ id: adminId });
      const cancelledOrder = { ...order, status: OrderStatus.CANCELLED };

      mockOrderRepository.findById.mockResolvedValue(order);
      mockUserRepository.findById.mockResolvedValue(admin);
      mockOrderRepository.cancel.mockResolvedValue(cancelledOrder);

      // Act
      const result = await orderService.cancelOrder(orderId, reason, adminId);

      // Assert
      expect(result).toEqual(cancelledOrder);
    });

    it('should throw ValidationException if order cannot be cancelled', async () => {
      // Arrange
      const orderId = '456';
      const reason = 'Changed mind';
      const userId = '123';
      const order = testUtils.createTestOrder({ 
        id: orderId, 
        userId, 
        status: OrderStatus.DELIVERED // Cannot be cancelled
      });
      const user = testUtils.createTestUser({ id: userId });

      mockOrderRepository.findById.mockResolvedValue(order);
      mockUserRepository.findById.mockResolvedValue(user);

      // Act & Assert
      await expect(orderService.cancelOrder(orderId, reason, userId)).rejects.toThrow(ValidationException);
      expect(mockOrderRepository.cancel).not.toHaveBeenCalled();
    });
  });

  describe('updateOrderStatus', () => {
    it('should update order status successfully by admin', async () => {
      // Arrange
      const orderId = '456';
      const newStatus = OrderStatus.CONFIRMED;
      const adminId = '789';
      const order = testUtils.createTestOrder({ 
        id: orderId, 
        status: OrderStatus.PENDING,
        items: []
      });
      const admin = testUtils.createTestAdmin({ id: adminId });
      const updatedOrder = { ...order, status: newStatus };

      mockOrderRepository.findById.mockResolvedValue(order);
      mockUserRepository.findById.mockResolvedValue(admin);
      mockOrderRepository.updateStatus.mockResolvedValue(updatedOrder);

      // Act
      const result = await orderService.updateOrderStatus(orderId, newStatus, adminId);

      // Assert
      expect(mockOrderRepository.updateStatus).toHaveBeenCalledWith(orderId, newStatus);
      expect(mockEmailService.sendNotificationEmail).toHaveBeenCalled();
      expect(result).toEqual(updatedOrder);
    });

    it('should allow seller to update orders containing their products', async () => {
      // Arrange
      const orderId = '456';
      const newStatus = OrderStatus.PROCESSING;
      const sellerId = '789';
      const order = testUtils.createTestOrder({ 
        id: orderId, 
        status: OrderStatus.CONFIRMED,
        items: [{ sellerId, productId: 'prod1' }]
      });
      const seller = testUtils.createTestSeller({ id: sellerId });
      const updatedOrder = { ...order, status: newStatus };

      mockOrderRepository.findById.mockResolvedValue(order);
      mockUserRepository.findById.mockResolvedValue(seller);
      mockOrderRepository.updateStatus.mockResolvedValue(updatedOrder);

      // Act
      const result = await orderService.updateOrderStatus(orderId, newStatus, sellerId);

      // Assert
      expect(result).toEqual(updatedOrder);
    });

    it('should throw AuthorizationException if seller tries to update order without their products', async () => {
      // Arrange
      const orderId = '456';
      const newStatus = OrderStatus.PROCESSING;
      const sellerId = '789';
      const otherSellerId = '999';
      const order = testUtils.createTestOrder({ 
        id: orderId, 
        items: [{ sellerId: otherSellerId, productId: 'prod1' }] // Different seller
      });
      const seller = testUtils.createTestSeller({ id: sellerId });

      mockOrderRepository.findById.mockResolvedValue(order);
      mockUserRepository.findById.mockResolvedValue(seller);

      // Act & Assert
      await expect(orderService.updateOrderStatus(orderId, newStatus, sellerId)).rejects.toThrow(AuthorizationException);
      expect(mockOrderRepository.updateStatus).not.toHaveBeenCalled();
    });
  });

  describe('updatePaymentStatus', () => {
    it('should update payment status successfully by admin', async () => {
      // Arrange
      const orderId = '456';
      const paymentStatus = PaymentStatus.COMPLETED;
      const adminId = '789';
      const admin = testUtils.createTestAdmin({ id: adminId });
      const order = testUtils.createTestOrder({ 
        id: orderId, 
        status: OrderStatus.PENDING,
        paymentStatus: PaymentStatus.PENDING
      });
      const updatedOrder = { ...order, paymentStatus };

      mockUserRepository.findById.mockResolvedValue(admin);
      mockOrderRepository.updatePaymentStatus.mockResolvedValue(updatedOrder);
      mockOrderRepository.updateStatus.mockResolvedValue(updatedOrder);

      // Act
      const result = await orderService.updatePaymentStatus(orderId, paymentStatus, adminId);

      // Assert
      expect(mockOrderRepository.updatePaymentStatus).toHaveBeenCalledWith(orderId, paymentStatus);
      expect(result).toEqual(updatedOrder);
    });

    it('should throw AuthorizationException if non-admin tries to update payment status', async () => {
      // Arrange
      const orderId = '456';
      const paymentStatus = PaymentStatus.COMPLETED;
      const userId = '123';
      const user = testUtils.createTestUser({ id: userId, role: UserRole.USER });

      mockUserRepository.findById.mockResolvedValue(user);

      // Act & Assert
      await expect(orderService.updatePaymentStatus(orderId, paymentStatus, userId)).rejects.toThrow(AuthorizationException);
      expect(mockOrderRepository.updatePaymentStatus).not.toHaveBeenCalled();
    });
  });

  describe('getOrderById', () => {
    it('should return order by ID', async () => {
      // Arrange
      const orderId = '456';
      const order = testUtils.createTestOrder({ id: orderId });

      mockOrderRepository.findById.mockResolvedValue(order);

      // Act
      const result = await orderService.getOrderById(orderId);

      // Assert
      expect(mockOrderRepository.findById).toHaveBeenCalledWith(orderId);
      expect(result).toEqual(order);
    });

    it('should throw ValidationException if order not found', async () => {
      // Arrange
      const orderId = 'nonexistent';

      mockOrderRepository.findById.mockResolvedValue(null);

      // Act & Assert
      await expect(orderService.getOrderById(orderId)).rejects.toThrow(ValidationException);
    });
  });

  describe('getUserOrders', () => {
    it('should return user orders', async () => {
      // Arrange
      const userId = '123';
      const options = { page: 1, limit: 10 };
      const orders = [testUtils.createTestOrder({ userId })];
      const result = {
        orders,
        pagination: { page: 1, limit: 10, total: 1, pages: 1 }
      };

      mockOrderRepository.findByUser.mockResolvedValue(result);

      // Act
      const response = await orderService.getUserOrders(userId, options);

      // Assert
      expect(mockOrderRepository.findByUser).toHaveBeenCalledWith(userId, options);
      expect(response).toEqual(result);
    });
  });

  describe('getSellerOrders', () => {
    it('should return seller orders', async () => {
      // Arrange
      const sellerId = '123';
      const options = { page: 1, limit: 10 };
      const orders = [testUtils.createTestOrder({ items: [{ sellerId }] })];
      const result = {
        orders,
        pagination: { page: 1, limit: 10, total: 1, pages: 1 }
      };

      mockOrderRepository.findBySeller.mockResolvedValue(result);

      // Act
      const response = await orderService.getSellerOrders(sellerId, options);

      // Assert
      expect(mockOrderRepository.findBySeller).toHaveBeenCalledWith(sellerId, options);
      expect(response).toEqual(result);
    });
  });

  describe('searchOrders', () => {
    it('should search orders successfully', async () => {
      // Arrange
      const query = 'ORD-123';
      const options = { page: 1, limit: 10 };
      const orders = [testUtils.createTestOrder({ orderNumber: 'ORD-123456' })];
      const result = {
        orders,
        pagination: { page: 1, limit: 10, total: 1, pages: 1 }
      };

      mockOrderRepository.search.mockResolvedValue(result);

      // Act
      const response = await orderService.searchOrders(query, options);

      // Assert
      expect(mockOrderRepository.search).toHaveBeenCalledWith(query, options);
      expect(response).toEqual(result);
    });
  });

  describe('getOrderCounts', () => {
    it('should return order counts by status', async () => {
      // Arrange
      const userId = '123';
      const counts = {
        pending: 5,
        confirmed: 3,
        delivered: 10
      };

      mockOrderRepository.getCountByStatus.mockResolvedValue(counts);

      // Act
      const result = await orderService.getOrderCounts(userId);

      // Assert
      expect(mockOrderRepository.getCountByStatus).toHaveBeenCalledWith(userId);
      expect(result).toEqual(counts);
    });
  });

  describe('getRevenueStatistics', () => {
    it('should return revenue statistics', async () => {
      // Arrange
      const options = { startDate: new Date(), endDate: new Date() };
      const statistics = {
        totalRevenue: 1000,
        totalOrders: 50,
        averageOrderValue: 20
      };

      mockOrderRepository.getRevenueStatistics.mockResolvedValue(statistics);

      // Act
      const result = await orderService.getRevenueStatistics(options);

      // Assert
      expect(mockOrderRepository.getRevenueStatistics).toHaveBeenCalledWith(options);
      expect(result).toEqual(statistics);
    });
  });
});
