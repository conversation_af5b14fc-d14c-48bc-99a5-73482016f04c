/**
 * Auth Controller
 * Handles authentication and authorization
 */

const { HttpStatus } = require('../../shared/enums');
const { ValidationException, AuthorizationException } = require('../../core/exceptions');

class AuthController {
  constructor(container) {
    this.container = container;
    this.userService = container.resolve('userService');
    this.tokenService = container.resolve('tokenService');
  }

  /**
   * User login
   */
  async login(req, res) {
    try {
      console.log('🔐 Login attempt:', { email: req.body.email, hasPassword: !!req.body.password });

      const { email, password } = req.body;

      if (!email || !password) {
        console.log('❌ Missing credentials');
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          message: 'Email and password are required',
          timestamp: new Date().toISOString()
        });
      }

      console.log('🔍 Authenticating user...');
      // Authenticate user
      const result = await this.userService.authenticateUser(email, password);
      console.log('✅ Authentication result:', result);
      console.log('✅ User object:', result.user);
      console.log('✅ User keys:', Object.keys(result.user || {}));

      const user = result.user;

      // Generate token
      const token = this.tokenService.generateAccessToken({
        id: user.id,
        email: user.email,
        role: user.role,
        permissions: user.permissions || []
      });

      // Return success response
      res.status(HttpStatus.OK).json({
        success: true,
        data: {
          user: {
            id: user.id,
            email: user.email,
            username: user.username,
            firstName: user.firstName,
            lastName: user.lastName,
            role: user.role,
            permissions: user.permissions || [],
            isActive: user.isActive
          },
          token,
          expiresIn: '24h'
        },
        message: 'Login successful',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('❌ Login error:', error.message);
      console.error('Error type:', error.constructor.name);
      console.error('Full error:', error);

      if (error instanceof ValidationException || error instanceof AuthorizationException) {
        console.log('🚫 Authentication/Validation error');
        return res.status(HttpStatus.UNAUTHORIZED).json({
          success: false,
          message: error.message,
          timestamp: new Date().toISOString()
        });
      }

      console.log('💥 Internal server error');
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: 'Login failed',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * User registration
   */
  async register(req, res) {
    try {
      const userData = req.body;

      // Validate required fields
      if (!userData.email || !userData.password || !userData.username) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          message: 'Email, password, and username are required',
          timestamp: new Date().toISOString()
        });
      }

      // Register user
      const user = await this.userService.registerUser(userData);

      // Generate token
      const token = this.tokenService.generateAccessToken({
        id: user.id,
        email: user.email,
        role: user.role,
        permissions: user.permissions || []
      });

      // Return success response
      res.status(HttpStatus.CREATED).json({
        success: true,
        data: {
          user: {
            id: user.id,
            email: user.email,
            username: user.username,
            firstName: user.firstName,
            lastName: user.lastName,
            role: user.role,
            permissions: user.permissions || [],
            isActive: user.isActive
          },
          token,
          expiresIn: '24h'
        },
        message: 'Registration successful',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Registration error:', error);

      if (error instanceof ValidationException) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          message: error.message,
          timestamp: new Date().toISOString()
        });
      }

      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: 'Registration failed',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Get current user profile
   */
  async getProfile(req, res) {
    try {
      const userId = req.user?.id;

      if (!userId) {
        return res.status(HttpStatus.UNAUTHORIZED).json({
          success: false,
          message: 'User not authenticated',
          timestamp: new Date().toISOString()
        });
      }

      const user = await this.userService.getUserById(userId);

      res.status(HttpStatus.OK).json({
        success: true,
        data: {
          id: user.id,
          email: user.email,
          username: user.username,
          firstName: user.firstName,
          lastName: user.lastName,
          role: user.role,
          permissions: user.permissions || [],
          isActive: user.isActive,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Get profile error:', error);

      if (error instanceof ValidationException) {
        return res.status(HttpStatus.NOT_FOUND).json({
          success: false,
          message: error.message,
          timestamp: new Date().toISOString()
        });
      }

      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: 'Failed to get profile',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(req, res) {
    try {
      const userId = req.user?.id;
      const updateData = req.body;

      if (!userId) {
        return res.status(HttpStatus.UNAUTHORIZED).json({
          success: false,
          message: 'User not authenticated',
          timestamp: new Date().toISOString()
        });
      }

      // Remove sensitive fields that shouldn't be updated via this endpoint
      delete updateData.password;
      delete updateData.role;
      delete updateData.permissions;
      delete updateData.isActive;

      const updatedUser = await this.userService.updateUser(userId, updateData, userId);

      res.status(HttpStatus.OK).json({
        success: true,
        data: {
          id: updatedUser.id,
          email: updatedUser.email,
          username: updatedUser.username,
          firstName: updatedUser.firstName,
          lastName: updatedUser.lastName,
          role: updatedUser.role,
          permissions: updatedUser.permissions || [],
          isActive: updatedUser.isActive,
          updatedAt: updatedUser.updatedAt
        },
        message: 'Profile updated successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Update profile error:', error);

      if (error instanceof ValidationException || error instanceof AuthorizationException) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          message: error.message,
          timestamp: new Date().toISOString()
        });
      }

      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: 'Failed to update profile',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Change password
   */
  async changePassword(req, res) {
    try {
      const userId = req.user?.id;
      const { currentPassword, newPassword } = req.body;

      if (!userId) {
        return res.status(HttpStatus.UNAUTHORIZED).json({
          success: false,
          message: 'User not authenticated',
          timestamp: new Date().toISOString()
        });
      }

      if (!currentPassword || !newPassword) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          message: 'Current password and new password are required',
          timestamp: new Date().toISOString()
        });
      }

      await this.userService.changePassword(userId, currentPassword, newPassword);

      res.status(HttpStatus.OK).json({
        success: true,
        message: 'Password changed successfully',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Change password error:', error);

      if (error instanceof ValidationException || error instanceof AuthorizationException) {
        return res.status(HttpStatus.BAD_REQUEST).json({
          success: false,
          message: error.message,
          timestamp: new Date().toISOString()
        });
      }

      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: 'Failed to change password',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Logout (client-side token invalidation)
   */
  async logout(req, res) {
    try {
      // In a stateless JWT system, logout is typically handled client-side
      // by removing the token from storage
      res.status(HttpStatus.OK).json({
        success: true,
        message: 'Logout successful',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Logout error:', error);
      res.status(HttpStatus.INTERNAL_SERVER_ERROR).json({
        success: false,
        message: 'Logout failed',
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Verify token
   */
  async verifyToken(req, res) {
    try {
      const user = req.user;

      if (!user) {
        return res.status(HttpStatus.UNAUTHORIZED).json({
          success: false,
          message: 'Invalid token',
          timestamp: new Date().toISOString()
        });
      }

      res.status(HttpStatus.OK).json({
        success: true,
        data: {
          id: user.id,
          email: user.email,
          username: user.username,
          role: user.role,
          permissions: user.permissions || [],
          isActive: user.isActive
        },
        message: 'Token is valid',
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Verify token error:', error);
      res.status(HttpStatus.UNAUTHORIZED).json({
        success: false,
        message: 'Token verification failed',
        timestamp: new Date().toISOString()
      });
    }
  }
}

module.exports = AuthController;
