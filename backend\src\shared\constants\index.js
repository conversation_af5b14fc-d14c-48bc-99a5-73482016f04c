/**
 * Application Constants
 * Uygulama genelinde kull<PERSON>lan sabitler
 */

// Veritabanı Sabitleri
const DATABASE = {
  CONNECTION_TIMEOUT: 30000,
  MAX_POOL_SIZE: 10,
  MIN_POOL_SIZE: 5
};

// JWT Sabitleri
const JWT = {
  ACCESS_TOKEN_EXPIRY: '24h',
  REFRESH_TOKEN_EXPIRY: '7d',
  ALGORITHM: 'HS256'
};

// Pagination Sabitleri
const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 10,
  MAX_LIMIT: 100
};

// Dosya Upload Sabitleri
const FILE_UPLOAD = {
  MAX_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/webp'],
  UPLOAD_PATH: 'uploads/'
};

// Cache Sabitleri
const CACHE = {
  DEFAULT_TTL: 3600, // 1 saat
  PRODUCTS_TTL: 1800, // 30 dakika
  CATEGORIES_TTL: 7200, // 2 saat
  USER_SESSION_TTL: 86400 // 24 saat
};

// Rate Limiting Sabitleri
const RATE_LIMIT = {
  WINDOW_MS: 15 * 60 * 1000, // 15 dakika
  MAX_REQUESTS: 100,
  AUTH_MAX_REQUESTS: 5 // Login/Register için
};

// Açık Artırma Sabitleri
const AUCTION = {
  MIN_BID_INCREMENT: 1,
  MAX_DURATION_DAYS: 30,
  MIN_DURATION_HOURS: 1,
  AUTO_EXTEND_MINUTES: 5
};

// Çekiliş Sabitleri
const LOTTERY = {
  MIN_NUMBERS: 12,
  MAX_NUMBERS: 12,
  NUMBER_RANGE_MIN: 100,
  NUMBER_RANGE_MAX: 999,
  MIN_DIFFERENCE: 4,
  MAX_TICKETS_PER_USER: 10
};

// Ürün Sabitleri
const PRODUCT = {
  MIN_PRICE: 0.01,
  MAX_PRICE: 99999999.99, // 100 milyon'a çıkarıldı
  MAX_IMAGES: 10,
  MIN_STOCK: 0,
  MAX_STOCK: 99999
};

// Sipariş Sabitleri
const ORDER = {
  MIN_TOTAL: 0.01,
  MAX_TOTAL: 999999.99,
  CANCELLATION_WINDOW_HOURS: 24
};

// Email Sabitleri
const EMAIL = {
  FROM_ADDRESS: '<EMAIL>',
  TEMPLATES: {
    WELCOME: 'welcome',
    ORDER_CONFIRMATION: 'order_confirmation',
    PASSWORD_RESET: 'password_reset',
    AUCTION_WON: 'auction_won',
    LOTTERY_WON: 'lottery_won'
  }
};

// Validation Sabitleri
const VALIDATION = {
  PASSWORD_MIN_LENGTH: 6,
  PASSWORD_MAX_LENGTH: 128,
  USERNAME_MIN_LENGTH: 3,
  USERNAME_MAX_LENGTH: 30,
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 50,
  DESCRIPTION_MAX_LENGTH: 1000,
  TITLE_MAX_LENGTH: 200
};

// API Sabitleri
const API = {
  VERSION: 'v1',
  BASE_PATH: '/api',
  TIMEOUT: 30000
};

// Logging Sabitleri
const LOGGING = {
  LEVELS: {
    ERROR: 'error',
    WARN: 'warn',
    INFO: 'info',
    DEBUG: 'debug'
  },
  MAX_FILE_SIZE: '20m',
  MAX_FILES: 5
};

// Security Sabitleri
const SECURITY = {
  BCRYPT_ROUNDS: 12,
  SESSION_SECRET_LENGTH: 64,
  CSRF_TOKEN_LENGTH: 32
};

module.exports = {
  DATABASE,
  JWT,
  PAGINATION,
  FILE_UPLOAD,
  CACHE,
  RATE_LIMIT,
  AUCTION,
  LOTTERY,
  PRODUCT,
  ORDER,
  EMAIL,
  VALIDATION,
  API,
  LOGGING,
  SECURITY
};
